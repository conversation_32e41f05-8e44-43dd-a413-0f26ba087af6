"""
PPO损失函数实现

实现PPO的裁剪损失函数、价值损失和熵正则化项
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List
from utils.math_utils import safe_mean, clip_gradients


class PPOLoss:
    """PPO损失函数"""
    
    def __init__(self, 
                 clip_param: float = 0.2,
                 vf_loss_coeff: float = 0.5,
                 entropy_coeff: float = 0.01,
                 max_grad_norm: float = 0.5,
                 use_clipped_value_loss: bool = True,
                 use_huber_loss: bool = False,
                 huber_delta: float = 1.0):
        """
        初始化PPO损失函数
        
        Args:
            clip_param: PPO裁剪参数ε
            vf_loss_coeff: 价值函数损失系数
            entropy_coeff: 熵正则化系数
            max_grad_norm: 最大梯度范数
            use_clipped_value_loss: 是否使用裁剪价值损失
            use_huber_loss: 是否使用Huber损失
            huber_delta: Huber损失的δ参数
        """
        self.clip_param = clip_param
        self.vf_loss_coeff = vf_loss_coeff
        self.entropy_coeff = entropy_coeff
        self.max_grad_norm = max_grad_norm
        self.use_clipped_value_loss = use_clipped_value_loss
        self.use_huber_loss = use_huber_loss
        self.huber_delta = huber_delta
        
        # 统计信息
        self.stats = {
            'policy_loss': 0.0,
            'value_loss': 0.0,
            'entropy_loss': 0.0,
            'total_loss': 0.0,
            'approx_kl': 0.0,
            'clip_fraction': 0.0,
            'explained_variance': 0.0
        }
    
    def compute_policy_loss(self,
                          log_probs: torch.Tensor,
                          old_log_probs: torch.Tensor,
                          advantages: torch.Tensor,
                          action_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算PPO策略损失 - 修复版本

        Args:
            log_probs: 新策略的对数概率 [batch_size, num_agents] 或 [batch_size]
            old_log_probs: 旧策略的对数概率 [batch_size, num_agents] 或 [batch_size]
            advantages: 优势函数 [batch_size, num_agents] 或 [batch_size]
            action_mask: 动作掩码 [batch_size, num_agents, action_dim] 或 [batch_size, action_dim]

        Returns:
            policy_loss: 策略损失
            stats: 统计信息
        """
        # 数值稳定性检查
        if torch.any(torch.isnan(log_probs)) or torch.any(torch.isinf(log_probs)):
            print("警告: log_probs包含NaN或Inf值，进行修复")
            log_probs = torch.clamp(log_probs, min=-10, max=10)

        if torch.any(torch.isnan(old_log_probs)) or torch.any(torch.isinf(old_log_probs)):
            print("警告: old_log_probs包含NaN或Inf值，进行修复")
            old_log_probs = torch.clamp(old_log_probs, min=-10, max=10)

        if torch.any(torch.isnan(advantages)) or torch.any(torch.isinf(advantages)):
            print("警告: advantages包含NaN或Inf值，进行修复")
            advantages = torch.clamp(advantages, min=-10, max=10)

        # 计算重要性采样比率，添加数值稳定性
        log_ratio = log_probs - old_log_probs
        log_ratio = torch.clamp(log_ratio, min=-10, max=10)  # 防止exp溢出
        ratio = torch.exp(log_ratio)

        # 额外的比率裁剪以防止极端值
        ratio = torch.clamp(ratio, min=0.1, max=10.0)

        # 计算裁剪的目标函数
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1.0 - self.clip_param, 1.0 + self.clip_param) * advantages

        # PPO损失（取最小值）
        policy_loss = -torch.min(surr1, surr2)

        # 检查损失有效性
        if torch.any(torch.isnan(policy_loss)) or torch.any(torch.isinf(policy_loss)):
            print("警告: policy_loss包含NaN或Inf值，使用备用计算")
            # 使用简化的策略损失
            policy_loss = -advantages * 0.1  # 小的固定损失

        # 应用动作掩码（如果提供）
        if action_mask is not None:
            # 这里假设action_mask已经在计算log_probs时应用了
            pass

        # 计算统计信息
        with torch.no_grad():
            # 近似KL散度
            approx_kl = safe_mean(torch.abs(old_log_probs - log_probs))

            # 裁剪比例
            clipped = torch.abs(ratio - 1.0) > self.clip_param
            clip_fraction = safe_mean(clipped.float())

            # 确保统计值有效
            approx_kl_val = approx_kl.item() if not torch.isnan(approx_kl) else 0.0
            clip_fraction_val = clip_fraction.item() if not torch.isnan(clip_fraction) else 0.0
            policy_loss_val = safe_mean(policy_loss).item() if not torch.isnan(safe_mean(policy_loss)) else 0.0

            stats = {
                'approx_kl': approx_kl_val,
                'clip_fraction': clip_fraction_val,
                'policy_loss': policy_loss_val
            }

        return safe_mean(policy_loss), stats
    
    def compute_value_loss(self, 
                         values: torch.Tensor,
                         old_values: torch.Tensor,
                         returns: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算价值函数损失
        
        Args:
            values: 新价值估计 [batch_size, num_agents] 或 [batch_size]
            old_values: 旧价值估计 [batch_size, num_agents] 或 [batch_size]
            returns: 回报 [batch_size, num_agents] 或 [batch_size]
            
        Returns:
            value_loss: 价值损失
            stats: 统计信息
        """
        if self.use_clipped_value_loss:
            # 裁剪价值损失
            value_pred_clipped = old_values + torch.clamp(
                values - old_values, -self.clip_param, self.clip_param
            )
            
            if self.use_huber_loss:
                value_losses = F.smooth_l1_loss(values, returns, reduction='none', beta=self.huber_delta)
                value_losses_clipped = F.smooth_l1_loss(value_pred_clipped, returns, reduction='none', beta=self.huber_delta)
            else:
                value_losses = F.mse_loss(values, returns, reduction='none')
                value_losses_clipped = F.mse_loss(value_pred_clipped, returns, reduction='none')
            
            value_loss = torch.max(value_losses, value_losses_clipped)
        else:
            # 标准价值损失
            if self.use_huber_loss:
                value_loss = F.smooth_l1_loss(values, returns, reduction='none', beta=self.huber_delta)
            else:
                value_loss = F.mse_loss(values, returns, reduction='none')
        
        # 计算统计信息
        with torch.no_grad():
            # 解释方差
            y_true = returns.flatten()
            y_pred = values.flatten()
            
            var_y = torch.var(y_true)
            explained_var = 1 - torch.var(y_true - y_pred) / (var_y + 1e-8)
            
            stats = {
                'value_loss': safe_mean(value_loss).item(),
                'explained_variance': explained_var.item()
            }
        
        return safe_mean(value_loss), stats
    
    def compute_entropy_loss(self, entropy: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算熵损失 - 修复版本

        Args:
            entropy: 策略熵 [batch_size, num_agents] 或 [batch_size]

        Returns:
            entropy_loss: 熵损失（负熵，用于最大化熵）
            stats: 统计信息
        """
        # 数值稳定性检查
        if torch.any(torch.isnan(entropy)) or torch.any(torch.isinf(entropy)):
            print("警告: entropy包含NaN或Inf值，进行修复")
            entropy = torch.clamp(entropy, min=0.0, max=10.0)

        # 如果熵过小，添加一个小的基础熵以确保探索
        if safe_mean(entropy).item() < 1e-6:
            print("警告: 熵过小，添加基础熵以促进探索")
            entropy = entropy + 0.01  # 添加小的基础熵

        entropy_loss = -safe_mean(entropy)

        # 确保熵损失有效
        if torch.isnan(entropy_loss) or torch.isinf(entropy_loss):
            print("警告: entropy_loss无效，使用默认值")
            entropy_loss = torch.tensor(-0.01, device=entropy.device, requires_grad=True)

        # 计算统计信息
        entropy_mean = safe_mean(entropy)
        entropy_val = entropy_mean.item() if not torch.isnan(entropy_mean) else 0.01
        entropy_loss_val = entropy_loss.item() if not torch.isnan(entropy_loss) else -0.01

        stats = {
            'entropy': entropy_val,
            'entropy_loss': entropy_loss_val
        }

        return entropy_loss, stats
    
    def compute_total_loss(self, 
                         log_probs: torch.Tensor,
                         old_log_probs: torch.Tensor,
                         values: torch.Tensor,
                         old_values: torch.Tensor,
                         returns: torch.Tensor,
                         advantages: torch.Tensor,
                         entropy: torch.Tensor,
                         action_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算总损失
        
        Args:
            log_probs: 新策略的对数概率
            old_log_probs: 旧策略的对数概率
            values: 新价值估计
            old_values: 旧价值估计
            returns: 回报
            advantages: 优势函数
            entropy: 策略熵
            action_mask: 动作掩码
            
        Returns:
            total_loss: 总损失
            stats: 详细统计信息
        """
        # 计算各项损失
        policy_loss, policy_stats = self.compute_policy_loss(
            log_probs, old_log_probs, advantages, action_mask
        )
        
        value_loss, value_stats = self.compute_value_loss(
            values, old_values, returns
        )
        
        entropy_loss, entropy_stats = self.compute_entropy_loss(entropy)
        
        # 总损失
        total_loss = (policy_loss + 
                     self.vf_loss_coeff * value_loss + 
                     self.entropy_coeff * entropy_loss)
        
        # 合并统计信息
        stats = {
            **policy_stats,
            **value_stats,
            **entropy_stats,
            'total_loss': total_loss.item()
        }
        
        # 更新内部统计
        self.stats.update(stats)
        
        return total_loss, stats
    
    def update_parameters(self, 
                        clip_param: Optional[float] = None,
                        vf_loss_coeff: Optional[float] = None,
                        entropy_coeff: Optional[float] = None):
        """更新损失函数参数"""
        if clip_param is not None:
            self.clip_param = clip_param
        if vf_loss_coeff is not None:
            self.vf_loss_coeff = vf_loss_coeff
        if entropy_coeff is not None:
            self.entropy_coeff = entropy_coeff
    
    def get_stats(self) -> Dict[str, float]:
        """获取统计信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        for key in self.stats:
            self.stats[key] = 0.0


class MultiAgentPPOLoss:
    """多智能体PPO损失函数"""
    
    def __init__(self, 
                 num_agents: int,
                 clip_param: float = 0.2,
                 vf_loss_coeff: float = 0.5,
                 entropy_coeff: float = 0.01,
                 max_grad_norm: float = 0.5,
                 use_clipped_value_loss: bool = True,
                 centralized_value: bool = True,
                 individual_entropy_coeff: bool = False):
        """
        初始化多智能体PPO损失函数
        
        Args:
            num_agents: 智能体数量
            clip_param: PPO裁剪参数
            vf_loss_coeff: 价值函数损失系数
            entropy_coeff: 熵正则化系数
            max_grad_norm: 最大梯度范数
            use_clipped_value_loss: 是否使用裁剪价值损失
            centralized_value: 是否使用中心化价值函数
            individual_entropy_coeff: 是否对每个智能体使用不同的熵系数
        """
        self.num_agents = num_agents
        self.centralized_value = centralized_value
        self.individual_entropy_coeff = individual_entropy_coeff
        
        if individual_entropy_coeff:
            # 每个智能体独立的损失函数
            self.ppo_losses = [
                PPOLoss(clip_param, vf_loss_coeff, entropy_coeff, max_grad_norm, use_clipped_value_loss)
                for _ in range(num_agents)
            ]
        else:
            # 共享的损失函数
            self.ppo_loss = PPOLoss(clip_param, vf_loss_coeff, entropy_coeff, max_grad_norm, use_clipped_value_loss)
    
    def compute_total_loss(self, 
                         log_probs: torch.Tensor,
                         old_log_probs: torch.Tensor,
                         values: torch.Tensor,
                         old_values: torch.Tensor,
                         returns: torch.Tensor,
                         advantages: torch.Tensor,
                         entropy: torch.Tensor,
                         action_masks: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算多智能体总损失
        
        Args:
            log_probs: [batch_size, num_agents]
            old_log_probs: [batch_size, num_agents]
            values: [batch_size, num_agents] 或 [batch_size] (中心化)
            old_values: [batch_size, num_agents] 或 [batch_size] (中心化)
            returns: [batch_size, num_agents] 或 [batch_size] (中心化)
            advantages: [batch_size, num_agents]
            entropy: [batch_size, num_agents]
            action_masks: [batch_size, num_agents, action_dim]
            
        Returns:
            total_loss: 总损失
            stats: 统计信息
        """
        if self.individual_entropy_coeff:
            # 每个智能体独立计算损失
            total_loss = 0.0
            all_stats = {}
            
            for agent_idx in range(self.num_agents):
                agent_log_probs = log_probs[:, agent_idx]
                agent_old_log_probs = old_log_probs[:, agent_idx]
                agent_advantages = advantages[:, agent_idx]
                agent_entropy = entropy[:, agent_idx]
                
                # 处理价值函数
                if values.dim() == 2:  # 分布式价值
                    agent_values = values[:, agent_idx]
                    agent_old_values = old_values[:, agent_idx]
                    agent_returns = returns[:, agent_idx]
                else:  # 中心化价值
                    agent_values = values
                    agent_old_values = old_values
                    agent_returns = returns
                
                # 处理动作掩码
                agent_action_mask = action_masks[:, agent_idx, :] if action_masks is not None else None
                
                agent_loss, agent_stats = self.ppo_losses[agent_idx].compute_total_loss(
                    agent_log_probs, agent_old_log_probs,
                    agent_values, agent_old_values, agent_returns,
                    agent_advantages, agent_entropy, agent_action_mask
                )
                
                total_loss += agent_loss
                
                # 添加智能体前缀到统计信息
                for key, value in agent_stats.items():
                    all_stats[f'agent_{agent_idx}_{key}'] = value
            
            # 平均损失
            total_loss = total_loss / self.num_agents
            
            # 计算平均统计信息
            avg_stats = {}
            for key in ['policy_loss', 'value_loss', 'entropy_loss', 'total_loss', 'approx_kl', 'clip_fraction']:
                values = [all_stats.get(f'agent_{i}_{key}', 0.0) for i in range(self.num_agents)]
                avg_stats[f'avg_{key}'] = np.mean(values)
            
            all_stats.update(avg_stats)
            
        else:
            # 共享损失函数
            # 展平多智能体维度
            flat_log_probs = log_probs.flatten()
            flat_old_log_probs = old_log_probs.flatten()
            flat_advantages = advantages.flatten()
            flat_entropy = entropy.flatten()
            
            if values.dim() == 2:  # 分布式价值
                flat_values = values.flatten()
                flat_old_values = old_values.flatten()
                flat_returns = returns.flatten()
            else:  # 中心化价值
                # 重复中心化价值以匹配智能体数量
                flat_values = values.repeat_interleave(self.num_agents)
                flat_old_values = old_values.repeat_interleave(self.num_agents)
                flat_returns = returns.repeat_interleave(self.num_agents)
            
            total_loss, all_stats = self.ppo_loss.compute_total_loss(
                flat_log_probs, flat_old_log_probs,
                flat_values, flat_old_values, flat_returns,
                flat_advantages, flat_entropy
            )
        
        return total_loss, all_stats
    
    def get_stats(self) -> Dict[str, float]:
        """获取统计信息"""
        if self.individual_entropy_coeff:
            all_stats = {}
            for i, ppo_loss in enumerate(self.ppo_losses):
                agent_stats = ppo_loss.get_stats()
                for key, value in agent_stats.items():
                    all_stats[f'agent_{i}_{key}'] = value
            return all_stats
        else:
            return self.ppo_loss.get_stats()


class AdaptivePPOLoss(PPOLoss):
    """自适应PPO损失函数"""
    
    def __init__(self, 
                 clip_param: float = 0.2,
                 vf_loss_coeff: float = 0.5,
                 entropy_coeff: float = 0.01,
                 max_grad_norm: float = 0.5,
                 adaptive_clip: bool = True,
                 target_kl: float = 0.01,
                 clip_decay: float = 0.999):
        """
        初始化自适应PPO损失函数
        
        Args:
            clip_param: 初始PPO裁剪参数
            vf_loss_coeff: 价值函数损失系数
            entropy_coeff: 熵正则化系数
            max_grad_norm: 最大梯度范数
            adaptive_clip: 是否自适应调整裁剪参数
            target_kl: 目标KL散度
            clip_decay: 裁剪参数衰减率
        """
        super().__init__(clip_param, vf_loss_coeff, entropy_coeff, max_grad_norm)
        
        self.adaptive_clip = adaptive_clip
        self.target_kl = target_kl
        self.clip_decay = clip_decay
        self.initial_clip_param = clip_param
        
        # 自适应统计
        self.kl_history = []
    
    def compute_total_loss(self, *args, **kwargs) -> Tuple[torch.Tensor, Dict[str, float]]:
        """计算自适应总损失"""
        total_loss, stats = super().compute_total_loss(*args, **kwargs)
        
        # 自适应调整裁剪参数
        if self.adaptive_clip:
            self._update_clip_param(stats['approx_kl'])
        
        return total_loss, stats
    
    def _update_clip_param(self, current_kl: float):
        """自适应更新裁剪参数"""
        self.kl_history.append(current_kl)
        
        # 保持历史长度
        if len(self.kl_history) > 50:
            self.kl_history.pop(0)
        
        # 根据KL散度调整裁剪参数
        if len(self.kl_history) > 10:
            recent_kl = np.mean(self.kl_history[-10:])
            
            if recent_kl > 2 * self.target_kl:
                # KL散度过大，减小裁剪参数
                self.clip_param *= self.clip_decay
            elif recent_kl < 0.5 * self.target_kl:
                # KL散度过小，增大裁剪参数
                self.clip_param /= self.clip_decay
            
            # 限制裁剪参数范围
            self.clip_param = np.clip(self.clip_param, 0.05, 0.5)
