"""
数学工具函数

提供常用的数学计算和工具函数
"""

import numpy as np
import torch
import torch.nn.functional as F
from typing import Tuple, List, Union, Optional
import math


def manhattan_distance(pos1: Tuple[int, int], pos2: Tuple[int, int]) -> int:
    """计算曼哈顿距离"""
    return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])


def euclidean_distance(pos1: Tuple[float, float], pos2: Tuple[float, float]) -> float:
    """计算欧几里得距离"""
    return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)


def normalize_coordinates(x: int, y: int, max_x: int, max_y: int) -> Tuple[float, float]:
    """归一化坐标到[0,1]范围"""
    return x / max_x, y / max_y


def denormalize_coordinates(x_norm: float, y_norm: float, max_x: int, max_y: int) -> Tuple[int, int]:
    """反归一化坐标"""
    return int(x_norm * max_x), int(y_norm * max_y)


def calculate_angle(pos1: Tuple[float, float], pos2: Tuple[float, float]) -> float:
    """计算两点间的角度（弧度）"""
    dx = pos2[0] - pos1[0]
    dy = pos2[1] - pos1[1]
    return math.atan2(dy, dx)


def rotate_point(point: Tuple[float, float], angle: float, center: Tuple[float, float] = (0, 0)) -> Tuple[float, float]:
    """绕中心点旋转点"""
    cos_angle = math.cos(angle)
    sin_angle = math.sin(angle)
    
    # 平移到原点
    x = point[0] - center[0]
    y = point[1] - center[1]
    
    # 旋转
    new_x = x * cos_angle - y * sin_angle
    new_y = x * sin_angle + y * cos_angle
    
    # 平移回去
    return new_x + center[0], new_y + center[1]


def softmax_with_temperature(logits: torch.Tensor, temperature: float = 1.0, dim: int = -1) -> torch.Tensor:
    """带温度参数的softmax"""
    return F.softmax(logits / temperature, dim=dim)


def gumbel_softmax(logits: torch.Tensor, temperature: float = 1.0, hard: bool = False, dim: int = -1) -> torch.Tensor:
    """Gumbel Softmax采样"""
    gumbel_noise = -torch.log(-torch.log(torch.rand_like(logits) + 1e-8) + 1e-8)
    y = F.softmax((logits + gumbel_noise) / temperature, dim=dim)
    
    if hard:
        # 硬采样：one-hot
        y_hard = torch.zeros_like(y)
        y_hard.scatter_(dim, y.argmax(dim=dim, keepdim=True), 1.0)
        y = (y_hard - y).detach() + y
    
    return y


def top_k_mask(tensor: torch.Tensor, k: int, dim: int = -1) -> torch.Tensor:
    """创建Top-K掩码"""
    if k >= tensor.size(dim):
        return torch.ones_like(tensor, dtype=torch.bool)
    
    topk_values, topk_indices = torch.topk(tensor, k, dim=dim)
    mask = torch.zeros_like(tensor, dtype=torch.bool)
    mask.scatter_(dim, topk_indices, True)
    
    return mask


def apply_mask(tensor: torch.Tensor, mask: torch.Tensor, fill_value: float = -float('inf')) -> torch.Tensor:
    """应用掩码"""
    return tensor.masked_fill(~mask, fill_value)


def positional_encoding(position: torch.Tensor, d_model: int, max_len: int = 10000) -> torch.Tensor:
    """位置编码"""
    pe = torch.zeros(position.size(0), d_model)
    
    div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                        -(math.log(max_len) / d_model))
    
    pe[:, 0::2] = torch.sin(position.unsqueeze(1) * div_term)
    pe[:, 1::2] = torch.cos(position.unsqueeze(1) * div_term)
    
    return pe


def relative_position_encoding(pos1: torch.Tensor, pos2: torch.Tensor, d_model: int) -> torch.Tensor:
    """相对位置编码"""
    diff = pos1.unsqueeze(-2) - pos2.unsqueeze(-3)  # [batch, seq1, seq2, 2]
    
    # 对x和y差值分别编码
    x_diff = diff[..., 0]
    y_diff = diff[..., 1]
    
    # 使用sin/cos编码
    x_encoding = torch.stack([
        torch.sin(x_diff / (10000 ** (2 * i / d_model))) if i % 2 == 0 
        else torch.cos(x_diff / (10000 ** (2 * (i-1) / d_model)))
        for i in range(d_model // 2)
    ], dim=-1)
    
    y_encoding = torch.stack([
        torch.sin(y_diff / (10000 ** (2 * i / d_model))) if i % 2 == 0 
        else torch.cos(y_diff / (10000 ** (2 * (i-1) / d_model)))
        for i in range(d_model // 2)
    ], dim=-1)
    
    return torch.cat([x_encoding, y_encoding], dim=-1)


def compute_gae(rewards: torch.Tensor, 
                values: torch.Tensor, 
                dones: torch.Tensor,
                gamma: float = 0.99, 
                gae_lambda: float = 0.95) -> Tuple[torch.Tensor, torch.Tensor]:
    """计算GAE优势估计"""
    batch_size, seq_len = rewards.shape
    advantages = torch.zeros_like(rewards)
    returns = torch.zeros_like(rewards)
    
    gae = 0
    for t in reversed(range(seq_len)):
        if t == seq_len - 1:
            next_value = 0
        else:
            next_value = values[:, t + 1]
        
        delta = rewards[:, t] + gamma * next_value * (1 - dones[:, t]) - values[:, t]
        gae = delta + gamma * gae_lambda * (1 - dones[:, t]) * gae
        advantages[:, t] = gae
        returns[:, t] = advantages[:, t] + values[:, t]
    
    return advantages, returns


def normalize_advantages(advantages: torch.Tensor, eps: float = 1e-8) -> torch.Tensor:
    """标准化优势函数"""
    return (advantages - advantages.mean()) / (advantages.std() + eps)


def clip_gradients(parameters, max_norm: float = 0.5) -> float:
    """梯度裁剪"""
    if max_norm <= 0:
        return 0.0
    
    total_norm = torch.nn.utils.clip_grad_norm_(parameters, max_norm)
    return total_norm.item()


def exponential_moving_average(current_value: float, new_value: float, decay: float = 0.99) -> float:
    """指数移动平均"""
    return decay * current_value + (1 - decay) * new_value


def linear_schedule(initial_value: float, final_value: float, current_step: int, total_steps: int) -> float:
    """线性调度"""
    if current_step >= total_steps:
        return final_value
    
    progress = current_step / total_steps
    return initial_value + (final_value - initial_value) * progress


def cosine_schedule(initial_value: float, final_value: float, current_step: int, total_steps: int) -> float:
    """余弦调度"""
    if current_step >= total_steps:
        return final_value
    
    progress = current_step / total_steps
    cosine_factor = 0.5 * (1 + math.cos(math.pi * progress))
    return final_value + (initial_value - final_value) * cosine_factor


def warmup_cosine_schedule(initial_value: float, 
                          peak_value: float, 
                          final_value: float,
                          current_step: int, 
                          warmup_steps: int, 
                          total_steps: int) -> float:
    """预热+余弦调度"""
    if current_step < warmup_steps:
        # 预热阶段
        return linear_schedule(initial_value, peak_value, current_step, warmup_steps)
    else:
        # 余弦衰减阶段
        return cosine_schedule(peak_value, final_value, current_step - warmup_steps, total_steps - warmup_steps)


def entropy(probs: torch.Tensor, dim: int = -1) -> torch.Tensor:
    """计算熵"""
    return -torch.sum(probs * torch.log(probs + 1e-8), dim=dim)


def kl_divergence(p: torch.Tensor, q: torch.Tensor, dim: int = -1) -> torch.Tensor:
    """计算KL散度"""
    return torch.sum(p * torch.log((p + 1e-8) / (q + 1e-8)), dim=dim)


def safe_mean(tensor: torch.Tensor) -> torch.Tensor:
    """安全的均值计算（处理空张量）"""
    if tensor.numel() == 0:
        return torch.tensor(0.0, device=tensor.device)
    return tensor.mean()


def safe_std(tensor: torch.Tensor) -> torch.Tensor:
    """安全的标准差计算"""
    if tensor.numel() <= 1:
        return torch.tensor(0.0, device=tensor.device)
    return tensor.std()


def batch_select(tensor: torch.Tensor, indices: torch.Tensor) -> torch.Tensor:
    """批量选择操作"""
    batch_size = tensor.size(0)
    batch_indices = torch.arange(batch_size, device=tensor.device).unsqueeze(1)
    return tensor[batch_indices, indices]


def one_hot(indices: torch.Tensor, num_classes: int) -> torch.Tensor:
    """One-hot编码"""
    return F.one_hot(indices, num_classes).float()


def smooth_l1_loss(input: torch.Tensor, target: torch.Tensor, beta: float = 1.0) -> torch.Tensor:
    """平滑L1损失"""
    return F.smooth_l1_loss(input, target, beta=beta)


def huber_loss(input: torch.Tensor, target: torch.Tensor, delta: float = 1.0) -> torch.Tensor:
    """Huber损失"""
    abs_error = torch.abs(input - target)
    quadratic = torch.min(abs_error, torch.tensor(delta))
    linear = abs_error - quadratic
    return 0.5 * quadratic**2 + delta * linear
