"""
约束增强机制

实现距离、载重、优先级、时间和可用性约束的注意力增强
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List
import math


class ConstraintEnhancement(nn.Module):
    """约束增强机制"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 constraint_types: List[str] = None,
                 constraint_weights: Dict[str, float] = None,
                 learnable_weights: bool = True,
                 temperature: float = 1.0):
        """
        初始化约束增强机制
        
        Args:
            embed_dim: 嵌入维度
            constraint_types: 约束类型列表
            constraint_weights: 约束权重字典
            learnable_weights: 是否学习约束权重
            temperature: 温度参数
        """
        super(ConstraintEnhancement, self).__init__()
        
        if constraint_types is None:
            constraint_types = ['distance', 'capacity', 'priority', 'time', 'availability']
        
        self.constraint_types = constraint_types
        self.embed_dim = embed_dim
        self.temperature = temperature
        
        # 默认约束权重
        if constraint_weights is None:
            constraint_weights = {
                'distance': 1.0,
                'capacity': 0.8,
                'priority': 0.6,
                'time': 0.7,
                'availability': 1.0
            }
        
        # 约束权重参数
        if learnable_weights:
            self.constraint_weights = nn.ParameterDict({
                constraint_type: nn.Parameter(torch.tensor(constraint_weights.get(constraint_type, 1.0)))
                for constraint_type in constraint_types
            })
        else:
            self.register_buffer('constraint_weights', torch.tensor([
                constraint_weights.get(constraint_type, 1.0) 
                for constraint_type in constraint_types
            ]))
        
        # 约束特征投影层
        self.constraint_projections = nn.ModuleDict({
            constraint_type: nn.Sequential(
                nn.Linear(embed_dim, embed_dim // 4),
                nn.ReLU(),
                nn.Linear(embed_dim // 4, 1)
            ) for constraint_type in constraint_types
        })
        
        # 约束融合层
        self.constraint_fusion = nn.Sequential(
            nn.Linear(len(constraint_types), embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1)
        )
    
    def _compute_distance_constraint(self, 
                                   agv_embeddings: torch.Tensor,
                                   task_embeddings: torch.Tensor,
                                   agv_positions: torch.Tensor,
                                   task_positions: torch.Tensor) -> torch.Tensor:
        """
        计算距离约束
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            task_embeddings: 任务嵌入 [batch_size, num_tasks, embed_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            task_positions: 任务位置 [batch_size, num_tasks, 2]
            
        Returns:
            distance_scores: 距离约束分数 [batch_size, num_agvs, num_tasks]
        """
        batch_size, num_agvs, _ = agv_positions.shape
        num_tasks = task_positions.shape[1]
        
        # 计算欧几里得距离
        agv_pos_expanded = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        task_pos_expanded = task_positions.unsqueeze(1)  # [batch_size, 1, num_tasks, 2]
        
        distances = torch.norm(agv_pos_expanded - task_pos_expanded, dim=-1)
        # [batch_size, num_agvs, num_tasks]
        
        # 距离越近，分数越高（使用负指数函数）
        distance_scores = torch.exp(-distances / self.temperature)
        
        return distance_scores
    
    def _compute_capacity_constraint(self, 
                                   agv_embeddings: torch.Tensor,
                                   task_embeddings: torch.Tensor,
                                   agv_capacities: torch.Tensor,
                                   task_weights: torch.Tensor) -> torch.Tensor:
        """
        计算载重约束
        
        Args:
            agv_embeddings: AGV嵌入
            task_embeddings: 任务嵌入
            agv_capacities: AGV剩余载重 [batch_size, num_agvs]
            task_weights: 任务重量 [batch_size, num_tasks]
            
        Returns:
            capacity_scores: 载重约束分数 [batch_size, num_agvs, num_tasks]
        """
        batch_size, num_agvs = agv_capacities.shape
        num_tasks = task_weights.shape[1]
        
        # 扩展维度进行比较
        agv_cap_expanded = agv_capacities.unsqueeze(2)  # [batch_size, num_agvs, 1]
        task_weight_expanded = task_weights.unsqueeze(1)  # [batch_size, 1, num_tasks]
        
        # 计算载重余量
        capacity_margin = agv_cap_expanded - task_weight_expanded
        
        # 载重足够时分数为1，不足时分数为0，接近临界时使用sigmoid平滑
        capacity_scores = torch.sigmoid(capacity_margin * 10)  # 乘以10增加陡峭度
        
        return capacity_scores
    
    def _compute_priority_constraint(self, 
                                   agv_embeddings: torch.Tensor,
                                   task_embeddings: torch.Tensor,
                                   task_priorities: torch.Tensor) -> torch.Tensor:
        """
        计算优先级约束
        
        Args:
            agv_embeddings: AGV嵌入
            task_embeddings: 任务嵌入
            task_priorities: 任务优先级 [batch_size, num_tasks]
            
        Returns:
            priority_scores: 优先级约束分数 [batch_size, num_agvs, num_tasks]
        """
        batch_size, num_agvs, _ = agv_embeddings.shape
        num_tasks = task_priorities.shape[1]
        
        # 归一化优先级到[0,1]范围
        priority_normalized = F.softmax(task_priorities, dim=-1)
        
        # 扩展到AGV维度
        priority_scores = priority_normalized.unsqueeze(1).expand(-1, num_agvs, -1)
        
        return priority_scores
    
    def _compute_time_constraint(self, 
                               agv_embeddings: torch.Tensor,
                               task_embeddings: torch.Tensor,
                               current_time: torch.Tensor,
                               task_deadlines: torch.Tensor,
                               estimated_durations: torch.Tensor) -> torch.Tensor:
        """
        计算时间约束
        
        Args:
            agv_embeddings: AGV嵌入
            task_embeddings: 任务嵌入
            current_time: 当前时间 [batch_size]
            task_deadlines: 任务截止时间 [batch_size, num_tasks]
            estimated_durations: 预估执行时间 [batch_size, num_agvs, num_tasks]
            
        Returns:
            time_scores: 时间约束分数 [batch_size, num_agvs, num_tasks]
        """
        batch_size, num_agvs, num_tasks = estimated_durations.shape
        
        # 扩展当前时间和截止时间
        current_time_expanded = current_time.unsqueeze(1).unsqueeze(2)  # [batch_size, 1, 1]
        deadline_expanded = task_deadlines.unsqueeze(1)  # [batch_size, 1, num_tasks]
        
        # 计算完成时间
        completion_time = current_time_expanded + estimated_durations
        
        # 计算时间余量
        time_margin = deadline_expanded - completion_time
        
        # 时间充足时分数高，时间紧张时分数低
        time_scores = torch.sigmoid(time_margin / self.temperature)
        
        return time_scores
    
    def _compute_availability_constraint(self, 
                                       agv_embeddings: torch.Tensor,
                                       task_embeddings: torch.Tensor,
                                       agv_availability: torch.Tensor,
                                       task_availability: torch.Tensor) -> torch.Tensor:
        """
        计算可用性约束
        
        Args:
            agv_embeddings: AGV嵌入
            task_embeddings: 任务嵌入
            agv_availability: AGV可用性 [batch_size, num_agvs]
            task_availability: 任务可用性 [batch_size, num_tasks]
            
        Returns:
            availability_scores: 可用性约束分数 [batch_size, num_agvs, num_tasks]
        """
        batch_size, num_agvs = agv_availability.shape
        num_tasks = task_availability.shape[1]
        
        # 扩展维度
        agv_avail_expanded = agv_availability.unsqueeze(2)  # [batch_size, num_agvs, 1]
        task_avail_expanded = task_availability.unsqueeze(1)  # [batch_size, 1, num_tasks]
        
        # 可用性分数为两者的乘积
        availability_scores = agv_avail_expanded * task_avail_expanded
        
        return availability_scores
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                task_embeddings: torch.Tensor,
                constraint_features: Dict[str, torch.Tensor],
                base_attention_scores: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            task_embeddings: 任务嵌入 [batch_size, num_tasks, embed_dim]
            constraint_features: 约束特征字典
            base_attention_scores: 基础注意力分数 [batch_size, num_agvs, num_tasks]
            
        Returns:
            enhanced_scores: 增强后的注意力分数 [batch_size, num_agvs, num_tasks]
        """
        batch_size, num_agvs, num_tasks = base_attention_scores.shape
        
        constraint_scores = []
        
        # 计算各种约束分数
        for constraint_type in self.constraint_types:
            if constraint_type == 'distance':
                scores = self._compute_distance_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['agv_positions'],
                    constraint_features['task_positions']
                )
            elif constraint_type == 'capacity':
                scores = self._compute_capacity_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['agv_capacities'],
                    constraint_features['task_weights']
                )
            elif constraint_type == 'priority':
                scores = self._compute_priority_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['task_priorities']
                )
            elif constraint_type == 'time':
                scores = self._compute_time_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['current_time'],
                    constraint_features['task_deadlines'],
                    constraint_features['estimated_durations']
                )
            elif constraint_type == 'availability':
                scores = self._compute_availability_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['agv_availability'],
                    constraint_features['task_availability']
                )
            else:
                # 默认约束（无约束）
                scores = torch.ones(batch_size, num_agvs, num_tasks, 
                                  device=agv_embeddings.device)
            
            constraint_scores.append(scores)
        
        # 堆叠约束分数
        constraint_scores = torch.stack(constraint_scores, dim=-1)
        # [batch_size, num_agvs, num_tasks, num_constraints]
        
        # 应用约束权重
        if isinstance(self.constraint_weights, nn.ParameterDict):
            weights = torch.stack([
                self.constraint_weights[constraint_type] 
                for constraint_type in self.constraint_types
            ])
        else:
            weights = self.constraint_weights
        
        # 加权约束分数
        weighted_scores = constraint_scores * weights.unsqueeze(0).unsqueeze(0).unsqueeze(0)
        
        # 通过融合层计算最终约束分数
        constraint_enhancement = self.constraint_fusion(weighted_scores).squeeze(-1)
        # [batch_size, num_agvs, num_tasks]
        
        # 将约束增强应用到基础注意力分数
        enhanced_scores = base_attention_scores + constraint_enhancement
        
        return enhanced_scores


class AdaptiveConstraintEnhancement(ConstraintEnhancement):
    """自适应约束增强机制"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 constraint_types: List[str] = None,
                 constraint_weights: Dict[str, float] = None,
                 adaptation_network_dim: int = 32):
        """
        初始化自适应约束增强机制
        
        Args:
            embed_dim: 嵌入维度
            constraint_types: 约束类型列表
            constraint_weights: 初始约束权重
            adaptation_network_dim: 自适应网络维度
        """
        super().__init__(embed_dim, constraint_types, constraint_weights, learnable_weights=False)
        
        # 自适应权重网络
        self.weight_adaptation_network = nn.Sequential(
            nn.Linear(embed_dim * 2, adaptation_network_dim),  # AGV + Task embeddings
            nn.ReLU(),
            nn.Linear(adaptation_network_dim, adaptation_network_dim),
            nn.ReLU(),
            nn.Linear(adaptation_network_dim, len(self.constraint_types)),
            nn.Softmax(dim=-1)
        )
    
    def _compute_adaptive_weights(self, 
                                agv_embeddings: torch.Tensor,
                                task_embeddings: torch.Tensor) -> torch.Tensor:
        """
        计算自适应约束权重
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            task_embeddings: 任务嵌入 [batch_size, num_tasks, embed_dim]
            
        Returns:
            adaptive_weights: 自适应权重 [batch_size, num_agvs, num_tasks, num_constraints]
        """
        batch_size, num_agvs, embed_dim = agv_embeddings.shape
        num_tasks = task_embeddings.shape[1]
        
        # 扩展嵌入以计算所有AGV-任务对的权重
        agv_expanded = agv_embeddings.unsqueeze(2).expand(-1, -1, num_tasks, -1)
        task_expanded = task_embeddings.unsqueeze(1).expand(-1, num_agvs, -1, -1)
        
        # 拼接AGV和任务嵌入
        combined_embeddings = torch.cat([agv_expanded, task_expanded], dim=-1)
        # [batch_size, num_agvs, num_tasks, embed_dim * 2]
        
        # 通过自适应网络计算权重
        adaptive_weights = self.weight_adaptation_network(combined_embeddings)
        # [batch_size, num_agvs, num_tasks, num_constraints]
        
        return adaptive_weights
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                task_embeddings: torch.Tensor,
                constraint_features: Dict[str, torch.Tensor],
                base_attention_scores: torch.Tensor) -> torch.Tensor:
        """
        前向传播（自适应版本）
        """
        batch_size, num_agvs, num_tasks = base_attention_scores.shape
        
        # 计算自适应权重
        adaptive_weights = self._compute_adaptive_weights(agv_embeddings, task_embeddings)
        
        constraint_scores = []
        
        # 计算各种约束分数（与父类相同）
        for constraint_type in self.constraint_types:
            if constraint_type == 'distance':
                scores = self._compute_distance_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['agv_positions'],
                    constraint_features['task_positions']
                )
            elif constraint_type == 'capacity':
                scores = self._compute_capacity_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['agv_capacities'],
                    constraint_features['task_weights']
                )
            elif constraint_type == 'priority':
                scores = self._compute_priority_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['task_priorities']
                )
            elif constraint_type == 'time':
                scores = self._compute_time_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['current_time'],
                    constraint_features['task_deadlines'],
                    constraint_features['estimated_durations']
                )
            elif constraint_type == 'availability':
                scores = self._compute_availability_constraint(
                    agv_embeddings, task_embeddings,
                    constraint_features['agv_availability'],
                    constraint_features['task_availability']
                )
            else:
                scores = torch.ones(batch_size, num_agvs, num_tasks, 
                                  device=agv_embeddings.device)
            
            constraint_scores.append(scores)
        
        # 堆叠约束分数
        constraint_scores = torch.stack(constraint_scores, dim=-1)
        # [batch_size, num_agvs, num_tasks, num_constraints]
        
        # 应用自适应权重
        weighted_scores = constraint_scores * adaptive_weights
        
        # 求和得到最终约束增强
        constraint_enhancement = torch.sum(weighted_scores, dim=-1)
        # [batch_size, num_agvs, num_tasks]
        
        # 将约束增强应用到基础注意力分数
        enhanced_scores = base_attention_scores + constraint_enhancement
        
        return enhanced_scores
