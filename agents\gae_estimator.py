"""
广义优势估计(GAE)实现

实现GAE算法，支持可配置的λ和γ参数，用于MAPPO训练
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Optional, List, Dict
from utils.math_utils import compute_gae, normalize_advantages


class GAEEstimator:
    """广义优势估计器"""
    
    def __init__(self, 
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95,
                 normalize_advantages: bool = True,
                 eps: float = 1e-8):
        """
        初始化GAE估计器
        
        Args:
            gamma: 折扣因子
            gae_lambda: GAE参数λ
            normalize_advantages: 是否标准化优势函数
            eps: 数值稳定性参数
        """
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.normalize_advantages = normalize_advantages
        self.eps = eps
    
    def compute_advantages_and_returns(self, 
                                     rewards: torch.Tensor,
                                     values: torch.Tensor,
                                     dones: torch.Tensor,
                                     next_values: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算GAE优势和回报
        
        Args:
            rewards: 奖励序列 [batch_size, seq_len] 或 [batch_size, num_agents, seq_len]
            values: 价值估计 [batch_size, seq_len] 或 [batch_size, num_agents, seq_len]
            dones: 终止标志 [batch_size, seq_len] 或 [batch_size, num_agents, seq_len]
            next_values: 下一状态价值 [batch_size] 或 [batch_size, num_agents]
            
        Returns:
            advantages: GAE优势 [batch_size, seq_len] 或 [batch_size, num_agents, seq_len]
            returns: 回报 [batch_size, seq_len] 或 [batch_size, num_agents, seq_len]
        """
        # 处理多智能体情况
        if rewards.dim() == 3:  # [batch_size, num_agents, seq_len]
            batch_size, num_agents, seq_len = rewards.shape
            
            all_advantages = []
            all_returns = []
            
            for agent_idx in range(num_agents):
                agent_rewards = rewards[:, agent_idx, :]  # [batch_size, seq_len]
                agent_values = values[:, agent_idx, :]    # [batch_size, seq_len]
                agent_dones = dones[:, agent_idx, :]      # [batch_size, seq_len]
                agent_next_values = next_values[:, agent_idx] if next_values is not None else None
                
                agent_advantages, agent_returns = self._compute_single_agent_gae(
                    agent_rewards, agent_values, agent_dones, agent_next_values
                )
                
                all_advantages.append(agent_advantages.unsqueeze(1))  # [batch_size, 1, seq_len]
                all_returns.append(agent_returns.unsqueeze(1))        # [batch_size, 1, seq_len]
            
            advantages = torch.cat(all_advantages, dim=1)  # [batch_size, num_agents, seq_len]
            returns = torch.cat(all_returns, dim=1)        # [batch_size, num_agents, seq_len]
            
        else:  # [batch_size, seq_len]
            advantages, returns = self._compute_single_agent_gae(rewards, values, dones, next_values)
        
        # 标准化优势函数
        if self.normalize_advantages:
            advantages = self._normalize_advantages(advantages)
        
        return advantages, returns
    
    def _compute_single_agent_gae(self, 
                                 rewards: torch.Tensor,
                                 values: torch.Tensor,
                                 dones: torch.Tensor,
                                 next_values: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算单个智能体的GAE
        
        Args:
            rewards: [batch_size, seq_len]
            values: [batch_size, seq_len]
            dones: [batch_size, seq_len]
            next_values: [batch_size]
            
        Returns:
            advantages: [batch_size, seq_len]
            returns: [batch_size, seq_len]
        """
        batch_size, seq_len = rewards.shape
        device = rewards.device
        
        # 初始化
        advantages = torch.zeros_like(rewards)
        returns = torch.zeros_like(rewards)
        
        # 计算最后一步的next_value
        if next_values is not None:
            last_values = next_values
        else:
            last_values = torch.zeros(batch_size, device=device)
        
        # 从后往前计算GAE
        gae = torch.zeros(batch_size, device=device)
        
        for t in reversed(range(seq_len)):
            if t == seq_len - 1:
                next_value = last_values
                next_non_terminal = 1.0 - dones[:, t]
            else:
                next_value = values[:, t + 1]
                next_non_terminal = 1.0 - dones[:, t]
            
            # 计算TD误差
            delta = rewards[:, t] + self.gamma * next_value * next_non_terminal - values[:, t]
            
            # 计算GAE
            gae = delta + self.gamma * self.gae_lambda * next_non_terminal * gae
            advantages[:, t] = gae
            
            # 计算回报
            returns[:, t] = advantages[:, t] + values[:, t]
        
        return advantages, returns
    
    def _normalize_advantages(self, advantages: torch.Tensor) -> torch.Tensor:
        """标准化优势函数"""
        if advantages.dim() == 3:  # [batch_size, num_agents, seq_len]
            # 对每个智能体分别标准化
            batch_size, num_agents, seq_len = advantages.shape
            normalized_advantages = torch.zeros_like(advantages)
            
            for agent_idx in range(num_agents):
                agent_advantages = advantages[:, agent_idx, :].flatten()
                if len(agent_advantages) > 1:
                    mean = agent_advantages.mean()
                    std = agent_advantages.std() + self.eps
                    normalized_advantages[:, agent_idx, :] = (advantages[:, agent_idx, :] - mean) / std
                else:
                    normalized_advantages[:, agent_idx, :] = advantages[:, agent_idx, :]
            
            return normalized_advantages
        else:  # [batch_size, seq_len]
            flat_advantages = advantages.flatten()
            if len(flat_advantages) > 1:
                mean = flat_advantages.mean()
                std = flat_advantages.std() + self.eps
                return (advantages - mean) / std
            else:
                return advantages
    
    def compute_value_targets(self, 
                            rewards: torch.Tensor,
                            values: torch.Tensor,
                            dones: torch.Tensor,
                            next_values: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算价值目标（用于价值网络训练）
        
        Args:
            rewards: 奖励序列
            values: 价值估计
            dones: 终止标志
            next_values: 下一状态价值
            
        Returns:
            value_targets: 价值目标
        """
        _, returns = self.compute_advantages_and_returns(rewards, values, dones, next_values)
        return returns
    
    def update_parameters(self, gamma: Optional[float] = None, gae_lambda: Optional[float] = None):
        """更新GAE参数"""
        if gamma is not None:
            self.gamma = gamma
        if gae_lambda is not None:
            self.gae_lambda = gae_lambda


class MultiAgentGAEEstimator:
    """多智能体GAE估计器"""
    
    def __init__(self, 
                 num_agents: int,
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95,
                 normalize_advantages: bool = True,
                 individual_normalization: bool = True):
        """
        初始化多智能体GAE估计器
        
        Args:
            num_agents: 智能体数量
            gamma: 折扣因子
            gae_lambda: GAE参数λ
            normalize_advantages: 是否标准化优势函数
            individual_normalization: 是否对每个智能体单独标准化
        """
        self.num_agents = num_agents
        self.individual_normalization = individual_normalization
        
        if individual_normalization:
            # 每个智能体独立的GAE估计器
            self.estimators = [
                GAEEstimator(gamma, gae_lambda, normalize_advantages)
                for _ in range(num_agents)
            ]
            self.shared_estimator = None
        else:
            # 共享的GAE估计器
            self.estimators = None
            self.shared_estimator = GAEEstimator(gamma, gae_lambda, normalize_advantages)
    
    def compute_advantages_and_returns(self, 
                                     rewards: torch.Tensor,
                                     values: torch.Tensor,
                                     dones: torch.Tensor,
                                     next_values: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算多智能体GAE优势和回报
        
        Args:
            rewards: [batch_size, num_agents, seq_len]
            values: [batch_size, num_agents, seq_len] 或 [batch_size, seq_len] (中心化价值)
            dones: [batch_size, num_agents, seq_len]
            next_values: [batch_size, num_agents] 或 [batch_size] (中心化价值)
            
        Returns:
            advantages: [batch_size, num_agents, seq_len]
            returns: [batch_size, num_agents, seq_len]
        """
        batch_size, num_agents, seq_len = rewards.shape
        
        if self.individual_normalization:
            # 每个智能体独立计算
            all_advantages = []
            all_returns = []
            
            for agent_idx in range(num_agents):
                agent_rewards = rewards[:, agent_idx, :]
                agent_dones = dones[:, agent_idx, :]
                
                # 处理价值函数
                if values.dim() == 3:  # 分布式价值函数
                    agent_values = values[:, agent_idx, :]
                    agent_next_values = next_values[:, agent_idx] if next_values is not None else None
                else:  # 中心化价值函数
                    agent_values = values  # 所有智能体共享同一价值
                    agent_next_values = next_values if next_values is not None else None
                
                agent_advantages, agent_returns = self.estimators[agent_idx].compute_advantages_and_returns(
                    agent_rewards, agent_values, agent_dones, agent_next_values
                )
                
                all_advantages.append(agent_advantages.unsqueeze(1))
                all_returns.append(agent_returns.unsqueeze(1))
            
            advantages = torch.cat(all_advantages, dim=1)
            returns = torch.cat(all_returns, dim=1)
        
        else:
            # 共享GAE估计器
            advantages, returns = self.shared_estimator.compute_advantages_and_returns(
                rewards, values, dones, next_values
            )
        
        return advantages, returns
    
    def compute_centralized_advantages(self, 
                                     rewards: torch.Tensor,
                                     values: torch.Tensor,
                                     dones: torch.Tensor,
                                     next_values: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算中心化价值函数的优势
        
        Args:
            rewards: [batch_size, num_agents, seq_len]
            values: [batch_size, seq_len] - 中心化价值
            dones: [batch_size, num_agents, seq_len]
            next_values: [batch_size] - 中心化下一状态价值
            
        Returns:
            advantages: [batch_size, num_agents, seq_len]
            returns: [batch_size, seq_len]
        """
        # 聚合奖励（简单求和）
        aggregated_rewards = torch.sum(rewards, dim=1)  # [batch_size, seq_len]
        
        # 聚合终止标志（任一智能体终止则终止）
        aggregated_dones = torch.max(dones, dim=1)[0]  # [batch_size, seq_len]
        
        # 计算中心化优势和回报
        centralized_advantages, centralized_returns = self.shared_estimator.compute_advantages_and_returns(
            aggregated_rewards, values, aggregated_dones, next_values
        )
        
        # 将中心化优势分配给每个智能体
        advantages = centralized_advantages.unsqueeze(1).expand(-1, self.num_agents, -1)
        
        return advantages, centralized_returns
    
    def get_statistics(self) -> Dict[str, float]:
        """获取GAE统计信息"""
        if self.individual_normalization:
            stats = {}
            for i, estimator in enumerate(self.estimators):
                stats[f'agent_{i}_gamma'] = estimator.gamma
                stats[f'agent_{i}_gae_lambda'] = estimator.gae_lambda
            return stats
        else:
            return {
                'gamma': self.shared_estimator.gamma,
                'gae_lambda': self.shared_estimator.gae_lambda
            }


class AdaptiveGAEEstimator(GAEEstimator):
    """自适应GAE估计器"""
    
    def __init__(self, 
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95,
                 normalize_advantages: bool = True,
                 adaptive_lambda: bool = True,
                 lambda_decay: float = 0.999,
                 min_lambda: float = 0.8,
                 max_lambda: float = 0.99):
        """
        初始化自适应GAE估计器
        
        Args:
            gamma: 折扣因子
            gae_lambda: 初始GAE参数λ
            normalize_advantages: 是否标准化优势函数
            adaptive_lambda: 是否自适应调整λ
            lambda_decay: λ衰减率
            min_lambda: λ最小值
            max_lambda: λ最大值
        """
        super().__init__(gamma, gae_lambda, normalize_advantages)
        
        self.adaptive_lambda = adaptive_lambda
        self.lambda_decay = lambda_decay
        self.min_lambda = min_lambda
        self.max_lambda = max_lambda
        self.initial_lambda = gae_lambda
        
        # 统计信息
        self.step_count = 0
        self.variance_history = []
    
    def compute_advantages_and_returns(self, 
                                     rewards: torch.Tensor,
                                     values: torch.Tensor,
                                     dones: torch.Tensor,
                                     next_values: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """计算自适应GAE优势和回报"""
        # 更新λ参数
        if self.adaptive_lambda:
            self._update_lambda(rewards, values)
        
        # 计算GAE
        advantages, returns = super().compute_advantages_and_returns(
            rewards, values, dones, next_values
        )
        
        self.step_count += 1
        return advantages, returns
    
    def _update_lambda(self, rewards: torch.Tensor, values: torch.Tensor):
        """自适应更新λ参数"""
        # 计算奖励方差作为环境复杂度指标
        reward_variance = torch.var(rewards).item()
        self.variance_history.append(reward_variance)
        
        # 保持历史长度
        if len(self.variance_history) > 100:
            self.variance_history.pop(0)
        
        # 根据方差调整λ
        if len(self.variance_history) > 10:
            recent_variance = np.mean(self.variance_history[-10:])
            
            # 高方差环境使用较小的λ（更关注即时奖励）
            # 低方差环境使用较大的λ（更关注长期奖励）
            if recent_variance > 1.0:
                target_lambda = self.min_lambda
            else:
                target_lambda = self.max_lambda
            
            # 平滑调整
            self.gae_lambda = self.gae_lambda * self.lambda_decay + target_lambda * (1 - self.lambda_decay)
            self.gae_lambda = np.clip(self.gae_lambda, self.min_lambda, self.max_lambda)
    
    def reset_adaptation(self):
        """重置自适应参数"""
        self.gae_lambda = self.initial_lambda
        self.step_count = 0
        self.variance_history.clear()
