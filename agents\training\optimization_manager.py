"""
训练优化策略集成管理器

集成所有训练优化策略，提供统一的接口
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
from dataclasses import dataclass

from .curriculum_learning import CurriculumLearningManager, CurriculumConfig
from .prioritized_replay import MultiAgentPrioritizedReplayBuffer, PrioritizedReplayConfig
from .stability_manager import TrainingStabilityManager, StabilityConfig
from .environment_adaptation import EnvironmentChangeDetector, AdaptationStrategyManager, AdaptationConfig
from .maml_integration import MAMLLearner, MAMLTaskSampler, MAMLConfig


@dataclass
class OptimizationConfig:
    """优化配置"""
    # 组件启用开关
    use_curriculum_learning: bool = True
    use_prioritized_replay: bool = True
    use_stability_manager: bool = True
    use_environment_adaptation: bool = True
    use_maml: bool = False  # MAML较为复杂，默认关闭
    
    # 集成参数
    update_frequency: int = 100         # 更新频率
    statistics_frequency: int = 1000    # 统计频率
    checkpoint_frequency: int = 5000    # 检查点频率
    
    # 性能阈值
    performance_window: int = 100       # 性能评估窗口
    min_performance_threshold: float = 0.3  # 最低性能阈值
    adaptation_trigger_threshold: float = 0.2  # 适应触发阈值


class TrainingOptimizationManager:
    """训练优化策略管理器"""
    
    def __init__(self, 
                 config: OptimizationConfig,
                 policy_network,
                 value_network,
                 optimizer,
                 num_agents: int = 4,
                 logger: logging.Logger = None):
        """
        初始化训练优化策略管理器
        
        Args:
            config: 优化配置
            policy_network: 策略网络
            value_network: 价值网络
            optimizer: 优化器
            num_agents: 智能体数量
            logger: 日志记录器
        """
        self.config = config
        self.policy_network = policy_network
        self.value_network = value_network
        self.optimizer = optimizer
        self.num_agents = num_agents
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化组件
        self._initialize_components()
        
        # 状态跟踪
        self.training_step = 0
        self.episode_count = 0
        self.last_update_step = 0
        
        # 性能历史
        self.performance_history = []
        self.optimization_events = []
        
        self.logger.info("训练优化策略管理器初始化完成")
    
    def _initialize_components(self):
        """初始化各个组件"""
        # 课程学习
        if self.config.use_curriculum_learning:
            curriculum_config = CurriculumConfig()
            self.curriculum_manager = CurriculumLearningManager(
                config=curriculum_config,
                logger=self.logger
            )
        else:
            self.curriculum_manager = None
        
        # 优先级经验回放
        if self.config.use_prioritized_replay:
            replay_config = PrioritizedReplayConfig()
            self.replay_buffer = MultiAgentPrioritizedReplayBuffer(
                config=replay_config,
                num_agents=self.num_agents
            )
        else:
            self.replay_buffer = None
        
        # 训练稳定性管理
        if self.config.use_stability_manager:
            stability_config = StabilityConfig()
            self.stability_manager = TrainingStabilityManager(
                config=stability_config,
                optimizer=self.optimizer,
                logger=self.logger
            )
        else:
            self.stability_manager = None
        
        # 环境适应
        if self.config.use_environment_adaptation:
            adaptation_config = AdaptationConfig()
            self.change_detector = EnvironmentChangeDetector(
                config=adaptation_config,
                logger=self.logger
            )
            self.adaptation_manager = AdaptationStrategyManager(
                config=adaptation_config,
                policy_network=self.policy_network,
                value_network=self.value_network,
                optimizer=self.optimizer,
                logger=self.logger
            )
        else:
            self.change_detector = None
            self.adaptation_manager = None
        
        # MAML元学习
        if self.config.use_maml:
            maml_config = MAMLConfig()
            # 这里需要定义损失函数和环境配置
            def dummy_loss_function(batch, policy, value):
                return torch.tensor(0.0)
            
            self.maml_learner = MAMLLearner(
                config=maml_config,
                policy_network=self.policy_network,
                value_network=self.value_network,
                loss_function=dummy_loss_function,
                logger=self.logger
            )
            
            # 任务采样器需要环境配置
            environment_configs = [
                {'state_dim': 64, 'action_dim': 5, 'num_agvs': 2},
                {'state_dim': 64, 'action_dim': 5, 'num_agvs': 3},
                {'state_dim': 64, 'action_dim': 5, 'num_agvs': 4}
            ]
            
            self.task_sampler = MAMLTaskSampler(
                config=maml_config,
                environment_configs=environment_configs,
                logger=self.logger
            )
        else:
            self.maml_learner = None
            self.task_sampler = None
    
    def get_environment_config(self) -> Dict[str, Any]:
        """获取当前环境配置"""
        if self.curriculum_manager:
            return self.curriculum_manager.get_current_config()
        else:
            # 返回默认配置
            return {
                'num_agvs': self.num_agents,
                'max_tasks': 8,
                'min_task_distance': 6.0,
                'agv_spawn_distance': 4.0,
                'task_generation_rate': 0.15,
                'episode_length': 400
            }
    
    def add_experience(self, 
                      state: torch.Tensor,
                      action: torch.Tensor,
                      reward: torch.Tensor,
                      next_state: torch.Tensor,
                      done: torch.Tensor,
                      agent_id: int,
                      episode_id: int,
                      step_id: int,
                      td_error: Optional[float] = None,
                      global_state: Optional[torch.Tensor] = None,
                      attention_info: Optional[Dict] = None,
                      cooperation_score: Optional[float] = None):
        """添加经验到回放缓冲区"""
        if self.replay_buffer:
            self.replay_buffer.add(
                state=state,
                action=action,
                reward=reward,
                next_state=next_state,
                done=done,
                agent_id=agent_id,
                episode_id=episode_id,
                step_id=step_id,
                td_error=td_error,
                global_state=global_state,
                attention_info=attention_info,
                cooperation_score=cooperation_score
            )
    
    def sample_experiences(self, batch_size: Optional[int] = None):
        """从回放缓冲区采样经验"""
        if self.replay_buffer:
            return self.replay_buffer.sample(batch_size)
        else:
            return [], np.array([]), np.array([])
    
    def update_priorities(self, indices: np.ndarray, td_errors: np.ndarray):
        """更新经验优先级"""
        if self.replay_buffer:
            self.replay_buffer.update_priorities(indices, td_errors)
    
    def process_training_step(self, 
                            loss: float,
                            performance_metrics: Dict[str, float],
                            env_features: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """
        处理训练步骤
        
        Args:
            loss: 当前损失
            performance_metrics: 性能指标
            env_features: 环境特征
            
        Returns:
            处理结果
        """
        self.training_step += 1
        results = {}
        
        # 1. 训练稳定性管理
        if self.stability_manager:
            stability_result = self.stability_manager.process_training_step(
                loss=loss,
                parameters=list(self.policy_network.parameters()) + list(self.value_network.parameters()),
                performance_metrics=performance_metrics
            )
            results['stability'] = stability_result
            
            # 检查是否需要紧急停止
            if stability_result.get('emergency_stop', False):
                results['should_stop'] = True
                self.logger.error("训练稳定性管理器要求紧急停止")
                return results
        
        # 2. 环境变化检测和适应
        if self.change_detector and self.adaptation_manager:
            change_result = self.change_detector.update(
                performance_metrics=performance_metrics,
                env_features=env_features,
                episode=self.episode_count
            )
            results['change_detection'] = change_result
            
            if change_result['change_detected']:
                adaptation_result = self.adaptation_manager.adapt_to_change(
                    change_result=change_result,
                    current_performance=performance_metrics,
                    episode=self.episode_count
                )
                results['adaptation'] = adaptation_result
                
                if adaptation_result.get('adapted', False):
                    self.optimization_events.append({
                        'step': self.training_step,
                        'type': 'adaptation',
                        'details': adaptation_result
                    })
        
        # 3. 记录性能历史
        self.performance_history.append({
            'step': self.training_step,
            'episode': self.episode_count,
            'loss': loss,
            'performance': performance_metrics.copy()
        })
        
        # 4. 定期统计和检查点
        if self.training_step % self.config.statistics_frequency == 0:
            results['statistics'] = self.get_comprehensive_statistics()
        
        if self.training_step % self.config.checkpoint_frequency == 0:
            results['checkpoint_needed'] = True
        
        return results
    
    def update_episode_performance(self, 
                                 success_rate: float,
                                 efficiency: float,
                                 collision_rate: float,
                                 cooperation_score: float) -> bool:
        """
        更新回合性能并检查课程学习进度
        
        Args:
            success_rate: 成功率
            efficiency: 效率
            collision_rate: 碰撞率
            cooperation_score: 协作得分
            
        Returns:
            是否发生了课程变化
        """
        self.episode_count += 1
        
        if self.curriculum_manager:
            curriculum_changed = self.curriculum_manager.update_performance(
                success_rate=success_rate,
                efficiency=efficiency,
                collision_rate=collision_rate,
                cooperation_score=cooperation_score
            )
            
            if curriculum_changed:
                self.optimization_events.append({
                    'step': self.training_step,
                    'episode': self.episode_count,
                    'type': 'curriculum_change',
                    'details': self.curriculum_manager.get_statistics()
                })
                
                self.logger.info(f"课程学习发生变化: {self.curriculum_manager.get_statistics()}")
            
            return curriculum_changed
        
        return False
    
    def should_stop_training(self) -> bool:
        """判断是否应该停止训练"""
        # 检查稳定性管理器
        if self.stability_manager and self.stability_manager.should_stop_training():
            return True
        
        # 检查课程学习完成
        if self.curriculum_manager and self.curriculum_manager.is_completed():
            self.logger.info("课程学习已完成")
            return True
        
        # 检查性能持续恶化
        if len(self.performance_history) >= self.config.performance_window:
            recent_performance = [p['performance'].get('overall_score', 0.0) 
                                for p in self.performance_history[-self.config.performance_window:]]
            
            if np.mean(recent_performance) < self.config.min_performance_threshold:
                self.logger.warning("性能持续低于阈值，建议停止训练")
                return True
        
        return False
    
    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        stats = {
            'training_step': self.training_step,
            'episode_count': self.episode_count,
            'optimization_events': len(self.optimization_events)
        }
        
        # 各组件统计
        if self.curriculum_manager:
            stats['curriculum'] = self.curriculum_manager.get_statistics()
        
        if self.replay_buffer:
            stats['replay_buffer'] = self.replay_buffer.get_statistics()
        
        if self.stability_manager:
            stats['stability'] = self.stability_manager.get_comprehensive_statistics()
        
        if self.change_detector:
            stats['change_detection'] = self.change_detector.get_statistics()
        
        if self.adaptation_manager:
            stats['adaptation'] = self.adaptation_manager.get_adaptation_statistics()
        
        if self.maml_learner:
            stats['maml'] = self.maml_learner.get_statistics()
        
        # 性能统计
        if self.performance_history:
            recent_performance = self.performance_history[-50:]
            if recent_performance:
                recent_scores = [p['performance'].get('overall_score', 0.0) for p in recent_performance]
                stats['recent_performance'] = {
                    'mean': np.mean(recent_scores),
                    'std': np.std(recent_scores),
                    'trend': np.polyfit(range(len(recent_scores)), recent_scores, 1)[0]
                }
        
        return stats
    
    def save_state(self, filepath: str):
        """保存优化管理器状态"""
        state = {
            'training_step': self.training_step,
            'episode_count': self.episode_count,
            'performance_history': self.performance_history[-1000:],  # 只保存最近1000条
            'optimization_events': self.optimization_events[-100:]    # 只保存最近100个事件
        }
        
        # 保存各组件状态
        if self.curriculum_manager:
            curriculum_path = filepath.replace('.json', '_curriculum.json')
            self.curriculum_manager.save_state(curriculum_path)
        
        if self.replay_buffer:
            replay_path = filepath.replace('.json', '_replay.json')
            self.replay_buffer.save_buffer(replay_path)
        
        # 保存主状态
        import json
        with open(filepath, 'w') as f:
            json.dump(state, f, indent=2)
        
        self.logger.info(f"优化管理器状态已保存到: {filepath}")
    
    def load_state(self, filepath: str):
        """加载优化管理器状态"""
        import json
        with open(filepath, 'r') as f:
            state = json.load(f)
        
        self.training_step = state['training_step']
        self.episode_count = state['episode_count']
        self.performance_history = state['performance_history']
        self.optimization_events = state['optimization_events']
        
        # 加载各组件状态
        if self.curriculum_manager:
            curriculum_path = filepath.replace('.json', '_curriculum.json')
            try:
                self.curriculum_manager.load_state(curriculum_path)
            except FileNotFoundError:
                self.logger.warning(f"课程学习状态文件未找到: {curriculum_path}")
        
        if self.replay_buffer:
            replay_path = filepath.replace('.json', '_replay.json')
            try:
                self.replay_buffer.load_buffer(replay_path)
            except FileNotFoundError:
                self.logger.warning(f"回放缓冲区状态文件未找到: {replay_path}")
        
        self.logger.info(f"优化管理器状态已从 {filepath} 加载")
