# 实验配置文件
# 用于性能评估和对比实验

environment:
  render_mode: "rgb_array"  # 实验时不显示界面
  max_episode_steps: 1200   # 更长的episode用于充分评估

attention:
  embed_dim: 128  # 更大的嵌入维度
  num_heads: 8
  sparse_k: 12    # 更丰富的注意力

network:
  policy_hidden_dims: [512, 256, 128]  # 更深的网络
  value_hidden_dims: [512, 256, 128]
  dropout: 0.05  # 评估时减少dropout

training:
  lr: 1.0e-4  # 更小的学习率用于精细调优
  num_sgd_iter: 15
  train_batch_size: 8000
  
  # 完整的课程学习
  curriculum_stages:
    - num_agvs: 2
      num_tasks: 8
      episodes: 1000
    - num_agvs: 3
      num_tasks: 12
      episodes: 1500
    - num_agvs: 4
      num_tasks: 16
      episodes: 2500
    - num_agvs: 5
      num_tasks: 20
      episodes: 1000  # 扩展性测试

experiment:
  name: "full_experiment"
  num_episodes: 10000
  eval_interval: 200
  save_interval: 1000
  
  # 多种评估设置
  eval_configs:
    - name: "standard"
      num_agvs: 4
      num_tasks: 16
    - name: "high_load"
      num_agvs: 4
      num_tasks: 24
    - name: "many_agvs"
      num_agvs: 6
      num_tasks: 16
    - name: "stress_test"
      num_agvs: 6
      num_tasks: 30
