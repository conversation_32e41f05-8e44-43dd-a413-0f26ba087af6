"""
协作信息聚合

实现不同层次协作注意力的自适应权重融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, Tuple, Optional, List, Union
from enum import Enum


class AggregationMethod(Enum):
    """聚合方法枚举"""
    WEIGHTED_SUM = "weighted_sum"           # 加权求和
    ATTENTION_FUSION = "attention_fusion"   # 注意力融合
    GATED_FUSION = "gated_fusion"          # 门控融合
    HIERARCHICAL_FUSION = "hierarchical_fusion"  # 层次化融合
    ADAPTIVE_FUSION = "adaptive_fusion"     # 自适应融合


class CollaborationInformationAggregator(nn.Module):
    """协作信息聚合器"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_layers: int = 3,
                 layer_names: List[str] = None,
                 aggregation_method: AggregationMethod = AggregationMethod.ADAPTIVE_FUSION,
                 use_layer_norm: bool = True,
                 use_residual: bool = True,
                 use_dynamic_weights: bool = True,
                 temperature: float = 1.0,
                 dropout: float = 0.1):
        """
        初始化协作信息聚合器
        
        Args:
            embed_dim: 嵌入维度
            num_layers: 层数
            layer_names: 层名称列表
            aggregation_method: 聚合方法
            use_layer_norm: 是否使用层归一化
            use_residual: 是否使用残差连接
            use_dynamic_weights: 是否使用动态权重
            temperature: 温度参数
            dropout: Dropout概率
        """
        super(CollaborationInformationAggregator, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_layers = num_layers
        self.aggregation_method = aggregation_method
        self.use_layer_norm = use_layer_norm
        self.use_residual = use_residual
        self.use_dynamic_weights = use_dynamic_weights
        self.temperature = temperature
        
        if layer_names is None:
            layer_names = [f"layer_{i}" for i in range(num_layers)]
        self.layer_names = layer_names
        
        # 静态权重参数
        if not use_dynamic_weights:
            self.static_weights = nn.Parameter(torch.ones(num_layers) / num_layers)
        
        # 动态权重预测器
        if use_dynamic_weights:
            self.weight_predictor = DynamicWeightPredictor(
                embed_dim, num_layers, temperature, dropout
            )
        
        # 不同聚合方法的实现
        if aggregation_method == AggregationMethod.ATTENTION_FUSION:
            self.attention_fusion = AttentionFusion(embed_dim, num_layers, dropout)
        elif aggregation_method == AggregationMethod.GATED_FUSION:
            self.gated_fusion = GatedFusion(embed_dim, num_layers, dropout)
        elif aggregation_method == AggregationMethod.HIERARCHICAL_FUSION:
            self.hierarchical_fusion = HierarchicalFusion(embed_dim, num_layers, dropout)
        elif aggregation_method == AggregationMethod.ADAPTIVE_FUSION:
            self.adaptive_fusion = AdaptiveFusion(embed_dim, num_layers, dropout)
        
        # 层归一化
        if use_layer_norm:
            self.layer_norm = nn.LayerNorm(embed_dim)
        
        # 输出投影
        self.output_projection = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 质量评估器
        self.quality_assessor = AggregationQualityAssessor(embed_dim, num_layers)
        
        # 聚合历史
        self.aggregation_history = {}
    
    def forward(self, 
                layer_outputs: Dict[str, torch.Tensor],
                layer_attention_weights: Optional[Dict[str, torch.Tensor]] = None,
                global_context: Optional[torch.Tensor] = None,
                batch_id: str = "default") -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            layer_outputs: 各层输出字典 {layer_name: [batch_size, num_agvs, embed_dim]}
            layer_attention_weights: 各层注意力权重字典 (可选)
            global_context: 全局上下文 [batch_size, embed_dim] (可选)
            batch_id: 批次ID
            
        Returns:
            result_dict: 结果字典
        """
        # 确保输入层的顺序
        ordered_outputs = []
        ordered_attention_weights = []
        
        for layer_name in self.layer_names:
            if layer_name in layer_outputs:
                ordered_outputs.append(layer_outputs[layer_name])
                if layer_attention_weights and layer_name in layer_attention_weights:
                    ordered_attention_weights.append(layer_attention_weights[layer_name])
        
        if len(ordered_outputs) == 0:
            raise ValueError("没有有效的层输出")
        
        # 堆叠层输出
        stacked_outputs = torch.stack(ordered_outputs, dim=0)  # [num_layers, batch_size, num_agvs, embed_dim]
        batch_size, num_agvs, embed_dim = stacked_outputs.shape[1:]
        
        # 计算聚合权重
        if self.use_dynamic_weights:
            # 动态权重
            weight_input = torch.mean(stacked_outputs, dim=(0, 2))  # [batch_size, embed_dim]
            if global_context is not None:
                weight_input = torch.cat([weight_input, global_context], dim=-1)

            weight_result = self.weight_predictor(weight_input, stacked_outputs)
            aggregation_weights = weight_result['weights']
            weight_info = weight_result
        else:
            # 静态权重
            aggregation_weights = F.softmax(self.static_weights / self.temperature, dim=0)
            aggregation_weights = aggregation_weights.unsqueeze(0).expand(batch_size, -1)
            weight_info = {'weights': aggregation_weights, 'method': 'static'}
        
        # 根据聚合方法进行融合
        if self.aggregation_method == AggregationMethod.WEIGHTED_SUM:
            # 加权求和
            aggregated_output = self._weighted_sum_aggregation(stacked_outputs, aggregation_weights)
            fusion_info = {'method': 'weighted_sum'}
            
        elif self.aggregation_method == AggregationMethod.ATTENTION_FUSION:
            # 注意力融合
            fusion_result = self.attention_fusion(stacked_outputs, aggregation_weights)
            aggregated_output = fusion_result['fused_output']
            fusion_info = fusion_result
            
        elif self.aggregation_method == AggregationMethod.GATED_FUSION:
            # 门控融合
            fusion_result = self.gated_fusion(stacked_outputs, aggregation_weights)
            aggregated_output = fusion_result['fused_output']
            fusion_info = fusion_result
            
        elif self.aggregation_method == AggregationMethod.HIERARCHICAL_FUSION:
            # 层次化融合
            fusion_result = self.hierarchical_fusion(stacked_outputs, aggregation_weights)
            aggregated_output = fusion_result['fused_output']
            fusion_info = fusion_result
            
        elif self.aggregation_method == AggregationMethod.ADAPTIVE_FUSION:
            # 自适应融合
            fusion_result = self.adaptive_fusion(stacked_outputs, aggregation_weights, global_context)
            aggregated_output = fusion_result['fused_output']
            fusion_info = fusion_result
            
        else:
            # 默认加权求和
            aggregated_output = self._weighted_sum_aggregation(stacked_outputs, aggregation_weights)
            fusion_info = {'method': 'default_weighted_sum'}
        
        # 残差连接
        if self.use_residual:
            # 使用第一层作为残差
            residual = stacked_outputs[0]
            aggregated_output = aggregated_output + residual
        
        # 层归一化
        if self.use_layer_norm:
            aggregated_output = self.layer_norm(aggregated_output)
        
        # 输出投影
        final_output = self.output_projection(aggregated_output)
        
        # 质量评估
        quality_metrics = self.quality_assessor.assess_quality(
            stacked_outputs, aggregated_output, aggregation_weights
        )
        
        # 更新聚合历史
        self._update_aggregation_history(aggregation_weights, quality_metrics, batch_id)
        
        # 组装结果
        result = {
            'aggregated_output': final_output,
            'raw_aggregated_output': aggregated_output,
            'aggregation_weights': aggregation_weights,
            'weight_info': weight_info,
            'fusion_info': fusion_info,
            'quality_metrics': quality_metrics,
            'layer_outputs': layer_outputs,
            'stacked_outputs': stacked_outputs
        }
        
        if ordered_attention_weights:
            result['layer_attention_weights'] = dict(zip(self.layer_names, ordered_attention_weights))
        
        return result
    
    def _weighted_sum_aggregation(self, 
                                stacked_outputs: torch.Tensor,
                                weights: torch.Tensor) -> torch.Tensor:
        """加权求和聚合"""
        # weights: [batch_size, num_layers]
        # stacked_outputs: [num_layers, batch_size, num_agvs, embed_dim]
        
        weights = weights.unsqueeze(-1).unsqueeze(-1)  # [batch_size, num_layers, 1, 1]
        weights = weights.permute(1, 0, 2, 3)  # [num_layers, batch_size, 1, 1]
        
        weighted_outputs = stacked_outputs * weights
        aggregated = torch.sum(weighted_outputs, dim=0)  # [batch_size, num_agvs, embed_dim]
        
        return aggregated
    
    def _update_aggregation_history(self, 
                                  weights: torch.Tensor,
                                  quality_metrics: Dict[str, torch.Tensor],
                                  batch_id: str):
        """更新聚合历史"""
        if batch_id not in self.aggregation_history:
            self.aggregation_history[batch_id] = {
                'weights_history': [],
                'quality_history': []
            }
        
        # 添加当前权重和质量
        self.aggregation_history[batch_id]['weights_history'].append(weights.detach().cpu())
        self.aggregation_history[batch_id]['quality_history'].append({
            k: v.detach().cpu() if isinstance(v, torch.Tensor) else v 
            for k, v in quality_metrics.items()
        })
        
        # 限制历史长度
        max_history = 100
        if len(self.aggregation_history[batch_id]['weights_history']) > max_history:
            self.aggregation_history[batch_id]['weights_history'].pop(0)
            self.aggregation_history[batch_id]['quality_history'].pop(0)
    
    def get_aggregation_statistics(self, batch_id: str = "default") -> Dict[str, float]:
        """获取聚合统计信息"""
        if batch_id not in self.aggregation_history:
            return {}
        
        history = self.aggregation_history[batch_id]
        
        if not history['weights_history']:
            return {}
        
        # 权重统计
        weights_tensor = torch.stack(history['weights_history'])  # [history_len, batch_size, num_layers]
        
        stats = {
            'avg_weights': torch.mean(weights_tensor, dim=(0, 1)).tolist(),
            'weight_variance': torch.var(weights_tensor, dim=(0, 1)).tolist(),
            'weight_stability': 1.0 - torch.mean(torch.std(weights_tensor, dim=0)).item()
        }
        
        # 质量统计
        if history['quality_history']:
            quality_keys = history['quality_history'][0].keys()
            for key in quality_keys:
                if isinstance(history['quality_history'][0][key], torch.Tensor):
                    values = [q[key] for q in history['quality_history']]
                    if values:
                        values_tensor = torch.stack(values)
                        stats[f'avg_{key}'] = torch.mean(values_tensor).item()
                        stats[f'std_{key}'] = torch.std(values_tensor).item()
        
        return stats


class DynamicWeightPredictor(nn.Module):
    """动态权重预测器"""

    def __init__(self, embed_dim: int, num_layers: int, temperature: float, dropout: float):
        super(DynamicWeightPredictor, self).__init__()

        self.embed_dim = embed_dim
        self.num_layers = num_layers
        self.temperature = temperature

        # 权重预测网络 - 动态初始化
        self.weight_predictor = None
        self.context_adjuster = None
    
    def forward(self,
                weight_input: torch.Tensor,
                layer_outputs: torch.Tensor) -> Dict[str, torch.Tensor]:
        """预测动态权重"""
        batch_size = weight_input.shape[0]
        input_dim = weight_input.shape[-1]

        # 动态初始化权重预测网络
        if self.weight_predictor is None:
            self.weight_predictor = nn.Sequential(
                nn.Linear(input_dim, input_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(input_dim // 2, input_dim // 4),
                nn.ReLU(),
                nn.Linear(input_dim // 4, self.num_layers)
            ).to(weight_input.device)

        # 基础权重预测
        base_weights = self.weight_predictor(weight_input)  # [batch_size, num_layers]

        # 上下文感知调整
        layer_context = layer_outputs.view(self.num_layers, batch_size, -1)  # [num_layers, batch_size, ...]
        layer_context = layer_context.permute(1, 0, 2)  # [batch_size, num_layers, ...]
        layer_context = layer_context.contiguous().view(batch_size, -1)  # [batch_size, num_layers * ...]

        # 动态初始化上下文调整器
        if self.context_adjuster is None:
            context_dim = layer_context.shape[-1]
            self.context_adjuster = nn.Sequential(
                nn.Linear(context_dim, self.embed_dim),
                nn.ReLU(),
                nn.Linear(self.embed_dim, self.num_layers),
                nn.Tanh()
            ).to(weight_input.device)

        context_adjustment = self.context_adjuster(layer_context)  # [batch_size, num_layers]

        # 组合权重
        adjusted_weights = base_weights + context_adjustment * 0.1

        # 应用温度和softmax
        final_weights = F.softmax(adjusted_weights / self.temperature, dim=-1)

        return {
            'weights': final_weights,
            'base_weights': base_weights,
            'context_adjustment': context_adjustment,
            'method': 'dynamic'
        }


class AttentionFusion(nn.Module):
    """注意力融合"""
    
    def __init__(self, embed_dim: int, num_layers: int, dropout: float):
        super(AttentionFusion, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_layers = num_layers
        
        # 层间注意力
        self.layer_attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, 
                stacked_outputs: torch.Tensor,
                weights: torch.Tensor) -> Dict[str, torch.Tensor]:
        """注意力融合"""
        num_layers, batch_size, num_agvs, embed_dim = stacked_outputs.shape
        
        # 重塑为序列格式
        layer_sequence = stacked_outputs.permute(1, 2, 0, 3)  # [batch_size, num_agvs, num_layers, embed_dim]
        layer_sequence = layer_sequence.contiguous().view(batch_size * num_agvs, num_layers, embed_dim)
        
        # 层间注意力
        attended_output, attention_weights = self.layer_attention(
            layer_sequence, layer_sequence, layer_sequence
        )
        
        # 重塑回原始格式
        attended_output = attended_output.view(batch_size, num_agvs, num_layers, embed_dim)
        
        # 加权融合
        weights_expanded = weights.unsqueeze(1).unsqueeze(-1)  # [batch_size, 1, num_layers, 1]
        weighted_output = attended_output * weights_expanded
        fused_output = torch.sum(weighted_output, dim=2)  # [batch_size, num_agvs, embed_dim]
        
        # 最终融合
        fused_output = self.fusion_network(fused_output)
        
        return {
            'fused_output': fused_output,
            'attention_weights': attention_weights,
            'attended_output': attended_output,
            'method': 'attention_fusion'
        }


class GatedFusion(nn.Module):
    """门控融合"""
    
    def __init__(self, embed_dim: int, num_layers: int, dropout: float):
        super(GatedFusion, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_layers = num_layers
        
        # 门控网络
        self.gates = nn.ModuleList([
            nn.Sequential(
                nn.Linear(embed_dim * 2, embed_dim),
                nn.Sigmoid()
            ) for _ in range(num_layers)
        ])
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, 
                stacked_outputs: torch.Tensor,
                weights: torch.Tensor) -> Dict[str, torch.Tensor]:
        """门控融合"""
        num_layers, batch_size, num_agvs, embed_dim = stacked_outputs.shape
        
        # 计算平均表示作为上下文
        context = torch.mean(stacked_outputs, dim=0)  # [batch_size, num_agvs, embed_dim]
        
        # 门控融合
        gated_outputs = []
        gate_values = []
        
        for i in range(num_layers):
            layer_output = stacked_outputs[i]  # [batch_size, num_agvs, embed_dim]
            
            # 计算门控值
            gate_input = torch.cat([layer_output, context], dim=-1)
            gate = self.gates[i](gate_input)  # [batch_size, num_agvs, embed_dim]
            
            # 应用门控
            gated_output = gate * layer_output
            gated_outputs.append(gated_output)
            gate_values.append(gate)
        
        # 堆叠门控输出
        gated_stack = torch.stack(gated_outputs, dim=0)  # [num_layers, batch_size, num_agvs, embed_dim]
        
        # 加权融合
        weights_expanded = weights.unsqueeze(-1).unsqueeze(-1)  # [batch_size, num_layers, 1, 1]
        weights_expanded = weights_expanded.permute(1, 0, 2, 3)  # [num_layers, batch_size, 1, 1]
        
        weighted_gated = gated_stack * weights_expanded
        fused_output = torch.sum(weighted_gated, dim=0)  # [batch_size, num_agvs, embed_dim]
        
        # 最终融合
        fused_output = self.fusion_network(fused_output)
        
        return {
            'fused_output': fused_output,
            'gate_values': gate_values,
            'gated_outputs': gated_outputs,
            'method': 'gated_fusion'
        }


class HierarchicalFusion(nn.Module):
    """层次化融合"""
    
    def __init__(self, embed_dim: int, num_layers: int, dropout: float):
        super(HierarchicalFusion, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_layers = num_layers
        
        # 层次化融合网络
        self.hierarchical_fusers = nn.ModuleList()
        current_layers = num_layers
        
        while current_layers > 1:
            next_layers = (current_layers + 1) // 2
            self.hierarchical_fusers.append(
                nn.Sequential(
                    nn.Linear(embed_dim * 2, embed_dim),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(embed_dim, embed_dim)
                )
            )
            current_layers = next_layers
    
    def forward(self, 
                stacked_outputs: torch.Tensor,
                weights: torch.Tensor) -> Dict[str, torch.Tensor]:
        """层次化融合"""
        num_layers, batch_size, num_agvs, embed_dim = stacked_outputs.shape
        
        # 初始化当前层输出
        current_outputs = [stacked_outputs[i] for i in range(num_layers)]
        current_weights = [weights[:, i] for i in range(num_layers)]
        
        fusion_steps = []
        
        # 层次化融合
        for fuser in self.hierarchical_fusers:
            next_outputs = []
            next_weights = []
            
            # 两两融合
            for i in range(0, len(current_outputs), 2):
                if i + 1 < len(current_outputs):
                    # 融合两个层
                    output1 = current_outputs[i]
                    output2 = current_outputs[i + 1]
                    weight1 = current_weights[i]
                    weight2 = current_weights[i + 1]
                    
                    # 归一化权重
                    total_weight = weight1 + weight2 + 1e-8
                    norm_weight1 = weight1 / total_weight
                    norm_weight2 = weight2 / total_weight
                    
                    # 加权拼接
                    weighted_concat = torch.cat([
                        output1 * norm_weight1.unsqueeze(-1).unsqueeze(-1),
                        output2 * norm_weight2.unsqueeze(-1).unsqueeze(-1)
                    ], dim=-1)
                    
                    # 融合
                    fused = fuser(weighted_concat)
                    next_outputs.append(fused)
                    next_weights.append(weight1 + weight2)
                else:
                    # 奇数个层，直接传递
                    next_outputs.append(current_outputs[i])
                    next_weights.append(current_weights[i])
            
            fusion_steps.append({
                'outputs': next_outputs.copy(),
                'weights': next_weights.copy()
            })
            
            current_outputs = next_outputs
            current_weights = next_weights
        
        # 最终输出
        fused_output = current_outputs[0]
        
        return {
            'fused_output': fused_output,
            'fusion_steps': fusion_steps,
            'method': 'hierarchical_fusion'
        }


class AdaptiveFusion(nn.Module):
    """自适应融合"""
    
    def __init__(self, embed_dim: int, num_layers: int, dropout: float):
        super(AdaptiveFusion, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_layers = num_layers
        
        # 自适应权重网络 - 动态初始化
        self.adaptive_weight_network = None
        
        # 特征变换网络
        self.feature_transforms = nn.ModuleList([
            nn.Sequential(
                nn.Linear(embed_dim, embed_dim),
                nn.ReLU(),
                nn.Linear(embed_dim, embed_dim)
            ) for _ in range(num_layers)
        ])
        
        # 最终融合网络
        self.final_fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, 
                stacked_outputs: torch.Tensor,
                weights: torch.Tensor,
                global_context: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """自适应融合"""
        num_layers, batch_size, num_agvs, embed_dim = stacked_outputs.shape
        
        # 计算自适应权重
        layer_features = torch.mean(stacked_outputs, dim=(0, 2))  # [batch_size, embed_dim]
        all_features = torch.cat([layer_features for _ in range(num_layers)], dim=-1)

        if global_context is not None:
            all_features = torch.cat([all_features, global_context], dim=-1)

        # 动态初始化自适应权重网络
        if self.adaptive_weight_network is None:
            input_dim = all_features.shape[-1]
            self.adaptive_weight_network = nn.Sequential(
                nn.Linear(input_dim, self.embed_dim),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(self.embed_dim, num_layers),
                nn.Softmax(dim=-1)
            ).to(all_features.device)

        adaptive_weights = self.adaptive_weight_network(all_features)  # [batch_size, num_layers]
        
        # 组合静态权重和自适应权重
        combined_weights = 0.7 * weights + 0.3 * adaptive_weights
        combined_weights = F.softmax(combined_weights, dim=-1)
        
        # 特征变换
        transformed_outputs = []
        for i in range(num_layers):
            transformed = self.feature_transforms[i](stacked_outputs[i])
            transformed_outputs.append(transformed)
        
        transformed_stack = torch.stack(transformed_outputs, dim=0)
        
        # 加权融合
        weights_expanded = combined_weights.unsqueeze(-1).unsqueeze(-1)  # [batch_size, num_layers, 1, 1]
        weights_expanded = weights_expanded.permute(1, 0, 2, 3)  # [num_layers, batch_size, 1, 1]
        
        weighted_outputs = transformed_stack * weights_expanded
        fused_output = torch.sum(weighted_outputs, dim=0)  # [batch_size, num_agvs, embed_dim]
        
        # 最终融合
        final_output = self.final_fusion(fused_output)
        
        return {
            'fused_output': final_output,
            'adaptive_weights': adaptive_weights,
            'combined_weights': combined_weights,
            'transformed_outputs': transformed_outputs,
            'method': 'adaptive_fusion'
        }


class AggregationQualityAssessor(nn.Module):
    """聚合质量评估器"""

    def __init__(self, embed_dim: int, num_layers: int):
        super(AggregationQualityAssessor, self).__init__()

        self.embed_dim = embed_dim
        self.num_layers = num_layers

        # 质量评估网络 - 动态初始化
        self.quality_assessor = None
    
    def assess_quality(self, 
                      layer_outputs: torch.Tensor,
                      aggregated_output: torch.Tensor,
                      weights: torch.Tensor) -> Dict[str, torch.Tensor]:
        """评估聚合质量"""
        num_layers, batch_size, num_agvs, embed_dim = layer_outputs.shape
        
        # 计算层间一致性
        layer_similarities = []
        for i in range(num_layers):
            for j in range(i + 1, num_layers):
                sim = F.cosine_similarity(
                    layer_outputs[i].view(batch_size, -1),
                    layer_outputs[j].view(batch_size, -1),
                    dim=-1
                )
                layer_similarities.append(sim)
        
        consistency = torch.mean(torch.stack(layer_similarities), dim=0) if layer_similarities else torch.zeros(batch_size)
        
        # 计算信息保留度
        layer_mean = torch.mean(layer_outputs, dim=0)  # [batch_size, num_agvs, embed_dim]
        information_retention = F.cosine_similarity(
            aggregated_output.view(batch_size, -1),
            layer_mean.view(batch_size, -1),
            dim=-1
        )
        
        # 计算权重分布熵
        weight_entropy = -torch.sum(weights * torch.log(weights + 1e-8), dim=-1)
        weight_entropy = weight_entropy / math.log(num_layers)  # 归一化
        
        # 组合特征进行质量评估
        all_features = torch.cat([
            layer_outputs.view(num_layers, batch_size, -1).permute(1, 0, 2).contiguous().view(batch_size, -1),
            aggregated_output.view(batch_size, -1)
        ], dim=-1)

        # 动态初始化质量评估网络
        if self.quality_assessor is None:
            input_dim = all_features.shape[-1]
            self.quality_assessor = nn.Sequential(
                nn.Linear(input_dim, self.embed_dim),
                nn.ReLU(),
                nn.Linear(self.embed_dim, self.embed_dim // 2),
                nn.ReLU(),
                nn.Linear(self.embed_dim // 2, 4),  # 4个质量指标
                nn.Sigmoid()
            ).to(all_features.device)

        quality_scores = self.quality_assessor(all_features)  # [batch_size, 4]
        
        return {
            'overall_quality': torch.mean(quality_scores, dim=-1),
            'consistency': consistency,
            'information_retention': information_retention,
            'weight_entropy': weight_entropy,
            'diversity_score': quality_scores[:, 0],
            'coherence_score': quality_scores[:, 1],
            'stability_score': quality_scores[:, 2],
            'effectiveness_score': quality_scores[:, 3]
        }
