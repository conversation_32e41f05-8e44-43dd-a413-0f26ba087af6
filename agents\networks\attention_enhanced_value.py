"""
注意力增强价值网络

将注意力机制集成到中心化价值网络，实现全局价值估计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List, Union

from ..attention.task_allocation_attention import TaskAllocationAttention
from ..attention.collaboration_attention import CollaborationAwareAttention
from ..attention.dual_layer_fusion import DualLayerAttentionFusion


class AttentionEnhancedValueNetwork(nn.Module):
    """注意力增强价值网络"""
    
    def __init__(self, 
                 agv_state_dim: int = 10,
                 task_state_dim: int = 11,
                 global_state_shape: Tuple[int, int] = (10, 26),
                 num_agvs: int = 4,
                 num_tasks: int = 10,
                 embed_dim: int = 64,
                 hidden_dims: List[int] = None,
                 use_dual_attention: bool = True,
                 use_global_attention: bool = True,
                 use_value_decomposition: bool = True,
                 activation: str = "relu",
                 layer_norm: bool = True,
                 dropout: float = 0.1):
        """
        初始化注意力增强价值网络
        
        Args:
            agv_state_dim: AGV状态维度
            task_state_dim: 任务状态维度
            global_state_shape: 全局状态形状
            num_agvs: AGV数量
            num_tasks: 任务数量
            embed_dim: 嵌入维度
            hidden_dims: 隐藏层维度列表
            use_dual_attention: 是否使用双层注意力
            use_global_attention: 是否使用全局注意力
            use_value_decomposition: 是否使用价值分解
            activation: 激活函数类型
            layer_norm: 是否使用层归一化
            dropout: Dropout概率
        """
        super(AttentionEnhancedValueNetwork, self).__init__()
        
        self.agv_state_dim = agv_state_dim
        self.task_state_dim = task_state_dim
        self.global_state_shape = global_state_shape
        self.num_agvs = num_agvs
        self.num_tasks = num_tasks
        self.embed_dim = embed_dim
        self.hidden_dims = hidden_dims or [512, 256, 128]
        self.use_dual_attention = use_dual_attention
        self.use_global_attention = use_global_attention
        self.use_value_decomposition = use_value_decomposition
        self.layer_norm = layer_norm
        self.dropout = dropout
        
        # 激活函数
        if activation == "relu":
            self.activation = nn.ReLU()
        elif activation == "tanh":
            self.activation = nn.Tanh()
        elif activation == "gelu":
            self.activation = nn.GELU()
        else:
            self.activation = nn.ReLU()
        
        # 状态编码器
        self.agv_encoder = nn.Sequential(
            nn.Linear(agv_state_dim, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation
        )
        
        self.task_encoder = nn.Sequential(
            nn.Linear(task_state_dim, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 全局状态编码器
        self.global_encoder = nn.Sequential(
            nn.Conv2d(1, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32) if layer_norm else nn.Identity(),
            self.activation,
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.AdaptiveAvgPool2d((4, 4)),
            nn.Flatten(),
            nn.Linear(64 * 4 * 4, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 双层注意力机制
        if use_dual_attention:
            # 任务分配注意力
            self.task_attention = TaskAllocationAttention(
                agv_state_dim=embed_dim,
                task_state_dim=embed_dim,
                embed_dim=embed_dim,
                num_heads=8,
                dropout=dropout
            )
            
            # 协作感知注意力
            self.collaboration_attention = CollaborationAwareAttention(
                embed_dim=embed_dim,
                num_heads=8,
                distance_thresholds=[5.0, 15.0, 30.0],
                use_adaptive_temperature=True,
                use_adaptive_hierarchy=True,
                dropout=dropout
            )
            
            # 双层注意力融合
            self.attention_fusion = DualLayerAttentionFusion(
                embed_dim=embed_dim,
                num_agvs=num_agvs,
                num_tasks=num_tasks,
                fusion_method="adaptive",
                dropout=dropout
            )
        
        # 全局注意力机制
        if use_global_attention:
            self.global_attention = GlobalValueAttention(
                embed_dim=embed_dim,
                num_agvs=num_agvs,
                num_tasks=num_tasks,
                dropout=dropout
            )
        
        # 价值分解模块
        if use_value_decomposition:
            self.value_decomposer = ValueDecomposer(
                embed_dim=embed_dim,
                num_agvs=num_agvs,
                num_tasks=num_tasks,
                dropout=dropout
            )
        
        # 特征融合网络
        fusion_input_dim = self._calculate_fusion_input_dim()
        
        self.feature_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, self.hidden_dims[0]),
            nn.LayerNorm(self.hidden_dims[0]) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout)
        )
        
        # 价值头部网络
        value_layers = []
        input_dim = self.hidden_dims[0]
        
        for hidden_dim in self.hidden_dims[1:]:
            value_layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.LayerNorm(hidden_dim) if layer_norm else nn.Identity(),
                self.activation,
                nn.Dropout(dropout)
            ])
            input_dim = hidden_dim
        
        # 最终价值输出
        value_layers.append(nn.Linear(input_dim, 1))
        self.value_head = nn.Sequential(*value_layers)
        
        # 辅助价值头部（用于价值分解）
        if use_value_decomposition:
            self.individual_value_heads = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(embed_dim, embed_dim // 2),
                    nn.LayerNorm(embed_dim // 2) if layer_norm else nn.Identity(),
                    self.activation,
                    nn.Linear(embed_dim // 2, 1)
                ) for _ in range(num_agvs)
            ])
            
            self.task_value_heads = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(embed_dim, embed_dim // 2),
                    nn.LayerNorm(embed_dim // 2) if layer_norm else nn.Identity(),
                    self.activation,
                    nn.Linear(embed_dim // 2, 1)
                ) for _ in range(num_tasks)
            ])
        
        # 初始化权重
        self._initialize_weights()
    
    def _calculate_fusion_input_dim(self) -> int:
        """计算特征融合输入维度"""
        fusion_dim = self.embed_dim  # 全局特征
        
        if self.use_dual_attention:
            fusion_dim += self.embed_dim  # 注意力融合特征
        else:
            fusion_dim += self.embed_dim * 2  # AGV + 任务特征
        
        if self.use_global_attention:
            fusion_dim += self.embed_dim  # 全局注意力特征
        
        if self.use_value_decomposition:
            fusion_dim += self.embed_dim  # 价值分解特征
        
        return fusion_dim
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Conv2d):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
    
    def forward(self, 
                all_agv_states: torch.Tensor,
                task_states: torch.Tensor,
                global_state: torch.Tensor,
                agv_positions: Optional[torch.Tensor] = None,
                agv_velocities: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            all_agv_states: 所有AGV状态 [batch_size, num_agvs, agv_state_dim]
            task_states: 任务状态 [batch_size, num_tasks, task_state_dim]
            global_state: 全局状态 [batch_size, height, width]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            
        Returns:
            value_output: 价值输出字典
        """
        batch_size = all_agv_states.size(0)
        
        # 编码AGV状态
        agv_states_flat = all_agv_states.view(batch_size * self.num_agvs, -1)
        agv_features = self.agv_encoder(agv_states_flat)
        agv_features = agv_features.view(batch_size, self.num_agvs, self.embed_dim)
        
        # 编码任务状态
        task_states_flat = task_states.view(batch_size * self.num_tasks, -1)
        task_features = self.task_encoder(task_states_flat)
        task_features = task_features.view(batch_size, self.num_tasks, self.embed_dim)
        
        # 编码全局状态
        global_state = global_state.unsqueeze(1)  # [batch_size, 1, height, width]
        global_features = self.global_encoder(global_state)  # [batch_size, embed_dim]
        
        # 双层注意力处理
        attention_features = None
        attention_info = {}
        
        if self.use_dual_attention:
            # 任务分配注意力
            task_attention_result = self.task_attention(
                agv_states=all_agv_states,
                task_states=task_states
            )
            
            # 协作感知注意力
            if agv_positions is not None and agv_velocities is not None:
                collaboration_result = self.collaboration_attention(
                    agv_embeddings=agv_features,
                    agv_positions=agv_positions,
                    agv_velocities=agv_velocities,
                    agv_states=all_agv_states
                )
                
                # 双层注意力融合
                fusion_result = self.attention_fusion(
                    task_attention_output=task_attention_result['final_output'],
                    collaboration_attention_output=collaboration_result['collaboration_output'],
                    agv_states=all_agv_states,
                    task_states=task_states,
                    agv_positions=agv_positions,
                    agv_velocities=agv_velocities,
                    global_context=global_features
                )

                # 聚合所有AGV的注意力特征
                attention_features = torch.mean(fusion_result['fused_output'], dim=1)  # [batch_size, embed_dim]
                
                attention_info = {
                    'task_attention': task_attention_result,
                    'collaboration_attention': collaboration_result,
                    'fusion_result': fusion_result
                }
            else:
                # 只使用任务分配注意力
                attention_features = torch.mean(task_attention_result['final_output'], dim=1)
                attention_info = {'task_attention': task_attention_result}
        
        # 全局注意力处理
        global_attention_features = None
        if self.use_global_attention:
            global_attention_result = self.global_attention(
                agv_features, task_features, global_features
            )
            global_attention_features = global_attention_result['global_value_features']
            attention_info['global_attention'] = global_attention_result
        
        # 价值分解处理
        decomposition_features = None
        individual_values = None
        task_values = None
        
        if self.use_value_decomposition:
            decomposition_result = self.value_decomposer(
                agv_features, task_features, global_features
            )
            decomposition_features = decomposition_result['decomposed_features']
            
            # 计算个体价值和任务价值
            individual_values = []
            for i in range(self.num_agvs):
                agv_value = self.individual_value_heads[i](agv_features[:, i, :])
                individual_values.append(agv_value)
            individual_values = torch.cat(individual_values, dim=1)  # [batch_size, num_agvs]
            
            task_values = []
            for i in range(self.num_tasks):
                task_value = self.task_value_heads[i](task_features[:, i, :])
                task_values.append(task_value)
            task_values = torch.cat(task_values, dim=1)  # [batch_size, num_tasks]
            
            attention_info['value_decomposition'] = decomposition_result
        
        # 特征融合
        fusion_features = [global_features]
        
        if attention_features is not None:
            fusion_features.append(attention_features)
        else:
            # 不使用注意力时的简单聚合
            agv_features_agg = torch.mean(agv_features, dim=1)
            task_features_agg = torch.mean(task_features, dim=1)
            fusion_features.extend([agv_features_agg, task_features_agg])
        
        if global_attention_features is not None:
            fusion_features.append(global_attention_features)
        
        if decomposition_features is not None:
            fusion_features.append(decomposition_features)
        
        fused_features = torch.cat(fusion_features, dim=1)
        fused_features = self.feature_fusion(fused_features)
        
        # 价值估计
        global_value = self.value_head(fused_features)  # [batch_size, 1]
        
        # 组装输出
        output = {
            'values': global_value,
            'global_features': global_features,
            'attention_features': attention_features,
            'attention_info': attention_info
        }
        
        # 添加价值分解信息
        if self.use_value_decomposition:
            output.update({
                'individual_values': individual_values,
                'task_values': task_values,
                'decomposed_features': decomposition_features
            })
        
        return output


class GlobalValueAttention(nn.Module):
    """全局价值注意力机制"""

    def __init__(self, embed_dim: int, num_agvs: int, num_tasks: int, dropout: float):
        super(GlobalValueAttention, self).__init__()

        self.embed_dim = embed_dim
        self.num_agvs = num_agvs
        self.num_tasks = num_tasks

        # 全局注意力网络
        self.global_attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )

        # 价值相关性网络
        self.value_relevance = nn.Sequential(
            nn.Linear(embed_dim * 3, embed_dim),  # AGV + 任务 + 全局
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )

        # 全局价值特征生成器
        self.global_value_generator = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )

    def forward(self,
                agv_features: torch.Tensor,
                task_features: torch.Tensor,
                global_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """全局价值注意力前向传播"""
        batch_size = agv_features.size(0)

        # 组合所有特征
        all_features = torch.cat([agv_features, task_features], dim=1)  # [batch_size, num_agvs+num_tasks, embed_dim]

        # 全局注意力
        global_context = global_features.unsqueeze(1)  # [batch_size, 1, embed_dim]
        attended_features, attention_weights = self.global_attention(
            global_context, all_features, all_features
        )

        # 价值相关性计算
        agv_global = torch.mean(agv_features, dim=1)
        task_global = torch.mean(task_features, dim=1)
        relevance_input = torch.cat([agv_global, task_global, global_features], dim=1)
        value_relevance = self.value_relevance(relevance_input)

        # 生成全局价值特征
        global_value_features = self.global_value_generator(
            attended_features.squeeze(1) + value_relevance
        )

        return {
            'global_value_features': global_value_features,
            'attention_weights': attention_weights,
            'value_relevance': value_relevance,
            'attended_features': attended_features
        }


class ValueDecomposer(nn.Module):
    """价值分解器"""

    def __init__(self, embed_dim: int, num_agvs: int, num_tasks: int, dropout: float):
        super(ValueDecomposer, self).__init__()

        self.embed_dim = embed_dim
        self.num_agvs = num_agvs
        self.num_tasks = num_tasks

        # AGV价值分解网络
        self.agv_decomposer = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim // 2)
        )

        # 任务价值分解网络
        self.task_decomposer = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim // 2)
        )

        # 交互价值网络
        self.interaction_network = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim // 2)
        )

        # 价值融合网络
        self.value_fusion = nn.Sequential(
            nn.Linear(embed_dim // 2 * 3, embed_dim),  # AGV + 任务 + 交互
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )

    def forward(self,
                agv_features: torch.Tensor,
                task_features: torch.Tensor,
                global_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """价值分解前向传播"""
        batch_size = agv_features.size(0)

        # AGV价值分解
        agv_values = self.agv_decomposer(agv_features)  # [batch_size, num_agvs, embed_dim//2]
        agv_value_agg = torch.mean(agv_values, dim=1)  # [batch_size, embed_dim//2]

        # 任务价值分解
        task_values = self.task_decomposer(task_features)  # [batch_size, num_tasks, embed_dim//2]
        task_value_agg = torch.mean(task_values, dim=1)  # [batch_size, embed_dim//2]

        # 交互价值计算
        agv_global = torch.mean(agv_features, dim=1)
        task_global = torch.mean(task_features, dim=1)
        interaction_input = torch.cat([agv_global, task_global], dim=1)
        interaction_values = self.interaction_network(interaction_input)  # [batch_size, embed_dim//2]

        # 价值融合
        decomposed_input = torch.cat([agv_value_agg, task_value_agg, interaction_values], dim=1)
        decomposed_features = self.value_fusion(decomposed_input)

        return {
            'decomposed_features': decomposed_features,
            'agv_values': agv_values,
            'task_values': task_values,
            'interaction_values': interaction_values,
            'agv_value_agg': agv_value_agg,
            'task_value_agg': task_value_agg
        }


class AttentionValueWrapper(nn.Module):
    """注意力价值包装器，用于与现有MAPPO框架集成"""

    def __init__(self, value_network: AttentionEnhancedValueNetwork):
        super(AttentionValueWrapper, self).__init__()

        self.value_network = value_network

    def forward(self,
                obs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        包装器前向传播，适配MAPPO接口

        Args:
            obs: 观测字典，包含：
                - 'all_agv_states': 所有AGV状态
                - 'task_states': 任务状态
                - 'global_state': 全局状态
                - 'agv_positions': AGV位置（可选）
                - 'agv_velocities': AGV速度（可选）

        Returns:
            value_output: 价值输出
        """
        return self.value_network(
            all_agv_states=obs['all_agv_states'],
            task_states=obs['task_states'],
            global_state=obs['global_state'],
            agv_positions=obs.get('agv_positions'),
            agv_velocities=obs.get('agv_velocities')
        )

    def get_value(self, obs: Dict[str, torch.Tensor]) -> torch.Tensor:
        """获取价值估计"""
        output = self.forward(obs)
        return output['values']

    def get_decomposed_values(self, obs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """获取分解的价值估计"""
        output = self.forward(obs)

        result = {'global_value': output['values']}

        if 'individual_values' in output:
            result['individual_values'] = output['individual_values']

        if 'task_values' in output:
            result['task_values'] = output['task_values']

        return result


class HybridValueNetwork(nn.Module):
    """混合价值网络，结合注意力增强和传统方法"""

    def __init__(self,
                 attention_value_net: AttentionEnhancedValueNetwork,
                 traditional_value_net: nn.Module,
                 fusion_weight: float = 0.7):
        super(HybridValueNetwork, self).__init__()

        self.attention_value_net = attention_value_net
        self.traditional_value_net = traditional_value_net
        self.fusion_weight = fusion_weight

        # 价值融合网络
        self.value_fusion = nn.Sequential(
            nn.Linear(2, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )

    def forward(self, obs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """混合价值网络前向传播"""
        # 注意力增强价值
        attention_output = self.attention_value_net(
            all_agv_states=obs['all_agv_states'],
            task_states=obs['task_states'],
            global_state=obs['global_state'],
            agv_positions=obs.get('agv_positions'),
            agv_velocities=obs.get('agv_velocities')
        )
        attention_value = attention_output['values']

        # 传统价值（假设传统网络接受扁平化输入）
        traditional_input = self._flatten_obs(obs)
        traditional_value = self.traditional_value_net(traditional_input)

        # 价值融合
        fusion_input = torch.cat([attention_value, traditional_value], dim=1)
        fused_value = self.value_fusion(fusion_input)

        # 加权融合
        final_value = (self.fusion_weight * attention_value +
                      (1 - self.fusion_weight) * traditional_value)

        return {
            'values': final_value,
            'attention_values': attention_value,
            'traditional_values': traditional_value,
            'fused_values': fused_value,
            'attention_info': attention_output.get('attention_info', {})
        }

    def _flatten_obs(self, obs: Dict[str, torch.Tensor]) -> torch.Tensor:
        """扁平化观测用于传统网络"""
        flattened = []

        # 扁平化AGV状态
        agv_states = obs['all_agv_states'].view(obs['all_agv_states'].size(0), -1)
        flattened.append(agv_states)

        # 扁平化任务状态
        task_states = obs['task_states'].view(obs['task_states'].size(0), -1)
        flattened.append(task_states)

        # 扁平化全局状态
        global_state = obs['global_state'].view(obs['global_state'].size(0), -1)
        flattened.append(global_state)

        return torch.cat(flattened, dim=1)
