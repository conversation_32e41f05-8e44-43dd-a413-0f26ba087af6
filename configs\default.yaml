# 默认配置文件
# 基于融合双层注意力机制的MAPPO多AGV协同调度系统

environment:
  map_width: 26
  map_height: 10
  num_shelves: 15
  shelf_width: 4
  shelf_height: 2
  num_agvs: 4
  num_tasks: 16
  agv_capacity: 25
  task_weights: [5, 10]
  render_mode: "human"
  max_episode_steps: 1000

attention:
  embed_dim: 64
  num_heads: 8
  sparse_k: 8
  temperature: 1.0
  dropout: 0.1
  
  # 约束权重
  lambda_distance: 2.0
  lambda_capacity: 1.0
  lambda_priority: 0.5
  lambda_deadline: 1.5
  
  # 时序一致性
  lambda_temporal: 0.1
  temporal_window: 5

network:
  policy_hidden_dims: [512, 256]
  value_hidden_dims: [512, 256]
  activation: "relu"
  layer_norm: true
  dropout: 0.1

training:
  # PPO参数
  lr: 3.0e-4
  gamma: 0.99
  gae_lambda: 0.95
  clip_param: 0.2
  vf_loss_coeff: 0.5
  entropy_coeff: 0.01
  
  # 训练参数
  num_sgd_iter: 10
  sgd_minibatch_size: 128
  train_batch_size: 4000
  num_workers: 4
  
  # 课程学习
  curriculum_stages:
    - num_agvs: 2
      num_tasks: 8
      episodes: 1000
    - num_agvs: 3
      num_tasks: 12
      episodes: 1500
    - num_agvs: 4
      num_tasks: 16
      episodes: 2000
  
  # 经验回放
  buffer_size: 100000
  prioritized_replay: true
  alpha: 0.6
  beta: 0.4
  
  # 元学习
  meta_learning: true
  inner_lr: 1.0e-3
  meta_lr: 1.0e-4
  num_inner_updates: 5

experiment:
  name: "mappo_dual_attention"
  seed: 42
  num_episodes: 5000
  eval_interval: 100
  save_interval: 500
  log_interval: 10
  
  # 设备配置
  device: "cuda"
  num_gpus: 1
  
  # 输出路径
  checkpoint_dir: "checkpoints"
  log_dir: "logs"
  result_dir: "results"
