"""
网格世界环境核心

实现26×10网格地图、货架布局和基础物理引擎
"""

import numpy as np
from typing import Tuple, List, Dict, Optional, Set
from enum import Enum
import copy


class CellType(Enum):
    """网格单元类型"""
    EMPTY = 0      # 空地
    SHELF = 1      # 货架
    WALL = 2       # 墙壁
    PASSAGE = 3    # 通道
    PICKUP = 4     # 拾取点
    DROPOFF = 5    # 放置点


class GridWorld:
    """网格世界环境核心类"""
    
    def __init__(self, 
                 width: int = 26, 
                 height: int = 10,
                 num_shelves: int = 15,
                 shelf_width: int = 4,
                 shelf_height: int = 2):
        """
        初始化网格世界
        
        Args:
            width: 地图宽度
            height: 地图高度
            num_shelves: 货架数量
            shelf_width: 货架宽度
            shelf_height: 货架高度
        """
        self.width = width
        self.height = height
        self.num_shelves = num_shelves
        self.shelf_width = shelf_width
        self.shelf_height = shelf_height
        
        # 初始化网格
        self.grid = np.zeros((height, width), dtype=int)
        self.cell_types = np.full((height, width), CellType.EMPTY, dtype=object)
        
        # 货架信息
        self.shelves = []  # 存储货架位置信息
        self.shelf_positions = set()  # 货架占用的所有位置
        
        # 通道和可通行区域
        self.passages = set()  # 通道位置
        self.free_spaces = set()  # 可通行位置
        
        # 拾取和放置点
        self.pickup_points = []
        self.dropoff_points = []
        
        # 生成地图
        self._generate_map()
    
    def _generate_map(self):
        """生成地图布局"""
        # 清空网格
        self.grid.fill(0)
        self.cell_types.fill(CellType.EMPTY)
        
        # 生成货架布局（3行5列）
        self._generate_shelves()
        
        # 生成通道
        self._generate_passages()
        
        # 生成拾取和放置点
        self._generate_pickup_dropoff_points()
        
        # 更新可通行区域
        self._update_free_spaces()
    
    def _generate_shelves(self):
        """生成货架布局（3行5列，每个货架4×2）"""
        self.shelves.clear()
        self.shelf_positions.clear()
        
        # 货架布局参数
        rows = 3
        cols = 5
        shelf_spacing_x = 1  # 货架间水平间距（减小间距）
        shelf_spacing_y = 1  # 货架间垂直间距（减小间距）
        
        # 计算起始位置，使货架居中
        start_x = (self.width - (cols * self.shelf_width + (cols - 1) * shelf_spacing_x)) // 2
        start_y = (self.height - (rows * self.shelf_height + (rows - 1) * shelf_spacing_y)) // 2
        
        shelf_id = 0
        for row in range(rows):
            for col in range(cols):
                if shelf_id >= self.num_shelves:
                    break
                
                # 计算货架左上角位置
                shelf_x = start_x + col * (self.shelf_width + shelf_spacing_x)
                shelf_y = start_y + row * (self.shelf_height + shelf_spacing_y)
                
                # 确保货架在地图范围内
                if (shelf_x + self.shelf_width <= self.width and 
                    shelf_y + self.shelf_height <= self.height):
                    
                    shelf_info = {
                        'id': shelf_id,
                        'x': shelf_x,
                        'y': shelf_y,
                        'width': self.shelf_width,
                        'height': self.shelf_height,
                        'positions': []
                    }
                    
                    # 在网格中标记货架
                    for dy in range(self.shelf_height):
                        for dx in range(self.shelf_width):
                            pos_x = shelf_x + dx
                            pos_y = shelf_y + dy
                            
                            if 0 <= pos_x < self.width and 0 <= pos_y < self.height:
                                self.grid[pos_y, pos_x] = CellType.SHELF.value
                                self.cell_types[pos_y, pos_x] = CellType.SHELF
                                self.shelf_positions.add((pos_x, pos_y))
                                shelf_info['positions'].append((pos_x, pos_y))
                    
                    self.shelves.append(shelf_info)
                    shelf_id += 1
            
            if shelf_id >= self.num_shelves:
                break
    
    def _generate_passages(self):
        """生成通道"""
        self.passages.clear()
        
        # 水平通道（货架行之间）
        for y in range(self.height):
            for x in range(self.width):
                if self.cell_types[y, x] == CellType.EMPTY:
                    self.passages.add((x, y))
                    self.cell_types[y, x] = CellType.PASSAGE
                    self.grid[y, x] = CellType.PASSAGE.value
    
    def _generate_pickup_dropoff_points(self):
        """生成拾取和放置点"""
        self.pickup_points.clear()
        self.dropoff_points.clear()
        
        # 在货架周围生成拾取点
        for shelf in self.shelves:
            shelf_x, shelf_y = shelf['x'], shelf['y']
            shelf_w, shelf_h = shelf['width'], shelf['height']
            
            # 货架四周的相邻位置作为拾取点
            adjacent_positions = []

            # 左侧
            adjacent_positions.extend([(shelf_x - 1, shelf_y + dy) for dy in range(shelf_h)])
            # 右侧
            adjacent_positions.extend([(shelf_x + shelf_w, shelf_y + dy) for dy in range(shelf_h)])
            # 上方
            adjacent_positions.extend([(shelf_x + dx, shelf_y - 1) for dx in range(shelf_w)])
            # 下方
            adjacent_positions.extend([(shelf_x + dx, shelf_y + shelf_h) for dx in range(shelf_w)])
            
            for x, y in adjacent_positions:
                if (0 <= x < self.width and 0 <= y < self.height and 
                    self.cell_types[y, x] == CellType.PASSAGE):
                    self.pickup_points.append((x, y))
        
        # 🔧 修改：固定卸货地点在右上角和右下角
        # 右上角位置
        right_top_x = self.width - 1
        right_top_y = 0

        # 右下角位置
        right_bottom_x = self.width - 1
        right_bottom_y = self.height - 1

        # 确保这些位置是可通行的，如果不是则寻找附近的可通行位置
        dropoff_candidates = [
            (right_top_x, right_top_y),      # 右上角
            (right_bottom_x, right_bottom_y)  # 右下角
        ]

        for target_x, target_y in dropoff_candidates:
            # 寻找目标位置附近的可通行位置
            found_position = False
            for search_radius in range(3):  # 搜索半径
                if found_position:
                    break
                for dx in range(-search_radius, search_radius + 1):
                    for dy in range(-search_radius, search_radius + 1):
                        px, py = target_x + dx, target_y + dy
                        if (0 <= px < self.width and 0 <= py < self.height and
                            self.cell_types[py, px] in [CellType.PASSAGE, CellType.EMPTY]):
                            # 设置为卸货点
                            self.dropoff_points.append((px, py))
                            self.cell_types[py, px] = CellType.DROPOFF
                            found_position = True
                            break

        # 如果没有找到合适的位置，使用地图右侧边缘的可通行位置
        if len(self.dropoff_points) < 2:
            for y in range(self.height):
                if (self.cell_types[y, self.width-1] == CellType.PASSAGE and
                    (self.width-1, y) not in self.dropoff_points):
                    self.dropoff_points.append((self.width-1, y))
                    self.cell_types[y, self.width-1] = CellType.DROPOFF
                    if len(self.dropoff_points) >= 2:
                        break
    
    def _update_free_spaces(self):
        """更新可通行区域"""
        self.free_spaces.clear()
        
        for y in range(self.height):
            for x in range(self.width):
                if self.cell_types[y, x] in [CellType.PASSAGE, CellType.PICKUP, CellType.DROPOFF]:
                    self.free_spaces.add((x, y))
                elif self.cell_types[y, x] == CellType.EMPTY:
                    # 空地也可通行
                    self.free_spaces.add((x, y))
    
    def is_valid_position(self, x: int, y: int) -> bool:
        """检查位置是否有效（在地图范围内且可通行）"""
        return (0 <= x < self.width and 
                0 <= y < self.height and 
                (x, y) in self.free_spaces)
    
    def is_occupied_by_shelf(self, x: int, y: int) -> bool:
        """检查位置是否被货架占用"""
        return (x, y) in self.shelf_positions
    
    def get_cell_type(self, x: int, y: int) -> CellType:
        """获取指定位置的单元格类型"""
        if 0 <= x < self.width and 0 <= y < self.height:
            return self.cell_types[y, x]
        return CellType.WALL  # 超出边界视为墙壁
    
    def get_neighbors(self, x: int, y: int, include_diagonal: bool = False) -> List[Tuple[int, int]]:
        """获取相邻位置"""
        neighbors = []
        
        # 四个方向
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]
        
        if include_diagonal:
            # 添加对角线方向
            directions.extend([(1, 1), (1, -1), (-1, 1), (-1, -1)])
        
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            if self.is_valid_position(nx, ny):
                neighbors.append((nx, ny))
        
        return neighbors
    
    def get_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> int:
        """计算曼哈顿距离"""
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])
    
    def find_path(self, start: Tuple[int, int], goal: Tuple[int, int]) -> Optional[List[Tuple[int, int]]]:
        """使用A*算法寻找路径"""
        if not self.is_valid_position(*start) or not self.is_valid_position(*goal):
            return None
        
        if start == goal:
            return [start]
        
        from heapq import heappush, heappop
        
        open_set = [(0, start)]
        came_from = {}
        g_score = {start: 0}
        f_score = {start: self.get_distance(start, goal)}
        
        while open_set:
            current = heappop(open_set)[1]
            
            if current == goal:
                # 重构路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                return path[::-1]
            
            for neighbor in self.get_neighbors(*current):
                tentative_g_score = g_score[current] + 1
                
                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = tentative_g_score + self.get_distance(neighbor, goal)
                    heappush(open_set, (f_score[neighbor], neighbor))
        
        return None  # 无法找到路径
    
    def get_random_free_position(self) -> Tuple[int, int]:
        """获取随机的可通行位置"""
        if not self.free_spaces:
            raise ValueError("没有可通行的位置")
        
        import random
        return random.choice(list(self.free_spaces))
    
    def get_shelf_info(self, shelf_id: int) -> Optional[Dict]:
        """获取货架信息"""
        for shelf in self.shelves:
            if shelf['id'] == shelf_id:
                return shelf
        return None
    
    def to_array(self) -> np.ndarray:
        """返回网格的数组表示"""
        return self.grid.copy()
    
    def __str__(self) -> str:
        """字符串表示"""
        symbols = {
            CellType.EMPTY: '.',
            CellType.SHELF: '#',
            CellType.WALL: 'X',
            CellType.PASSAGE: ' ',
            CellType.PICKUP: 'P',
            CellType.DROPOFF: 'D'
        }
        
        result = []
        for y in range(self.height):
            row = ""
            for x in range(self.width):
                cell_type = self.cell_types[y, x]
                row += symbols.get(cell_type, '?')
            result.append(row)
        
        return '\n'.join(result)
