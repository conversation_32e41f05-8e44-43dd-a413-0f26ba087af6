"""
AGV物理模型

实现AGV的状态表示、运动模型、载重管理和任务队列系统
"""

import numpy as np
from typing import Tuple, List, Optional, Dict, Any
from enum import Enum
import copy
from utils.data_structures import AGVState


class AGVAction(Enum):
    """AGV动作类型"""
    MOVE_UP = 0
    MOVE_DOWN = 1
    MOVE_LEFT = 2
    MOVE_RIGHT = 3
    WAIT = 4
    PICKUP = 5
    DROPOFF = 6


class AGVStatus(Enum):
    """AGV状态类型"""
    IDLE = "idle"
    MOVING = "moving"
    LOADING = "loading"
    UNLOADING = "unloading"
    WAITING = "waiting"


class AGVModel:
    """AGV物理模型类"""
    
    def __init__(self, 
                 agv_id: int,
                 initial_x: int = 0,
                 initial_y: int = 0,
                 capacity: int = 25,
                 max_speed: float = 1.0,
                 pickup_time: int = 2,
                 dropoff_time: int = 2):
        """
        初始化AGV模型
        
        Args:
            agv_id: AGV唯一标识
            initial_x: 初始X坐标
            initial_y: 初始Y坐标
            capacity: 最大载重量
            max_speed: 最大速度
            pickup_time: 拾取时间（步数）
            dropoff_time: 放置时间（步数）
        """
        self.id = agv_id
        self.capacity = capacity
        self.max_speed = max_speed
        self.pickup_time = pickup_time
        self.dropoff_time = dropoff_time
        
        # 位置和运动状态
        self.x = initial_x
        self.y = initial_y
        self.last_x = initial_x
        self.last_y = initial_y
        self.theta = 0.0  # 朝向角度（弧度）
        self.velocity = 0.0
        
        # 载重状态
        self.current_load = 0
        self.carried_tasks = []  # 当前携带的任务ID列表
        
        # 任务队列
        self.task_queue = []  # 待执行任务队列
        self.current_task_id = None  # 当前执行的任务ID
        self.target_position = None  # 目标位置
        
        # 状态管理
        self.status = AGVStatus.IDLE
        self.action_timer = 0  # 动作计时器（用于pickup/dropoff）
        self.last_action = AGVAction.WAIT
        
        # 路径规划
        self.planned_path = []  # 规划的路径
        self.path_index = 0  # 当前路径索引
        
        # 性能统计
        self.total_distance = 0
        self.total_tasks_completed = 0
        self.total_wait_time = 0
        self.collision_count = 0
    
    @property
    def position(self) -> Tuple[int, int]:
        """当前位置"""
        return (self.x, self.y)
    
    @property
    def last_position(self) -> Tuple[int, int]:
        """上一个位置"""
        return (self.last_x, self.last_y)
    
    @property
    def is_idle(self) -> bool:
        """是否空闲"""
        return self.status == AGVStatus.IDLE
    
    @property
    def is_moving(self) -> bool:
        """是否在移动"""
        return self.status == AGVStatus.MOVING
    
    @property
    def is_busy(self) -> bool:
        """是否忙碌（执行任务中）"""
        return self.status in [AGVStatus.LOADING, AGVStatus.UNLOADING]
    
    @property
    def is_full(self) -> bool:
        """是否满载"""
        return self.current_load >= self.capacity
    
    @property
    def available_capacity(self) -> int:
        """可用载重量"""
        return self.capacity - self.current_load
    
    def can_take_task(self, task_weight: int) -> bool:
        """检查是否能承担新任务"""
        return self.current_load + task_weight <= self.capacity
    
    def add_task_to_queue(self, task_id: int):
        """添加任务到队列"""
        if task_id not in self.task_queue:
            self.task_queue.append(task_id)
    
    def remove_task_from_queue(self, task_id: int):
        """从队列中移除任务"""
        if task_id in self.task_queue:
            self.task_queue.remove(task_id)
    
    def get_next_task(self) -> Optional[int]:
        """获取下一个任务"""
        if self.task_queue:
            return self.task_queue[0]
        return None
    
    def start_task(self, task_id: int):
        """开始执行任务"""
        if task_id in self.task_queue:
            self.task_queue.remove(task_id)
        self.current_task_id = task_id
        self.status = AGVStatus.MOVING
    
    def complete_current_task(self):
        """完成当前任务"""
        if self.current_task_id is not None:
            self.total_tasks_completed += 1
            self.current_task_id = None
            self.target_position = None
            self.planned_path.clear()
            self.path_index = 0
            
            if not self.task_queue:
                self.status = AGVStatus.IDLE
    
    def pickup_task(self, task_weight: int) -> bool:
        """拾取任务"""
        if not self.can_take_task(task_weight):
            return False
        
        self.current_load += task_weight
        if self.current_task_id is not None:
            self.carried_tasks.append(self.current_task_id)
        
        self.status = AGVStatus.LOADING
        self.action_timer = self.pickup_time
        return True
    
    def dropoff_task(self, task_id: int, task_weight: int) -> bool:
        """放置任务"""
        if task_id not in self.carried_tasks:
            return False
        
        self.current_load -= task_weight
        self.carried_tasks.remove(task_id)
        
        self.status = AGVStatus.UNLOADING
        self.action_timer = self.dropoff_time
        return True
    
    def set_target_position(self, target_x: int, target_y: int):
        """设置目标位置"""
        self.target_position = (target_x, target_y)
    
    def set_planned_path(self, path: List[Tuple[int, int]]):
        """设置规划路径"""
        self.planned_path = path.copy()
        self.path_index = 0
    
    def get_next_position_from_path(self) -> Optional[Tuple[int, int]]:
        """从规划路径获取下一个位置"""
        if self.path_index < len(self.planned_path):
            next_pos = self.planned_path[self.path_index]
            self.path_index += 1
            return next_pos
        return None
    
    def execute_action(self, action: AGVAction) -> bool:
        """执行动作"""
        self.last_action = action
        
        # 如果正在执行pickup/dropoff，需要等待完成
        if self.action_timer > 0:
            self.action_timer -= 1
            if self.action_timer == 0:
                if self.status == AGVStatus.LOADING:
                    self.status = AGVStatus.MOVING
                elif self.status == AGVStatus.UNLOADING:
                    self.complete_current_task()
            return True
        
        # 保存当前位置
        self.last_x, self.last_y = self.x, self.y
        
        # 执行移动动作
        if action == AGVAction.MOVE_UP:
            return self._move(0, -1)
        elif action == AGVAction.MOVE_DOWN:
            return self._move(0, 1)
        elif action == AGVAction.MOVE_LEFT:
            return self._move(-1, 0)
        elif action == AGVAction.MOVE_RIGHT:
            return self._move(1, 0)
        elif action == AGVAction.WAIT:
            self.status = AGVStatus.WAITING
            self.total_wait_time += 1
            return True
        elif action == AGVAction.PICKUP:
            # pickup动作需要外部环境处理
            return True
        elif action == AGVAction.DROPOFF:
            # dropoff动作需要外部环境处理
            return True
        
        return False
    
    def _move(self, dx: int, dy: int) -> bool:
        """执行移动"""
        new_x = self.x + dx
        new_y = self.y + dy
        
        # 更新位置（实际的有效性检查由环境负责）
        self.x = new_x
        self.y = new_y
        
        # 更新朝向
        if dx != 0 or dy != 0:
            self.theta = np.arctan2(dy, dx)
            self.velocity = self.max_speed
            self.status = AGVStatus.MOVING
            
            # 更新总距离
            self.total_distance += 1
        else:
            self.velocity = 0.0
        
        return True
    
    def revert_move(self):
        """撤销移动（用于碰撞处理）"""
        self.x, self.y = self.last_x, self.last_y
        self.velocity = 0.0
        self.collision_count += 1
    
    def get_state_vector(self) -> np.ndarray:
        """获取状态向量表示"""
        status_encoding = {
            AGVStatus.IDLE: 0,
            AGVStatus.MOVING: 1,
            AGVStatus.LOADING: 2,
            AGVStatus.UNLOADING: 3,
            AGVStatus.WAITING: 4
        }
        
        return np.array([
            self.x, self.y,  # 位置
            self.theta, self.velocity,  # 朝向和速度
            self.current_load, len(self.task_queue),  # 载重和任务队列长度
            self.current_task_id if self.current_task_id is not None else -1,  # 当前任务
            status_encoding[self.status],  # 状态编码
            1 if self.target_position is not None else 0,  # 是否有目标
            len(self.carried_tasks)  # 携带任务数量
        ], dtype=np.float32)
    
    def get_state_dict(self) -> Dict[str, Any]:
        """获取状态字典"""
        return {
            'id': self.id,
            'position': self.position,
            'last_position': self.last_position,
            'theta': self.theta,
            'velocity': self.velocity,
            'current_load': self.current_load,
            'capacity': self.capacity,
            'available_capacity': self.available_capacity,
            'task_queue': self.task_queue.copy(),
            'current_task_id': self.current_task_id,
            'target_position': self.target_position,
            'status': self.status.value,
            'carried_tasks': self.carried_tasks.copy(),
            'is_idle': self.is_idle,
            'is_moving': self.is_moving,
            'is_busy': self.is_busy,
            'is_full': self.is_full,
            'action_timer': self.action_timer,
            'last_action': self.last_action.value,
            'total_distance': self.total_distance,
            'total_tasks_completed': self.total_tasks_completed,
            'total_wait_time': self.total_wait_time,
            'collision_count': self.collision_count
        }
    
    def reset(self, initial_x: int = 0, initial_y: int = 0):
        """重置AGV状态"""
        self.x = initial_x
        self.y = initial_y
        self.last_x = initial_x
        self.last_y = initial_y
        self.theta = 0.0
        self.velocity = 0.0
        
        self.current_load = 0
        self.carried_tasks.clear()
        self.task_queue.clear()
        self.current_task_id = None
        self.target_position = None
        
        self.status = AGVStatus.IDLE
        self.action_timer = 0
        self.last_action = AGVAction.WAIT
        
        self.planned_path.clear()
        self.path_index = 0
        
        # 保留性能统计（可选择性重置）
        # self.total_distance = 0
        # self.total_tasks_completed = 0
        # self.total_wait_time = 0
        # self.collision_count = 0
    
    def copy(self) -> 'AGVModel':
        """创建AGV模型的副本"""
        new_agv = AGVModel(
            agv_id=self.id,
            initial_x=self.x,
            initial_y=self.y,
            capacity=self.capacity,
            max_speed=self.max_speed,
            pickup_time=self.pickup_time,
            dropoff_time=self.dropoff_time
        )
        
        # 复制所有状态
        new_agv.last_x = self.last_x
        new_agv.last_y = self.last_y
        new_agv.theta = self.theta
        new_agv.velocity = self.velocity
        new_agv.current_load = self.current_load
        new_agv.carried_tasks = self.carried_tasks.copy()
        new_agv.task_queue = self.task_queue.copy()
        new_agv.current_task_id = self.current_task_id
        new_agv.target_position = self.target_position
        new_agv.status = self.status
        new_agv.action_timer = self.action_timer
        new_agv.last_action = self.last_action
        new_agv.planned_path = self.planned_path.copy()
        new_agv.path_index = self.path_index
        new_agv.total_distance = self.total_distance
        new_agv.total_tasks_completed = self.total_tasks_completed
        new_agv.total_wait_time = self.total_wait_time
        new_agv.collision_count = self.collision_count
        
        return new_agv
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"AGV{self.id}@({self.x},{self.y}) "
                f"Load:{self.current_load}/{self.capacity} "
                f"Status:{self.status.value} "
                f"Tasks:{len(self.task_queue)}")
    
    def __repr__(self) -> str:
        return self.__str__()
