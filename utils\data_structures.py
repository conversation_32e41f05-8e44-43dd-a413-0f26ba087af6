"""
数据结构工具

提供项目中使用的各种数据结构
"""

import numpy as np
import torch
from typing import List, Dict, Any, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from collections import deque, defaultdict
import heapq
import random


@dataclass
class AGVState:
    """AGV状态数据结构"""
    id: int
    x: int
    y: int
    theta: float = 0.0  # 朝向角度
    velocity: float = 0.0
    load: int = 0  # 当前载重
    capacity: int = 25  # 最大载重
    target_task_id: Optional[int] = None
    task_queue: List[int] = None
    status: str = "idle"  # idle, moving, loading, unloading
    
    def __post_init__(self):
        if self.task_queue is None:
            self.task_queue = []
    
    @property
    def position(self) -> Tuple[int, int]:
        return (self.x, self.y)
    
    @property
    def is_idle(self) -> bool:
        return self.status == "idle"
    
    @property
    def is_full(self) -> bool:
        return self.load >= self.capacity
    
    def can_take_task(self, task_weight: int) -> bool:
        """检查是否能承担新任务"""
        return self.load + task_weight <= self.capacity
    
    def add_task(self, task_id: int):
        """添加任务到队列"""
        if task_id not in self.task_queue:
            self.task_queue.append(task_id)
    
    def remove_task(self, task_id: int):
        """从队列中移除任务"""
        if task_id in self.task_queue:
            self.task_queue.remove(task_id)
    
    def to_vector(self) -> np.ndarray:
        """转换为向量表示"""
        return np.array([
            self.x, self.y, self.theta, self.velocity,
            self.load, len(self.task_queue),
            self.target_task_id if self.target_task_id is not None else -1,
            1 if self.is_idle else 0
        ], dtype=np.float32)


@dataclass
class TaskState:
    """任务状态数据结构"""
    id: int
    x: int
    y: int
    weight: int
    priority: float = 1.0
    deadline: Optional[int] = None
    status: str = "unassigned"  # unassigned, assigned, in_progress, completed
    assigned_agv_id: Optional[int] = None
    creation_time: int = 0
    completion_time: Optional[int] = None
    
    @property
    def position(self) -> Tuple[int, int]:
        return (self.x, self.y)
    
    @property
    def is_available(self) -> bool:
        return self.status == "unassigned"
    
    @property
    def is_completed(self) -> bool:
        return self.status == "completed"
    
    def assign_to_agv(self, agv_id: int):
        """分配给AGV"""
        self.assigned_agv_id = agv_id
        self.status = "assigned"
    
    def start_execution(self):
        """开始执行"""
        self.status = "in_progress"
    
    def complete(self, completion_time: int):
        """完成任务"""
        self.status = "completed"
        self.completion_time = completion_time
    
    def to_vector(self) -> np.ndarray:
        """转换为向量表示"""
        status_encoding = {
            "unassigned": 0, "assigned": 1, 
            "in_progress": 2, "completed": 3
        }
        
        return np.array([
            self.x, self.y, self.weight, self.priority,
            self.deadline if self.deadline is not None else -1,
            status_encoding[self.status],
            self.assigned_agv_id if self.assigned_agv_id is not None else -1
        ], dtype=np.float32)


class Experience(NamedTuple):
    """经验样本数据结构"""
    state: Dict[str, torch.Tensor]
    action: torch.Tensor
    reward: torch.Tensor
    next_state: Dict[str, torch.Tensor]
    done: torch.Tensor
    log_prob: torch.Tensor
    value: torch.Tensor
    advantage: Optional[torch.Tensor] = None
    return_: Optional[torch.Tensor] = None
    attention_weights: Optional[Dict[str, torch.Tensor]] = None


class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        self.priorities = deque(maxlen=capacity)
    
    def push(self, experience: Experience, priority: float = 1.0):
        """添加经验"""
        self.buffer.append(experience)
        self.priorities.append(priority)
    
    def sample(self, batch_size: int, prioritized: bool = False) -> List[Experience]:
        """采样经验"""
        if prioritized and len(self.priorities) > 0:
            # 优先级采样
            priorities = np.array(self.priorities)
            probabilities = priorities / priorities.sum()
            indices = np.random.choice(len(self.buffer), batch_size, p=probabilities)
            return [self.buffer[i] for i in indices]
        else:
            # 随机采样
            return random.sample(self.buffer, min(batch_size, len(self.buffer)))
    
    def update_priorities(self, indices: List[int], priorities: List[float]):
        """更新优先级"""
        for idx, priority in zip(indices, priorities):
            if 0 <= idx < len(self.priorities):
                self.priorities[idx] = priority
    
    def __len__(self):
        return len(self.buffer)
    
    def clear(self):
        """清空缓冲区"""
        self.buffer.clear()
        self.priorities.clear()


class PriorityQueue:
    """优先级队列"""
    
    def __init__(self):
        self._queue = []
        self._index = 0
    
    def push(self, item: Any, priority: float):
        """添加元素"""
        heapq.heappush(self._queue, (-priority, self._index, item))
        self._index += 1
    
    def pop(self) -> Any:
        """弹出最高优先级元素"""
        if self._queue:
            return heapq.heappop(self._queue)[-1]
        raise IndexError("pop from empty priority queue")
    
    def peek(self) -> Any:
        """查看最高优先级元素"""
        if self._queue:
            return self._queue[0][-1]
        raise IndexError("peek from empty priority queue")
    
    def __len__(self):
        return len(self._queue)
    
    def __bool__(self):
        return bool(self._queue)


class CircularBuffer:
    """循环缓冲区"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.buffer = [None] * capacity
        self.head = 0
        self.size = 0
    
    def push(self, item: Any):
        """添加元素"""
        self.buffer[self.head] = item
        self.head = (self.head + 1) % self.capacity
        if self.size < self.capacity:
            self.size += 1
    
    def get_all(self) -> List[Any]:
        """获取所有元素"""
        if self.size == 0:
            return []
        
        if self.size < self.capacity:
            return self.buffer[:self.size]
        else:
            return self.buffer[self.head:] + self.buffer[:self.head]
    
    def get_latest(self, n: int) -> List[Any]:
        """获取最新的n个元素"""
        all_items = self.get_all()
        return all_items[-n:] if len(all_items) >= n else all_items
    
    def __len__(self):
        return self.size
    
    def clear(self):
        """清空缓冲区"""
        self.head = 0
        self.size = 0


class MovingAverage:
    """移动平均计算器"""
    
    def __init__(self, window_size: int):
        self.window_size = window_size
        self.values = deque(maxlen=window_size)
    
    def update(self, value: float):
        """更新值"""
        self.values.append(value)
    
    def get_average(self) -> float:
        """获取平均值"""
        if not self.values:
            return 0.0
        return sum(self.values) / len(self.values)
    
    def get_std(self) -> float:
        """获取标准差"""
        if len(self.values) < 2:
            return 0.0
        
        mean = self.get_average()
        variance = sum((x - mean) ** 2 for x in self.values) / len(self.values)
        return variance ** 0.5
    
    def __len__(self):
        return len(self.values)


class EpisodeBuffer:
    """Episode缓冲区"""
    
    def __init__(self):
        self.states = []
        self.actions = []
        self.rewards = []
        self.log_probs = []
        self.values = []
        self.dones = []
        self.attention_weights = []
    
    def push(self, state: Dict[str, torch.Tensor], 
             action: torch.Tensor,
             reward: float,
             log_prob: torch.Tensor,
             value: torch.Tensor,
             done: bool,
             attention_weights: Optional[Dict[str, torch.Tensor]] = None):
        """添加步骤数据"""
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.log_probs.append(log_prob)
        self.values.append(value)
        self.dones.append(done)
        self.attention_weights.append(attention_weights)
    
    def get_batch(self) -> Dict[str, torch.Tensor]:
        """获取批量数据"""
        # 合并状态字典
        batch_states = {}
        for key in self.states[0].keys():
            batch_states[key] = torch.stack([s[key] for s in self.states])
        
        batch = {
            'states': batch_states,
            'actions': torch.stack(self.actions),
            'rewards': torch.tensor(self.rewards, dtype=torch.float32),
            'log_probs': torch.stack(self.log_probs),
            'values': torch.stack(self.values),
            'dones': torch.tensor(self.dones, dtype=torch.bool)
        }
        
        # 添加注意力权重（如果有）
        if self.attention_weights[0] is not None:
            batch_attention = {}
            for key in self.attention_weights[0].keys():
                batch_attention[key] = torch.stack([aw[key] for aw in self.attention_weights])
            batch['attention_weights'] = batch_attention
        
        return batch
    
    def clear(self):
        """清空缓冲区"""
        self.states.clear()
        self.actions.clear()
        self.rewards.clear()
        self.log_probs.clear()
        self.values.clear()
        self.dones.clear()
        self.attention_weights.clear()
    
    def __len__(self):
        return len(self.states)


class MultiAgentBatch:
    """多智能体批量数据"""
    
    def __init__(self, num_agents: int):
        self.num_agents = num_agents
        self.agent_buffers = [EpisodeBuffer() for _ in range(num_agents)]
    
    def push(self, agent_id: int, **kwargs):
        """为特定智能体添加数据"""
        self.agent_buffers[agent_id].push(**kwargs)
    
    def get_batch(self) -> Dict[str, torch.Tensor]:
        """获取多智能体批量数据"""
        agent_batches = [buffer.get_batch() for buffer in self.agent_buffers]
        
        # 合并所有智能体的数据
        combined_batch = {}
        for key in agent_batches[0].keys():
            if key == 'states':
                # 状态需要特殊处理
                combined_states = {}
                for state_key in agent_batches[0]['states'].keys():
                    combined_states[state_key] = torch.cat([
                        batch['states'][state_key] for batch in agent_batches
                    ], dim=1)  # 在智能体维度上拼接
                combined_batch[key] = combined_states
            else:
                combined_batch[key] = torch.cat([
                    batch[key] for batch in agent_batches
                ], dim=1)
        
        return combined_batch
    
    def clear(self):
        """清空所有缓冲区"""
        for buffer in self.agent_buffers:
            buffer.clear()
    
    def __len__(self):
        return len(self.agent_buffers[0]) if self.agent_buffers else 0
