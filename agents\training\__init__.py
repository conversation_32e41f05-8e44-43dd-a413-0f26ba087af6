"""
训练优化策略模块

包含课程学习、优先级经验回放、训练稳定性保证、环境适应和元学习等训练优化策略
"""

from .curriculum_learning import (
    CurriculumLearningManager, CurriculumConfig, CurriculumStage, DifficultyLevel,
    AdaptiveCurriculumScheduler
)

from .prioritized_replay import (
    MultiAgentPrioritizedReplayBuffer, PrioritizedReplayConfig, Experience,
    SumTree, MultiAgentTDErrorCalculator, AdaptivePriorityScheduler
)

from .stability_manager import (
    TrainingStabilityManager, StabilityConfig, StabilityLevel,
    GradientClipper, AnomalyDetector, LearningRateScheduler
)

from .environment_adaptation import (
    EnvironmentChangeDetector, AdaptationStrategyManager, AdaptationConfig,
    ChangeType, AdaptationStrategy
)

from .maml_integration import (
    MAMLLearner, MAMLTaskSampler, MAMLTask, MAMLConfig
)

from .optimization_manager import (
    TrainingOptimizationManager, OptimizationConfig
)

from .attention_loss import (
    AttentionEnhancedLoss, AttentionRegularizer, TemporalConsistencyLoss
)

__all__ = [
    # 课程学习
    'CurriculumLearningManager', 'CurriculumConfig', 'CurriculumStage', 'DifficultyLevel',
    'AdaptiveCurriculumScheduler',
    
    # 优先级经验回放
    'MultiAgentPrioritizedReplayBuffer', 'PrioritizedReplayConfig', 'Experience',
    'SumTree', 'MultiAgentTDErrorCalculator', 'AdaptivePriorityScheduler',
    
    # 训练稳定性
    'TrainingStabilityManager', 'StabilityConfig', 'StabilityLevel',
    'GradientClipper', 'AnomalyDetector', 'LearningRateScheduler',
    
    # 环境适应
    'EnvironmentChangeDetector', 'AdaptationStrategyManager', 'AdaptationConfig',
    'ChangeType', 'AdaptationStrategy',
    
    # MAML元学习
    'MAMLLearner', 'MAMLTaskSampler', 'MAMLTask', 'MAMLConfig',
    
    # 集成管理
    'TrainingOptimizationManager', 'OptimizationConfig',
    
    # 注意力损失
    'AttentionEnhancedLoss', 'AttentionRegularizer', 'TemporalConsistencyLoss'
]
