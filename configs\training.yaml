# 训练专用配置文件
# 针对训练过程优化的参数设置

environment:
  num_agvs: 4
  num_tasks: 16
  max_episode_steps: 800  # 训练时缩短episode长度

attention:
  embed_dim: 64
  num_heads: 4  # 训练时减少头数以加速
  sparse_k: 6   # 更稀疏的注意力
  dropout: 0.2  # 增加dropout防止过拟合

network:
  policy_hidden_dims: [256, 128]  # 较小的网络
  value_hidden_dims: [256, 128]
  dropout: 0.2

training:
  lr: 5.0e-4  # 稍高的学习率
  num_sgd_iter: 8
  sgd_minibatch_size: 64
  train_batch_size: 2000
  
  # 更激进的课程学习
  curriculum_stages:
    - num_agvs: 2
      num_tasks: 6
      episodes: 500
    - num_agvs: 3
      num_tasks: 10
      episodes: 800
    - num_agvs: 4
      num_tasks: 16
      episodes: 1200

experiment:
  name: "training_run"
  eval_interval: 50
  save_interval: 200
  log_interval: 5
