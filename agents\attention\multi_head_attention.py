"""
多头注意力机制基础模块

实现高效的多头注意力机制，作为所有注意力组件的基础
支持掩码、相对位置编码和自适应温度等高级特性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List, Union
from enum import Enum
import math


class AttentionType(Enum):
    """注意力类型枚举"""
    SELF_ATTENTION = "self"
    CROSS_ATTENTION = "cross"
    CAUSAL_ATTENTION = "causal"


class MultiHeadAttention(nn.Module):
    """多头注意力机制"""

    def __init__(self,
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 bias: bool = True,
                 attention_type: AttentionType = AttentionType.SELF_ATTENTION,
                 use_relative_position: bool = False,
                 max_relative_position: int = 32,
                 use_adaptive_temperature: bool = False,
                 temperature_init: float = 1.0,
                 kdim: Optional[int] = None,
                 vdim: Optional[int] = None):
        """
        初始化多头注意力机制

        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            dropout: Dropout概率
            bias: 是否使用偏置
            attention_type: 注意力类型
            use_relative_position: 是否使用相对位置编码
            max_relative_position: 最大相对位置
            use_adaptive_temperature: 是否使用自适应温度
            temperature_init: 温度初始值
            kdim: 键的维度
            vdim: 值的维度
        """
        super(MultiHeadAttention, self).__init__()

        assert embed_dim % num_heads == 0, f"embed_dim ({embed_dim}) must be divisible by num_heads ({num_heads})"

        self.embed_dim = embed_dim
        self.kdim = kdim if kdim is not None else embed_dim
        self.vdim = vdim if vdim is not None else embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.attention_type = attention_type
        self.use_relative_position = use_relative_position
        self.max_relative_position = max_relative_position
        self.use_adaptive_temperature = use_adaptive_temperature
        
        # 线性投影层
        self.q_proj_weight = nn.Parameter(torch.empty(embed_dim, embed_dim))
        self.k_proj_weight = nn.Parameter(torch.empty(embed_dim, self.kdim))
        self.v_proj_weight = nn.Parameter(torch.empty(embed_dim, self.vdim))
        self.out_proj = nn.Linear(embed_dim, embed_dim, bias=bias)
        
        # 偏置参数
        if bias:
            self.in_proj_bias = nn.Parameter(torch.empty(3 * embed_dim))
        else:
            self.register_parameter('in_proj_bias', None)
        
        # 键值偏置
        if add_bias_kv:
            self.bias_k = nn.Parameter(torch.empty(1, 1, embed_dim))
            self.bias_v = nn.Parameter(torch.empty(1, 1, embed_dim))
        else:
            self.bias_k = self.bias_v = None
        
        self.add_zero_attn = add_zero_attn
        
        # 初始化权重
        self._reset_parameters()
    
    def _reset_parameters(self):
        """初始化参数"""
        nn.init.xavier_uniform_(self.q_proj_weight)
        nn.init.xavier_uniform_(self.k_proj_weight)
        nn.init.xavier_uniform_(self.v_proj_weight)
        
        if self.in_proj_bias is not None:
            nn.init.constant_(self.in_proj_bias, 0.)
        nn.init.constant_(self.out_proj.bias, 0.)
        
        if self.bias_k is not None:
            nn.init.xavier_normal_(self.bias_k)
        if self.bias_v is not None:
            nn.init.xavier_normal_(self.bias_v)
    
    def forward(self, 
                query: torch.Tensor,
                key: torch.Tensor,
                value: torch.Tensor,
                key_padding_mask: Optional[torch.Tensor] = None,
                need_weights: bool = True,
                attn_mask: Optional[torch.Tensor] = None,
                average_attn_weights: bool = True) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播
        
        Args:
            query: 查询张量 [tgt_len, batch_size, embed_dim]
            key: 键张量 [src_len, batch_size, kdim]
            value: 值张量 [src_len, batch_size, vdim]
            key_padding_mask: 键填充掩码 [batch_size, src_len]
            need_weights: 是否需要返回注意力权重
            attn_mask: 注意力掩码 [tgt_len, src_len]
            average_attn_weights: 是否平均注意力权重
            
        Returns:
            attn_output: 注意力输出 [tgt_len, batch_size, embed_dim]
            attn_output_weights: 注意力权重（可选）
        """
        return F.multi_head_attention_forward(
            query, key, value,
            self.embed_dim, self.num_heads,
            self.in_proj_bias, self.bias_k, self.bias_v,
            self.add_zero_attn, self.dropout, self.out_proj.weight, self.out_proj.bias,
            training=self.training,
            key_padding_mask=key_padding_mask,
            need_weights=need_weights,
            attn_mask=attn_mask,
            use_separate_proj_weight=True,
            q_proj_weight=self.q_proj_weight,
            k_proj_weight=self.k_proj_weight,
            v_proj_weight=self.v_proj_weight,
            average_attn_weights=average_attn_weights
        )


class TaskAllocationMultiHeadAttention(nn.Module):
    """任务分配专用多头注意力"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 temperature: float = 1.0,
                 use_layer_norm: bool = True,
                 use_residual: bool = True):
        """
        初始化任务分配多头注意力
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            dropout: Dropout概率
            temperature: 温度参数
            use_layer_norm: 是否使用层归一化
            use_residual: 是否使用残差连接
        """
        super(TaskAllocationMultiHeadAttention, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.temperature = temperature
        self.use_layer_norm = use_layer_norm
        self.use_residual = use_residual
        
        assert self.head_dim * num_heads == embed_dim, "embed_dim必须能被num_heads整除"
        
        # 查询、键、值投影
        self.q_linear = nn.Linear(embed_dim, embed_dim)
        self.k_linear = nn.Linear(embed_dim, embed_dim)
        self.v_linear = nn.Linear(embed_dim, embed_dim)
        
        # 输出投影
        self.out_linear = nn.Linear(embed_dim, embed_dim)
        
        # 层归一化
        if use_layer_norm:
            self.layer_norm = nn.LayerNorm(embed_dim)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in [self.q_linear, self.k_linear, self.v_linear, self.out_linear]:
            nn.init.xavier_uniform_(module.weight)
            nn.init.constant_(module.bias, 0.)
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                task_embeddings: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            task_embeddings: 任务嵌入 [batch_size, num_tasks, embed_dim]
            attention_mask: 注意力掩码 [batch_size, num_agvs, num_tasks]
            
        Returns:
            attended_agv_features: 注意力增强的AGV特征 [batch_size, num_agvs, embed_dim]
            attention_weights: 注意力权重 [batch_size, num_heads, num_agvs, num_tasks]
        """
        batch_size, num_agvs, embed_dim = agv_embeddings.shape
        num_tasks = task_embeddings.shape[1]
        
        # 计算查询、键、值
        Q = self.q_linear(agv_embeddings)  # [batch_size, num_agvs, embed_dim]
        K = self.k_linear(task_embeddings)  # [batch_size, num_tasks, embed_dim]
        V = self.v_linear(task_embeddings)  # [batch_size, num_tasks, embed_dim]
        
        # 重塑为多头格式
        Q = Q.view(batch_size, num_agvs, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, num_tasks, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, num_tasks, self.num_heads, self.head_dim).transpose(1, 2)
        # 形状: [batch_size, num_heads, seq_len, head_dim]
        
        # 计算注意力分数
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / (math.sqrt(self.head_dim) * self.temperature)
        # 形状: [batch_size, num_heads, num_agvs, num_tasks]
        
        # 应用注意力掩码
        if attention_mask is not None:
            # 扩展掩码维度以匹配多头注意力
            if attention_mask.dim() == 3:
                attention_mask = attention_mask.unsqueeze(1)  # [batch_size, 1, num_agvs, num_tasks]
            
            attention_scores = attention_scores.masked_fill(
                attention_mask == 0, float('-inf')
            )
        
        # 计算注意力权重
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        attended_values = torch.matmul(attention_weights, V)
        # 形状: [batch_size, num_heads, num_agvs, head_dim]
        
        # 重塑输出
        attended_values = attended_values.transpose(1, 2).contiguous().view(
            batch_size, num_agvs, embed_dim
        )
        
        # 输出投影
        output = self.out_linear(attended_values)
        
        # 残差连接
        if self.use_residual:
            output = output + agv_embeddings
        
        # 层归一化
        if self.use_layer_norm:
            output = self.layer_norm(output)
        
        return output, attention_weights


class HierarchicalMultiHeadAttention(nn.Module):
    """层次化多头注意力"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads_list: List[int] = [4, 8, 16],
                 dropout: float = 0.1,
                 fusion_method: str = "concat"):
        """
        初始化层次化多头注意力
        
        Args:
            embed_dim: 嵌入维度
            num_heads_list: 不同层次的注意力头数列表
            dropout: Dropout概率
            fusion_method: 融合方法 ("concat", "add", "weighted")
        """
        super(HierarchicalMultiHeadAttention, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_heads_list = num_heads_list
        self.num_levels = len(num_heads_list)
        self.fusion_method = fusion_method
        
        # 创建多个层次的注意力层
        self.attention_layers = nn.ModuleList([
            TaskAllocationMultiHeadAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout
            ) for num_heads in num_heads_list
        ])
        
        # 融合层
        if fusion_method == "concat":
            self.fusion_layer = nn.Sequential(
                nn.Linear(embed_dim * self.num_levels, embed_dim),
                nn.LayerNorm(embed_dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            )
        elif fusion_method == "weighted":
            self.level_weights = nn.Parameter(torch.ones(self.num_levels) / self.num_levels)
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim)
        )
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                task_embeddings: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """
        前向传播
        
        Args:
            agv_embeddings: AGV嵌入
            task_embeddings: 任务嵌入
            attention_mask: 注意力掩码
            
        Returns:
            fused_output: 融合后的输出
            attention_weights_list: 各层次的注意力权重列表
        """
        level_outputs = []
        attention_weights_list = []
        
        # 计算各层次的注意力
        for attention_layer in self.attention_layers:
            output, attention_weights = attention_layer(
                agv_embeddings, task_embeddings, attention_mask
            )
            level_outputs.append(output)
            attention_weights_list.append(attention_weights)
        
        # 融合不同层次的输出
        if self.fusion_method == "concat":
            # 拼接融合
            concatenated = torch.cat(level_outputs, dim=-1)
            fused_output = self.fusion_layer(concatenated)
            
        elif self.fusion_method == "add":
            # 加法融合
            fused_output = sum(level_outputs) / self.num_levels
            
        elif self.fusion_method == "weighted":
            # 加权融合
            weights = F.softmax(self.level_weights, dim=0)
            fused_output = sum(
                weight * output for weight, output in zip(weights, level_outputs)
            )
        
        else:
            # 默认使用平均
            fused_output = sum(level_outputs) / self.num_levels
        
        # 输出层处理
        final_output = self.output_layer(fused_output)
        
        return final_output, attention_weights_list


class CrossAttentionModule(nn.Module):
    """交叉注意力模块"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 dropout: float = 0.1):
        """
        初始化交叉注意力模块
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            dropout: Dropout概率
        """
        super(CrossAttentionModule, self).__init__()
        
        # AGV到任务的注意力
        self.agv_to_task_attention = TaskAllocationMultiHeadAttention(
            embed_dim, num_heads, dropout
        )
        
        # 任务到AGV的注意力
        self.task_to_agv_attention = TaskAllocationMultiHeadAttention(
            embed_dim, num_heads, dropout
        )
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                task_embeddings: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_embeddings: AGV嵌入
            task_embeddings: 任务嵌入
            attention_mask: 注意力掩码
            
        Returns:
            enhanced_agv_features: 增强的AGV特征
            enhanced_task_features: 增强的任务特征
            agv_to_task_weights: AGV到任务的注意力权重
            task_to_agv_weights: 任务到AGV的注意力权重
        """
        # AGV到任务的注意力
        agv_attended, agv_to_task_weights = self.agv_to_task_attention(
            agv_embeddings, task_embeddings, attention_mask
        )
        
        # 任务到AGV的注意力（交换查询和键值）
        task_attended, task_to_agv_weights = self.task_to_agv_attention(
            task_embeddings, agv_embeddings, 
            attention_mask.transpose(-2, -1) if attention_mask is not None else None
        )
        
        # 特征融合
        enhanced_agv_features = self.feature_fusion(
            torch.cat([agv_embeddings, agv_attended], dim=-1)
        )
        
        enhanced_task_features = self.feature_fusion(
            torch.cat([task_embeddings, task_attended], dim=-1)
        )
        
        return enhanced_agv_features, enhanced_task_features, agv_to_task_weights, task_to_agv_weights


class EnhancedMultiHeadAttention(nn.Module):
    """增强版多头注意力机制，支持相对位置编码和自适应温度"""

    def __init__(self,
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 bias: bool = True,
                 attention_type: AttentionType = AttentionType.SELF_ATTENTION,
                 use_relative_position: bool = False,
                 max_relative_position: int = 32,
                 use_adaptive_temperature: bool = False,
                 temperature_init: float = 1.0):
        """
        初始化增强版多头注意力机制

        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            dropout: Dropout概率
            bias: 是否使用偏置
            attention_type: 注意力类型
            use_relative_position: 是否使用相对位置编码
            max_relative_position: 最大相对位置
            use_adaptive_temperature: 是否使用自适应温度
            temperature_init: 温度初始值
        """
        super(EnhancedMultiHeadAttention, self).__init__()

        assert embed_dim % num_heads == 0, f"embed_dim ({embed_dim}) must be divisible by num_heads ({num_heads})"

        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.attention_type = attention_type
        self.use_relative_position = use_relative_position
        self.max_relative_position = max_relative_position
        self.use_adaptive_temperature = use_adaptive_temperature

        # 线性变换层
        self.q_linear = nn.Linear(embed_dim, embed_dim, bias=bias)
        self.k_linear = nn.Linear(embed_dim, embed_dim, bias=bias)
        self.v_linear = nn.Linear(embed_dim, embed_dim, bias=bias)
        self.out_linear = nn.Linear(embed_dim, embed_dim, bias=bias)

        # Dropout
        self.dropout = nn.Dropout(dropout)
        self.attention_dropout = nn.Dropout(dropout)

        # 相对位置编码
        if use_relative_position:
            self.relative_position_k = nn.Parameter(
                torch.randn(2 * max_relative_position + 1, self.head_dim)
            )
            self.relative_position_v = nn.Parameter(
                torch.randn(2 * max_relative_position + 1, self.head_dim)
            )

        # 自适应温度
        if use_adaptive_temperature:
            self.temperature = nn.Parameter(torch.tensor(temperature_init))
        else:
            self.register_buffer('temperature', torch.tensor(temperature_init))

        # 缩放因子
        self.scale = 1.0 / math.sqrt(self.head_dim)

        # 初始化权重
        self._initialize_weights()

    def _initialize_weights(self):
        """初始化权重"""
        for module in [self.q_linear, self.k_linear, self.v_linear, self.out_linear]:
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.constant_(module.bias, 0.0)

    def _get_relative_positions(self, seq_len: int) -> torch.Tensor:
        """获取相对位置矩阵"""
        positions = torch.arange(seq_len, dtype=torch.long)
        relative_positions = positions.unsqueeze(0) - positions.unsqueeze(1)
        relative_positions = torch.clamp(
            relative_positions,
            -self.max_relative_position,
            self.max_relative_position
        ) + self.max_relative_position
        return relative_positions

    def forward(self,
                query: torch.Tensor,
                key: Optional[torch.Tensor] = None,
                value: Optional[torch.Tensor] = None,
                attention_mask: Optional[torch.Tensor] = None,
                key_padding_mask: Optional[torch.Tensor] = None,
                return_attention: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        前向传播

        Args:
            query: 查询张量 [batch_size, seq_len, embed_dim]
            key: 键张量 [batch_size, seq_len, embed_dim] (可选，默认使用query)
            value: 值张量 [batch_size, seq_len, embed_dim] (可选，默认使用query)
            attention_mask: 注意力掩码 [seq_len, seq_len] 或 [batch_size, seq_len, seq_len]
            key_padding_mask: 键填充掩码 [batch_size, seq_len]
            return_attention: 是否返回注意力权重

        Returns:
            output: 输出张量 [batch_size, seq_len, embed_dim]
            attention_weights: 注意力权重 (如果return_attention=True)
        """
        batch_size, seq_len, embed_dim = query.shape

        # 如果没有提供key和value，使用query（自注意力）
        if key is None:
            key = query
        if value is None:
            value = query

        # 线性变换
        Q = self.q_linear(query)  # [batch_size, seq_len, embed_dim]
        K = self.k_linear(key)    # [batch_size, seq_len, embed_dim]
        V = self.v_linear(value)  # [batch_size, seq_len, embed_dim]

        # 重塑为多头格式
        Q = Q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        # 现在形状为 [batch_size, num_heads, seq_len, head_dim]

        # 计算注意力分数
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale

        # 应用自适应温度
        if self.use_adaptive_temperature:
            attention_scores = attention_scores / self.temperature

        # 应用相对位置编码
        if self.use_relative_position:
            relative_positions = self._get_relative_positions(seq_len).to(query.device)
            relative_k = self.relative_position_k[relative_positions]  # [seq_len, seq_len, head_dim]

            # 计算相对位置注意力
            q_reshaped = Q.view(batch_size * self.num_heads, seq_len, self.head_dim)
            relative_scores = torch.matmul(q_reshaped, relative_k.transpose(-2, -1))
            relative_scores = relative_scores.view(batch_size, self.num_heads, seq_len, seq_len)

            attention_scores = attention_scores + relative_scores * self.scale

        # 应用注意力掩码
        if attention_mask is not None:
            if attention_mask.dim() == 2:
                attention_mask = attention_mask.unsqueeze(0).unsqueeze(0)
            elif attention_mask.dim() == 3:
                attention_mask = attention_mask.unsqueeze(1)

            attention_scores = attention_scores.masked_fill(
                attention_mask == 0, float('-inf')
            )

        # 应用键填充掩码
        if key_padding_mask is not None:
            key_padding_mask = key_padding_mask.unsqueeze(1).unsqueeze(2)
            attention_scores = attention_scores.masked_fill(
                key_padding_mask, float('-inf')
            )

        # 因果掩码（用于因果注意力）
        if self.attention_type == AttentionType.CAUSAL_ATTENTION:
            causal_mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1).bool()
            causal_mask = causal_mask.to(query.device)
            attention_scores = attention_scores.masked_fill(causal_mask, float('-inf'))

        # 计算注意力权重
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.attention_dropout(attention_weights)

        # 应用注意力权重到值
        attended_values = torch.matmul(attention_weights, V)

        # 应用相对位置编码到值
        if self.use_relative_position:
            relative_v = self.relative_position_v[relative_positions]  # [seq_len, seq_len, head_dim]

            # 计算相对位置值
            attention_weights_reshaped = attention_weights.view(
                batch_size * self.num_heads, seq_len, seq_len
            )
            relative_values = torch.matmul(attention_weights_reshaped, relative_v)
            relative_values = relative_values.view(
                batch_size, self.num_heads, seq_len, self.head_dim
            )

            attended_values = attended_values + relative_values

        # 重塑输出
        attended_values = attended_values.transpose(1, 2).contiguous().view(
            batch_size, seq_len, embed_dim
        )

        # 输出投影
        output = self.out_linear(attended_values)
        output = self.dropout(output)

        if return_attention:
            return output, attention_weights
        else:
            return output

    def get_attention_info(self) -> Dict[str, any]:
        """获取注意力机制信息"""
        return {
            'embed_dim': self.embed_dim,
            'num_heads': self.num_heads,
            'head_dim': self.head_dim,
            'attention_type': self.attention_type.value,
            'use_relative_position': self.use_relative_position,
            'use_adaptive_temperature': self.use_adaptive_temperature,
            'current_temperature': self.temperature.item() if self.use_adaptive_temperature else None
        }


class PositionalEncoding(nn.Module):
    """位置编码模块"""

    def __init__(self, embed_dim: int, max_len: int = 5000, dropout: float = 0.1):
        super(PositionalEncoding, self).__init__()

        self.dropout = nn.Dropout(dropout)

        # 创建位置编码
        pe = torch.zeros(max_len, embed_dim)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)

        div_term = torch.exp(torch.arange(0, embed_dim, 2).float() *
                           (-math.log(10000.0) / embed_dim))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)

        self.register_buffer('pe', pe)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: 输入张量 [seq_len, batch_size, embed_dim]
        """
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class AttentionBlock(nn.Module):
    """注意力块，包含多头注意力和前馈网络"""

    def __init__(self,
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 ff_dim: int = 256,
                 dropout: float = 0.1,
                 attention_type: AttentionType = AttentionType.SELF_ATTENTION,
                 use_layer_norm: bool = True,
                 use_enhanced_attention: bool = True):
        super(AttentionBlock, self).__init__()

        # 选择注意力机制
        if use_enhanced_attention:
            self.attention = EnhancedMultiHeadAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout,
                attention_type=attention_type
            )
        else:
            self.attention = MultiHeadAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout
            )

        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(embed_dim, ff_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(ff_dim, embed_dim),
            nn.Dropout(dropout)
        )

        # 层归一化
        self.use_layer_norm = use_layer_norm
        self.use_enhanced_attention = use_enhanced_attention
        if use_layer_norm:
            self.norm1 = nn.LayerNorm(embed_dim)
            self.norm2 = nn.LayerNorm(embed_dim)

    def forward(self,
                x: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                key_padding_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播"""
        # 多头注意力 + 残差连接
        if self.use_layer_norm:
            if self.use_enhanced_attention:
                attended = self.attention(
                    self.norm1(x),
                    attention_mask=attention_mask,
                    key_padding_mask=key_padding_mask
                )
            else:
                # 原始MultiHeadAttention需要不同的参数格式
                attended, _ = self.attention(
                    self.norm1(x).transpose(0, 1),
                    self.norm1(x).transpose(0, 1),
                    self.norm1(x).transpose(0, 1),
                    attn_mask=attention_mask,
                    key_padding_mask=key_padding_mask
                )
                attended = attended.transpose(0, 1)

            x = x + attended

            # 前馈网络 + 残差连接
            ff_output = self.feed_forward(self.norm2(x))
            x = x + ff_output
        else:
            if self.use_enhanced_attention:
                attended = self.attention(
                    x,
                    attention_mask=attention_mask,
                    key_padding_mask=key_padding_mask
                )
            else:
                attended, _ = self.attention(
                    x.transpose(0, 1),
                    x.transpose(0, 1),
                    x.transpose(0, 1),
                    attn_mask=attention_mask,
                    key_padding_mask=key_padding_mask
                )
                attended = attended.transpose(0, 1)

            x = x + attended

            ff_output = self.feed_forward(x)
            x = x + ff_output

        return x
