"""
时序一致性约束

实现注意力权重的时序平滑约束，防止决策震荡
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List, Deque
from collections import deque


class TemporalConsistencyConstraint(nn.Module):
    """时序一致性约束"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 history_length: int = 5,
                 smoothing_factor: float = 0.8,
                 consistency_weight: float = 0.3,
                 use_exponential_decay: bool = True,
                 learnable_smoothing: bool = True):
        """
        初始化时序一致性约束
        
        Args:
            embed_dim: 嵌入维度
            history_length: 历史长度
            smoothing_factor: 平滑因子
            consistency_weight: 一致性权重
            use_exponential_decay: 是否使用指数衰减
            learnable_smoothing: 是否学习平滑参数
        """
        super(TemporalConsistencyConstraint, self).__init__()
        
        self.embed_dim = embed_dim
        self.history_length = history_length
        self.consistency_weight = consistency_weight
        self.use_exponential_decay = use_exponential_decay
        
        # 平滑因子
        if learnable_smoothing:
            self.smoothing_factor = nn.Parameter(torch.tensor(smoothing_factor))
        else:
            self.register_buffer('smoothing_factor', torch.tensor(smoothing_factor))
        
        # 历史注意力权重存储
        self.attention_history = {}  # 每个batch的历史记录
        
        # 时序建模网络
        self.temporal_encoder = nn.LSTM(
            input_size=1,  # 注意力权重
            hidden_size=embed_dim // 4,
            num_layers=2,
            batch_first=True,
            dropout=0.1
        )
        
        # 一致性预测网络
        self.consistency_predictor = nn.Sequential(
            nn.Linear(embed_dim // 4, embed_dim // 8),
            nn.ReLU(),
            nn.Linear(embed_dim // 8, 1),
            nn.Sigmoid()
        )
        
        # 权重调整网络
        self.weight_adjustment = nn.Sequential(
            nn.Linear(embed_dim * 2 + embed_dim // 4, embed_dim // 2),  # AGV + Task + temporal features
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 1),
            nn.Tanh()
        )
        
        # 指数衰减权重
        if use_exponential_decay:
            decay_weights = torch.exp(-torch.arange(history_length, dtype=torch.float) * 0.5)
            self.register_buffer('decay_weights', decay_weights / decay_weights.sum())
    
    def _update_attention_history(self, 
                                batch_id: str,
                                attention_weights: torch.Tensor):
        """
        更新注意力历史
        
        Args:
            batch_id: 批次ID
            attention_weights: 当前注意力权重 [batch_size, num_agvs, num_tasks]
        """
        if batch_id not in self.attention_history:
            self.attention_history[batch_id] = deque(maxlen=self.history_length)
        
        # 添加当前权重到历史
        self.attention_history[batch_id].append(attention_weights.detach().clone())
    
    def _get_attention_history(self, batch_id: str) -> Optional[torch.Tensor]:
        """
        获取注意力历史
        
        Args:
            batch_id: 批次ID
            
        Returns:
            history: 历史注意力权重 [history_len, batch_size, num_agvs, num_tasks]
        """
        if batch_id not in self.attention_history or len(self.attention_history[batch_id]) == 0:
            return None
        
        history_list = list(self.attention_history[batch_id])
        if len(history_list) == 0:
            return None
        
        return torch.stack(history_list, dim=0)
    
    def _compute_temporal_smoothing(self, 
                                  current_attention: torch.Tensor,
                                  history: torch.Tensor) -> torch.Tensor:
        """
        计算时序平滑
        
        Args:
            current_attention: 当前注意力权重 [batch_size, num_agvs, num_tasks]
            history: 历史注意力权重 [history_len, batch_size, num_agvs, num_tasks]
            
        Returns:
            smoothed_attention: 平滑后的注意力权重
        """
        history_len, batch_size, num_agvs, num_tasks = history.shape
        
        if self.use_exponential_decay:
            # 使用指数衰减权重
            weights = self.decay_weights[:history_len].view(-1, 1, 1, 1)
            weighted_history = history * weights
            historical_average = weighted_history.sum(dim=0)
        else:
            # 简单平均
            historical_average = history.mean(dim=0)
        
        # 指数移动平均
        smoothed_attention = (self.smoothing_factor * historical_average + 
                            (1 - self.smoothing_factor) * current_attention)
        
        return smoothed_attention
    
    def _compute_consistency_loss(self, 
                                current_attention: torch.Tensor,
                                history: torch.Tensor) -> torch.Tensor:
        """
        计算一致性损失
        
        Args:
            current_attention: 当前注意力权重
            history: 历史注意力权重
            
        Returns:
            consistency_loss: 一致性损失
        """
        if history.shape[0] < 2:
            return torch.tensor(0.0, device=current_attention.device)
        
        # 计算相邻时间步的差异
        temporal_diff = torch.diff(history, dim=0)  # [history_len-1, batch_size, num_agvs, num_tasks]
        
        # 计算方差作为一致性度量
        consistency_loss = torch.var(temporal_diff, dim=0).mean()
        
        return consistency_loss
    
    def _predict_attention_trend(self, 
                               history: torch.Tensor,
                               agv_embeddings: torch.Tensor,
                               task_embeddings: torch.Tensor) -> torch.Tensor:
        """
        预测注意力趋势
        
        Args:
            history: 历史注意力权重 [history_len, batch_size, num_agvs, num_tasks]
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            task_embeddings: 任务嵌入 [batch_size, num_tasks, embed_dim]
            
        Returns:
            predicted_attention: 预测的注意力权重
        """
        history_len, batch_size, num_agvs, num_tasks = history.shape
        
        # 重塑历史数据用于LSTM
        history_reshaped = history.permute(1, 2, 3, 0).contiguous()  # [batch_size, num_agvs, num_tasks, history_len]
        history_flat = history_reshaped.view(batch_size * num_agvs * num_tasks, history_len, 1)
        
        # 通过LSTM编码时序信息
        lstm_output, _ = self.temporal_encoder(history_flat)  # [batch_size * num_agvs * num_tasks, history_len, hidden_size]
        
        # 取最后一个时间步的输出
        temporal_features = lstm_output[:, -1, :]  # [batch_size * num_agvs * num_tasks, hidden_size]
        
        # 重塑回原始形状
        temporal_features = temporal_features.view(batch_size, num_agvs, num_tasks, -1)
        
        # 结合AGV和任务嵌入
        agv_expanded = agv_embeddings.unsqueeze(2).expand(-1, -1, num_tasks, -1)
        task_expanded = task_embeddings.unsqueeze(1).expand(-1, num_agvs, -1, -1)
        
        combined_features = torch.cat([agv_expanded, task_expanded, temporal_features], dim=-1)
        
        # 预测注意力调整
        attention_adjustment = self.weight_adjustment(combined_features).squeeze(-1)
        
        # 基于历史趋势预测下一步注意力
        if history_len >= 2:
            trend = history[-1] - history[-2]  # 最近的变化趋势
            predicted_attention = history[-1] + trend + attention_adjustment
        else:
            predicted_attention = history[-1] + attention_adjustment
        
        return predicted_attention
    
    def forward(self, 
                current_attention: torch.Tensor,
                agv_embeddings: torch.Tensor,
                task_embeddings: torch.Tensor,
                batch_id: str = "default",
                return_loss: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播
        
        Args:
            current_attention: 当前注意力权重 [batch_size, num_agvs, num_tasks]
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            task_embeddings: 任务嵌入 [batch_size, num_tasks, embed_dim]
            batch_id: 批次ID
            return_loss: 是否返回一致性损失
            
        Returns:
            smoothed_attention: 平滑后的注意力权重
            consistency_loss: 一致性损失（可选）
        """
        # 获取历史注意力权重
        history = self._get_attention_history(batch_id)
        
        if history is None or history.shape[0] == 0:
            # 没有历史记录，直接返回当前注意力
            self._update_attention_history(batch_id, current_attention)
            if return_loss:
                return current_attention, torch.tensor(0.0, device=current_attention.device)
            else:
                return current_attention, None
        
        # 计算时序平滑
        smoothed_attention = self._compute_temporal_smoothing(current_attention, history)
        
        # 预测注意力趋势
        if history.shape[0] >= 2:
            predicted_attention = self._predict_attention_trend(history, agv_embeddings, task_embeddings)
            
            # 结合预测和平滑结果
            final_attention = (self.consistency_weight * predicted_attention + 
                             (1 - self.consistency_weight) * smoothed_attention)
        else:
            final_attention = smoothed_attention
        
        # 更新历史记录
        self._update_attention_history(batch_id, current_attention)
        
        # 计算一致性损失
        consistency_loss = None
        if return_loss:
            consistency_loss = self._compute_consistency_loss(current_attention, history)
        
        return final_attention, consistency_loss
    
    def reset_history(self, batch_id: str = None):
        """
        重置历史记录
        
        Args:
            batch_id: 批次ID，如果为None则重置所有历史
        """
        if batch_id is None:
            self.attention_history.clear()
        elif batch_id in self.attention_history:
            del self.attention_history[batch_id]
    
    def get_consistency_metrics(self, batch_id: str = "default") -> Dict[str, float]:
        """
        获取一致性指标
        
        Args:
            batch_id: 批次ID
            
        Returns:
            metrics: 一致性指标字典
        """
        history = self._get_attention_history(batch_id)
        
        if history is None or history.shape[0] < 2:
            return {
                'temporal_variance': 0.0,
                'trend_stability': 0.0,
                'history_length': 0
            }
        
        # 计算时序方差
        temporal_variance = torch.var(history, dim=0).mean().item()
        
        # 计算趋势稳定性
        if history.shape[0] >= 3:
            trends = torch.diff(history, dim=0)
            trend_stability = 1.0 / (1.0 + torch.var(trends, dim=0).mean().item())
        else:
            trend_stability = 1.0
        
        return {
            'temporal_variance': temporal_variance,
            'trend_stability': trend_stability,
            'history_length': history.shape[0]
        }


class AdaptiveTemporalConsistency(TemporalConsistencyConstraint):
    """自适应时序一致性约束"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 history_length: int = 5,
                 smoothing_factor: float = 0.8,
                 consistency_weight: float = 0.3,
                 adaptation_threshold: float = 0.1):
        """
        初始化自适应时序一致性约束
        
        Args:
            embed_dim: 嵌入维度
            history_length: 历史长度
            smoothing_factor: 初始平滑因子
            consistency_weight: 初始一致性权重
            adaptation_threshold: 自适应阈值
        """
        super().__init__(embed_dim, history_length, smoothing_factor, consistency_weight, 
                        use_exponential_decay=True, learnable_smoothing=True)
        
        self.adaptation_threshold = adaptation_threshold
        
        # 自适应控制网络
        self.adaptation_controller = nn.Sequential(
            nn.Linear(embed_dim * 2 + 3, embed_dim // 4),  # AGV + Task + 3个统计特征
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 2),  # 输出平滑因子和一致性权重
            nn.Sigmoid()
        )
    
    def _compute_adaptation_features(self, 
                                   current_attention: torch.Tensor,
                                   history: torch.Tensor) -> torch.Tensor:
        """
        计算自适应特征
        
        Args:
            current_attention: 当前注意力权重
            history: 历史注意力权重
            
        Returns:
            adaptation_features: 自适应特征 [3]
        """
        if history.shape[0] < 2:
            return torch.zeros(3, device=current_attention.device)
        
        # 计算变化率
        recent_change = torch.norm(current_attention - history[-1]).item()
        
        # 计算历史方差
        historical_variance = torch.var(history, dim=0).mean().item()
        
        # 计算趋势强度
        if history.shape[0] >= 3:
            trend = history[-1] - history[-2]
            prev_trend = history[-2] - history[-3]
            trend_consistency = F.cosine_similarity(
                trend.flatten(), prev_trend.flatten(), dim=0
            ).item()
        else:
            trend_consistency = 1.0
        
        return torch.tensor([recent_change, historical_variance, trend_consistency], 
                          device=current_attention.device)
    
    def forward(self, 
                current_attention: torch.Tensor,
                agv_embeddings: torch.Tensor,
                task_embeddings: torch.Tensor,
                batch_id: str = "default",
                return_loss: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播（自适应版本）
        """
        # 获取历史注意力权重
        history = self._get_attention_history(batch_id)
        
        if history is None or history.shape[0] == 0:
            # 没有历史记录，直接返回当前注意力
            self._update_attention_history(batch_id, current_attention)
            if return_loss:
                return current_attention, torch.tensor(0.0, device=current_attention.device)
            else:
                return current_attention, None
        
        # 计算自适应特征
        adaptation_features = self._compute_adaptation_features(current_attention, history)
        
        # 计算平均嵌入特征
        agv_mean = agv_embeddings.mean(dim=(0, 1))  # [embed_dim]
        task_mean = task_embeddings.mean(dim=(0, 1))  # [embed_dim]
        
        # 组合特征
        combined_features = torch.cat([agv_mean, task_mean, adaptation_features])
        
        # 通过自适应控制器计算参数
        adaptive_params = self.adaptation_controller(combined_features)
        adaptive_smoothing = adaptive_params[0] * 0.9 + 0.05  # 限制在[0.05, 0.95]
        adaptive_consistency = adaptive_params[1] * 0.8 + 0.1  # 限制在[0.1, 0.9]
        
        # 临时更新参数
        original_smoothing = self.smoothing_factor.data.clone()
        original_consistency = self.consistency_weight
        
        self.smoothing_factor.data = adaptive_smoothing
        self.consistency_weight = adaptive_consistency.item()
        
        # 调用父类方法
        result, loss = super().forward(
            current_attention, agv_embeddings, task_embeddings, batch_id, return_loss
        )
        
        # 恢复原始参数
        self.smoothing_factor.data = original_smoothing
        self.consistency_weight = original_consistency
        
        return result, loss
