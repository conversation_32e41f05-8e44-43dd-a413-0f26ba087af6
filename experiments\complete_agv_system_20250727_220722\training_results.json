{"experiment_info": {"experiment_dir": "experiments\\complete_agv_system_20250727_220722", "total_episodes": 50, "best_reward": -32.99999999999997, "final_avg_reward": 0.0, "device": "cuda", "network_parameters": 159750}, "config": {"map_width": 26, "map_height": 10, "num_agvs": 2, "num_tasks": 4, "total_episodes": 50, "learning_rate": 0.0001, "hidden_dim": 128, "embed_dim": 64, "num_heads": 4}, "training_history": [{"episode": 0, "reward": -35.699999999999946, "avg_reward": -35.699999999999946, "best_reward": -35.699999999999946, "policy_loss": 0.2349227850580678, "value_loss": 1276.0127291724086, "entropy": 0.02848476174451259, "learning_rate": 9.95e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 1.0, "total_distance": 99, "tasks_completed": 0}, {"episode": 1, "reward": -38.349999999999945, "avg_reward": -37.02499999999995, "best_reward": -35.699999999999946, "policy_loss": 0.33346904411250494, "value_loss": 271.533955742546, "entropy": 0.06774786680100855, "learning_rate": 9.90025e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 1.0, "total_distance": 43, "tasks_completed": 0}, {"episode": 2, "reward": -32.99999999999997, "avg_reward": -35.68333333333329, "best_reward": -32.99999999999997, "policy_loss": 0.2756224380063748, "value_loss": 168.4750436866718, "entropy": 0.009953611130011477, "learning_rate": 9.85074875e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 1.0, "total_distance": 79, "tasks_completed": 0}, {"episode": 3, "reward": -60.44999999999995, "avg_reward": -41.87499999999996, "best_reward": -32.99999999999997, "policy_loss": 0.1726849643807629, "value_loss": 91.21927427550827, "entropy": 0.018083190423162372, "learning_rate": 9.80149500625e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.84, "total_distance": 125, "tasks_completed": 0}, {"episode": 4, "reward": -59.300000000000026, "avg_reward": -45.35999999999997, "best_reward": -32.99999999999997, "policy_loss": 0.21724351981959192, "value_loss": 36.656006819542334, "entropy": 0.02346923808697961, "learning_rate": 9.75248753121875e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.8200000000000001, "total_distance": 138, "tasks_completed": 0}, {"episode": 5, "reward": -63.84999999999995, "avg_reward": -48.441666666666634, "best_reward": -32.99999999999997, "policy_loss": 0.17493813710345235, "value_loss": 38.71225401910422, "entropy": 0.04086413559156019, "learning_rate": 9.703725093562657e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.8, "total_distance": 141, "tasks_completed": 0}, {"episode": 6, "reward": -67.75000000000003, "avg_reward": -51.19999999999998, "best_reward": -32.99999999999997, "policy_loss": 0.21459007418221276, "value_loss": 42.10581582775669, "entropy": 0.011974609216628854, "learning_rate": 9.655206468094843e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.81, "total_distance": 140, "tasks_completed": 0}, {"episode": 7, "reward": -77.80000000000004, "avg_reward": -54.524999999999984, "best_reward": -32.99999999999997, "policy_loss": 0.15428177380551164, "value_loss": 28.91728017830581, "entropy": 0.02430837195910641, "learning_rate": 9.606930435754369e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.71, "total_distance": 126, "tasks_completed": 0}, {"episode": 8, "reward": -93.10000000000001, "avg_reward": -58.811111111111096, "best_reward": -32.99999999999997, "policy_loss": 0.22159332927513362, "value_loss": 22.33338285769594, "entropy": 0.018639733457122436, "learning_rate": 9.558895783575598e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.6699999999999999, "total_distance": 120, "tasks_completed": 0}, {"episode": 9, "reward": -88.65000000000006, "avg_reward": -61.794999999999995, "best_reward": -32.99999999999997, "policy_loss": 0.03459639524389366, "value_loss": 15.090267731044442, "entropy": 0.021942051320881527, "learning_rate": 9.51110130465772e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.69, "total_distance": 132, "tasks_completed": 0}, {"episode": 10, "reward": -84.1, "avg_reward": -63.82272727272727, "best_reward": -32.99999999999997, "policy_loss": 0.15614290922619897, "value_loss": 32.21918084023671, "entropy": 0.013736034782189203, "learning_rate": 9.463545798134431e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.71, "total_distance": 158, "tasks_completed": 0}, {"episode": 11, "reward": -79.35000000000001, "avg_reward": -65.11666666666666, "best_reward": -32.99999999999997, "policy_loss": 0.21473174583903226, "value_loss": 24.705607995117994, "entropy": 0.0019890124465478704, "learning_rate": 9.416228069143759e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.73, "total_distance": 148, "tasks_completed": 0}, {"episode": 12, "reward": -66.59999999999997, "avg_reward": -65.23076923076923, "best_reward": -32.99999999999997, "policy_loss": 0.1616231623766791, "value_loss": 10.618498757730523, "entropy": 0.005046220956832788, "learning_rate": 9.36914692879804e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.77, "total_distance": 147, "tasks_completed": 0}, {"episode": 13, "reward": -59.04999999999999, "avg_reward": -64.78928571428571, "best_reward": -32.99999999999997, "policy_loss": 0.21275447492839902, "value_loss": 17.696677647430576, "entropy": 0.00027730962193317054, "learning_rate": 9.32230119415405e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.81, "total_distance": 168, "tasks_completed": 0}, {"episode": 14, "reward": -51.85000000000001, "avg_reward": -63.92666666666666, "best_reward": -32.99999999999997, "policy_loss": 0.18646402990483646, "value_loss": 10.707580695433231, "entropy": 0.03766408096922073, "learning_rate": 9.27568968818328e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.84, "total_distance": 180, "tasks_completed": 0}, {"episode": 15, "reward": -56.84999999999999, "avg_reward": -63.484374999999986, "best_reward": -32.99999999999997, "policy_loss": 0.16921900591207156, "value_loss": 18.84632720622331, "entropy": 0.02847484260693553, "learning_rate": 9.229311239742364e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.83, "total_distance": 128, "tasks_completed": 0}, {"episode": 16, "reward": -68.3, "avg_reward": -63.76764705882351, "best_reward": -32.99999999999997, "policy_loss": 0.17358715581942685, "value_loss": 16.219628901529248, "entropy": 0.020721597814423565, "learning_rate": 9.183164683543651e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.79, "total_distance": 155, "tasks_completed": 0}, {"episode": 17, "reward": -61.19999999999996, "avg_reward": -63.624999999999986, "best_reward": -32.99999999999997, "policy_loss": 0.13936611688904402, "value_loss": 15.0288331538887, "entropy": 0.06422300354185305, "learning_rate": 9.137248860125932e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.83, "total_distance": 143, "tasks_completed": 0}, {"episode": 18, "reward": -73.95000000000009, "avg_reward": -64.16842105263157, "best_reward": -32.99999999999997, "policy_loss": 0.24155303166491843, "value_loss": 16.096193278607, "entropy": 0.018629341956784486, "learning_rate": 9.091562615825302e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.73, "total_distance": 142, "tasks_completed": 0}, {"episode": 19, "reward": -84.29999999999998, "avg_reward": -65.17499999999998, "best_reward": -32.99999999999997, "policy_loss": 0.2280132006073445, "value_loss": 25.78277327432336, "entropy": 0.041520517426036796, "learning_rate": 9.046104802746175e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.71, "total_distance": 138, "tasks_completed": 0}, {"episode": 20, "reward": -43.80000000000005, "avg_reward": -64.15714285714284, "best_reward": -32.99999999999997, "policy_loss": 0.2004280136386479, "value_loss": 11.234817947014816, "entropy": 0.047157083245536975, "learning_rate": 9.000874278732444e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.92, "total_distance": 189, "tasks_completed": 0}, {"episode": 21, "reward": -51.599999999999994, "avg_reward": -63.58636363636362, "best_reward": -32.99999999999997, "policy_loss": 0.1408535974858153, "value_loss": 6.15646533531389, "entropy": 0.027605802091873338, "learning_rate": 8.955869907338781e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.87, "total_distance": 187, "tasks_completed": 0}, {"episode": 22, "reward": -101.90000000000015, "avg_reward": -65.25217391304346, "best_reward": -32.99999999999997, "policy_loss": 0.08987028554733416, "value_loss": 23.282662013746933, "entropy": 0.046194942795022106, "learning_rate": 8.911090557802087e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.6, "total_distance": 160, "tasks_completed": 0}, {"episode": 23, "reward": -114.09999999999995, "avg_reward": -67.28750000000001, "best_reward": -32.99999999999997, "policy_loss": 0.14698623232320174, "value_loss": 46.7407696311688, "entropy": 0.05634352155987462, "learning_rate": 8.866535105013077e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.55, "total_distance": 126, "tasks_completed": 0}, {"episode": 24, "reward": -87.30000000000005, "avg_reward": -68.08800000000001, "best_reward": -32.99999999999997, "policy_loss": 0.2437060604461531, "value_loss": 21.13193535623063, "entropy": 0.02533062942490337, "learning_rate": 8.822202429488011e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.69, "total_distance": 125, "tasks_completed": 0}, {"episode": 25, "reward": -59.35000000000009, "avg_reward": -67.75192307692308, "best_reward": -32.99999999999997, "policy_loss": 0.2567232254815035, "value_loss": 17.435735058236364, "entropy": 0.033154207066730425, "learning_rate": 8.77809141734057e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.84, "total_distance": 162, "tasks_completed": 0}, {"episode": 26, "reward": -85.95000000000005, "avg_reward": -68.42592592592594, "best_reward": -32.99999999999997, "policy_loss": 0.1555044726088755, "value_loss": 20.97307960017526, "entropy": 0.037645819201965386, "learning_rate": 8.734200960253867e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.69, "total_distance": 140, "tasks_completed": 0}, {"episode": 27, "reward": -44.35, "avg_reward": -67.56607142857143, "best_reward": -32.99999999999997, "policy_loss": 0.2907616528925818, "value_loss": 25.712371394002087, "entropy": 0.07455100743717134, "learning_rate": 8.690529955452598e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.87, "total_distance": 177, "tasks_completed": 0}, {"episode": 28, "reward": -95.64999999999999, "avg_reward": -68.5344827586207, "best_reward": -32.99999999999997, "policy_loss": 0.0869826750850673, "value_loss": 29.064568759779696, "entropy": 0.05687168600616175, "learning_rate": 8.647077305675335e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.6, "total_distance": 141, "tasks_completed": 0}, {"episode": 29, "reward": -57.20000000000005, "avg_reward": -68.15666666666668, "best_reward": -32.99999999999997, "policy_loss": 0.21141016065260593, "value_loss": 16.90753084023095, "entropy": 0.050829767808540774, "learning_rate": 8.603841919146959e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.86, "total_distance": 147, "tasks_completed": 0}, {"episode": 30, "reward": -35.95000000000002, "avg_reward": -67.11774193548388, "best_reward": -32.99999999999997, "policy_loss": 0.29407016971161226, "value_loss": 17.58512949712997, "entropy": 0.0386937333075817, "learning_rate": 8.560822709551223e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.96, "total_distance": 194, "tasks_completed": 0}, {"episode": 31, "reward": -40.1, "avg_reward": -66.2734375, "best_reward": -32.99999999999997, "policy_loss": 0.12964748477808063, "value_loss": 18.63154885633577, "entropy": 0.0026388867727397763, "learning_rate": 8.518018596003468e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.9, "total_distance": 114, "tasks_completed": 0}, {"episode": 32, "reward": -42.150000000000034, "avg_reward": -65.54242424242425, "best_reward": -32.99999999999997, "policy_loss": 0.19586726816154965, "value_loss": 6.848319425968172, "entropy": 0.00012677920691070692, "learning_rate": 8.47542850302345e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.96, "total_distance": 112, "tasks_completed": 0}, {"episode": 33, "reward": -40.94999999999999, "avg_reward": -64.81911764705882, "best_reward": -32.99999999999997, "policy_loss": 0.1107384322345668, "value_loss": 7.563675734931021, "entropy": 0.0003943791715241218, "learning_rate": 8.433051360508334e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.96, "total_distance": 106, "tasks_completed": 0}, {"episode": 34, "reward": -59.10000000000001, "avg_reward": -64.65571428571428, "best_reward": -32.99999999999997, "policy_loss": 0.24342666169186075, "value_loss": 6.666390761918956, "entropy": 0.0032074854118621945, "learning_rate": 8.390886103705792e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.83, "total_distance": 167, "tasks_completed": 0}, {"episode": 35, "reward": -38.54999999999997, "avg_reward": -63.93055555555556, "best_reward": -32.99999999999997, "policy_loss": 0.24370559347453635, "value_loss": 3.944657300599271, "entropy": 0.00249243149414383, "learning_rate": 8.348931673187264e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.96, "total_distance": 142, "tasks_completed": 0}, {"episode": 36, "reward": -35.399999999999984, "avg_reward": -63.15945945945946, "best_reward": -32.99999999999997, "policy_loss": 0.2113436173568699, "value_loss": 4.112296809198378, "entropy": 0.01334325343630032, "learning_rate": 8.307187014821327e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.99, "total_distance": 134, "tasks_completed": 0}, {"episode": 37, "reward": -44.099999999999994, "avg_reward": -62.6578947368421, "best_reward": -32.99999999999997, "policy_loss": 0.21772209102826529, "value_loss": 4.767420280801646, "entropy": 0.016568460225856636, "learning_rate": 8.265651079747221e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.91, "total_distance": 182, "tasks_completed": 0}, {"episode": 38, "reward": -33.45, "avg_reward": -61.908974358974355, "best_reward": -32.99999999999997, "policy_loss": 0.26104972147799643, "value_loss": 6.1524774969646145, "entropy": 0.03143497119036207, "learning_rate": 8.224322824348484e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.96, "total_distance": 194, "tasks_completed": 0}, {"episode": 39, "reward": -48.100000000000016, "avg_reward": -61.563750000000006, "best_reward": -32.99999999999997, "policy_loss": 0.13863553041254126, "value_loss": 5.074728536584686, "entropy": 0.019983363584544143, "learning_rate": 8.183201210226742e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.91, "total_distance": 163, "tasks_completed": 0}, {"episode": 40, "reward": -82.94999999999999, "avg_reward": -62.08536585365854, "best_reward": -32.99999999999997, "policy_loss": 0.1511914133880236, "value_loss": 14.47389740686826, "entropy": 0.019862340682159925, "learning_rate": 8.142285204175607e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.69, "total_distance": 159, "tasks_completed": 0}, {"episode": 41, "reward": -63.59999999999998, "avg_reward": -62.12142857142857, "best_reward": -32.99999999999997, "policy_loss": 0.2506020346224778, "value_loss": 6.444463607753035, "entropy": 0.016518569188500958, "learning_rate": 8.101573778154729e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.83, "total_distance": 134, "tasks_completed": 0}, {"episode": 42, "reward": -99.15000000000012, "avg_reward": -62.98255813953488, "best_reward": -32.99999999999997, "policy_loss": 0.2501148765769161, "value_loss": 14.928459192059284, "entropy": 0.009943759301480858, "learning_rate": 8.061065909263956e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.61, "total_distance": 161, "tasks_completed": 0}, {"episode": 43, "reward": -83.84999999999998, "avg_reward": -63.45681818181818, "best_reward": -32.99999999999997, "policy_loss": 0.13809076458166258, "value_loss": 24.420356135631835, "entropy": 0.03255318309696203, "learning_rate": 8.020760579717637e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.71, "total_distance": 159, "tasks_completed": 0}, {"episode": 44, "reward": -68.55000000000001, "avg_reward": -63.57, "best_reward": -32.99999999999997, "policy_loss": 0.12956778223213866, "value_loss": 12.774847817337408, "entropy": 0.09248132384797202, "learning_rate": 7.980656776819048e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.81, "total_distance": 131, "tasks_completed": 0}, {"episode": 45, "reward": -70.50000000000007, "avg_reward": -63.720652173913045, "best_reward": -32.99999999999997, "policy_loss": 0.1343374857008628, "value_loss": 7.2673444156963765, "entropy": 0.016233847289170125, "learning_rate": 7.940753492934953e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.8, "total_distance": 145, "tasks_completed": 0}, {"episode": 46, "reward": -41.90000000000003, "avg_reward": -63.25638297872341, "best_reward": -32.99999999999997, "policy_loss": 0.1928464646772069, "value_loss": 6.904471859816694, "entropy": 0.006473083262528026, "learning_rate": 7.901049725470279e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.9299999999999999, "total_distance": 128, "tasks_completed": 0}, {"episode": 47, "reward": -39.20000000000003, "avg_reward": -62.75520833333334, "best_reward": -32.99999999999997, "policy_loss": 0.10295345927061038, "value_loss": 6.034757369556149, "entropy": 0.005708004751543759, "learning_rate": 7.861544476842927e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.97, "total_distance": 125, "tasks_completed": 0}, {"episode": 48, "reward": -111.7500000000001, "avg_reward": -63.75510204081633, "best_reward": -32.99999999999997, "policy_loss": 0.24096801822329725, "value_loss": 8.197295236972035, "entropy": 0.0007938451305358285, "learning_rate": 7.822236754458713e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.5700000000000001, "total_distance": 108, "tasks_completed": 0}, {"episode": 49, "reward": -37.800000000000054, "avg_reward": -63.23600000000001, "best_reward": -32.99999999999997, "policy_loss": 0.27730652406234385, "value_loss": 8.15006907584593, "entropy": 0.04815847050741955, "learning_rate": 7.783125570686419e-05, "completion_rate": 0.0, "collision_count": 0, "agv_utilization": 0.0, "path_efficiency": 0.0, "task_assignment_efficiency": 0.0, "collaboration_quality": 0.97, "total_distance": 135, "tasks_completed": 0}]}