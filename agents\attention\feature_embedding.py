"""
特征嵌入层实现

实现AGV和任务的特征嵌入层，将原始状态映射到统一的64维嵌入空间
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List
import math


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        """
        初始化位置编码
        
        Args:
            d_model: 模型维度
            max_len: 最大序列长度
        """
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        添加位置编码
        
        Args:
            x: 输入张量 [seq_len, batch_size, d_model]
            
        Returns:
            带位置编码的张量
        """
        return x + self.pe[:x.size(0), :]


class AGVFeatureEmbedding(nn.Module):
    """AGV特征嵌入层"""
    
    def __init__(self, 
                 agv_state_dim: int = 10,
                 embed_dim: int = 64,
                 use_positional_encoding: bool = True,
                 dropout: float = 0.1):
        """
        初始化AGV特征嵌入层
        
        Args:
            agv_state_dim: AGV状态维度
            embed_dim: 嵌入维度
            use_positional_encoding: 是否使用位置编码
            dropout: Dropout概率
        """
        super(AGVFeatureEmbedding, self).__init__()
        
        self.agv_state_dim = agv_state_dim
        self.embed_dim = embed_dim
        self.use_positional_encoding = use_positional_encoding
        
        # AGV状态特征分解
        # 位置特征 (x, y): 2维
        # 朝向特征 (direction): 1维
        # 状态特征 (status, load, capacity): 3维
        # 任务特征 (current_task, task_queue): 2维
        # 性能特征 (speed, efficiency): 2维
        
        # 位置嵌入
        self.position_embedding = nn.Sequential(
            nn.Linear(2, 16),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(16, 16)
        )
        
        # 朝向嵌入（使用Embedding层处理离散朝向）
        self.direction_embedding = nn.Embedding(4, 8)  # 4个方向
        
        # 状态嵌入
        self.status_embedding = nn.Sequential(
            nn.Linear(3, 12),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(12, 12)
        )
        
        # 任务嵌入
        self.task_embedding = nn.Sequential(
            nn.Linear(2, 8),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(8, 8)
        )
        
        # 性能嵌入
        self.performance_embedding = nn.Sequential(
            nn.Linear(2, 8),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(8, 8)
        )
        
        # 特征融合层
        total_features = 16 + 8 + 12 + 8 + 8  # 52维
        self.feature_fusion = nn.Sequential(
            nn.Linear(total_features, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim)
        )
        
        # 位置编码
        if use_positional_encoding:
            self.pos_encoding = PositionalEncoding(embed_dim)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0, 0.1)
    
    def forward(self, agv_states: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            agv_states: AGV状态 [batch_size, num_agvs, agv_state_dim]
            
        Returns:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
        """
        batch_size, num_agvs, _ = agv_states.shape
        
        # 分解AGV状态特征
        # 假设AGV状态格式：[x, y, direction, status, load, capacity, current_task, task_queue_len, speed, efficiency]
        positions = agv_states[:, :, :2]  # [batch_size, num_agvs, 2]
        directions = torch.clamp(agv_states[:, :, 2].long(), 0, 3)  # [batch_size, num_agvs] 限制在[0,3]范围
        status_features = agv_states[:, :, 3:6]  # [batch_size, num_agvs, 3]
        task_features = agv_states[:, :, 6:8]  # [batch_size, num_agvs, 2]
        performance_features = agv_states[:, :, 8:10]  # [batch_size, num_agvs, 2]
        
        # 特征嵌入
        pos_embed = self.position_embedding(positions)  # [batch_size, num_agvs, 16]
        dir_embed = self.direction_embedding(directions)  # [batch_size, num_agvs, 8]
        status_embed = self.status_embedding(status_features)  # [batch_size, num_agvs, 12]
        task_embed = self.task_embedding(task_features)  # [batch_size, num_agvs, 8]
        perf_embed = self.performance_embedding(performance_features)  # [batch_size, num_agvs, 8]
        
        # 特征拼接
        combined_features = torch.cat([
            pos_embed, dir_embed, status_embed, task_embed, perf_embed
        ], dim=-1)  # [batch_size, num_agvs, 52]
        
        # 特征融合
        agv_embeddings = self.feature_fusion(combined_features)  # [batch_size, num_agvs, embed_dim]
        
        # 添加位置编码
        if self.use_positional_encoding:
            # 转换为序列格式进行位置编码
            agv_embeddings = agv_embeddings.transpose(0, 1)  # [num_agvs, batch_size, embed_dim]
            agv_embeddings = self.pos_encoding(agv_embeddings)
            agv_embeddings = agv_embeddings.transpose(0, 1)  # [batch_size, num_agvs, embed_dim]
        
        return agv_embeddings


class TaskFeatureEmbedding(nn.Module):
    """任务特征嵌入层"""
    
    def __init__(self, 
                 task_state_dim: int = 11,
                 embed_dim: int = 64,
                 use_positional_encoding: bool = True,
                 dropout: float = 0.1):
        """
        初始化任务特征嵌入层
        
        Args:
            task_state_dim: 任务状态维度
            embed_dim: 嵌入维度
            use_positional_encoding: 是否使用位置编码
            dropout: Dropout概率
        """
        super(TaskFeatureEmbedding, self).__init__()
        
        self.task_state_dim = task_state_dim
        self.embed_dim = embed_dim
        self.use_positional_encoding = use_positional_encoding
        
        # 任务状态特征分解
        # 位置特征 (pickup_x, pickup_y, dropoff_x, dropoff_y): 4维
        # 任务属性 (weight, priority, type): 3维
        # 时间特征 (creation_time, deadline, estimated_duration): 3维
        # 状态特征 (status): 1维
        
        # 位置嵌入（拾取和放置位置）
        self.pickup_position_embedding = nn.Sequential(
            nn.Linear(2, 12),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(12, 12)
        )
        
        self.dropoff_position_embedding = nn.Sequential(
            nn.Linear(2, 12),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(12, 12)
        )
        
        # 任务属性嵌入
        self.task_attr_embedding = nn.Sequential(
            nn.Linear(3, 16),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(16, 16)
        )
        
        # 时间特征嵌入
        self.time_embedding = nn.Sequential(
            nn.Linear(3, 12),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(12, 12)
        )
        
        # 状态嵌入（使用Embedding层处理离散状态）
        self.status_embedding = nn.Embedding(5, 8)  # 5种任务状态
        
        # 特征融合层
        total_features = 12 + 12 + 16 + 12 + 8  # 60维
        self.feature_fusion = nn.Sequential(
            nn.Linear(total_features, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim)
        )
        
        # 位置编码
        if use_positional_encoding:
            self.pos_encoding = PositionalEncoding(embed_dim)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0, 0.1)
    
    def forward(self, task_states: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            task_states: 任务状态 [batch_size, num_tasks, task_state_dim]
            
        Returns:
            task_embeddings: 任务嵌入 [batch_size, num_tasks, embed_dim]
        """
        batch_size, num_tasks, _ = task_states.shape
        
        # 分解任务状态特征
        # 假设任务状态格式：[pickup_x, pickup_y, dropoff_x, dropoff_y, weight, priority, type,
        #                   creation_time, deadline, estimated_duration, status]
        pickup_positions = task_states[:, :, :2]  # [batch_size, num_tasks, 2]
        dropoff_positions = task_states[:, :, 2:4]  # [batch_size, num_tasks, 2]
        task_attributes = task_states[:, :, 4:7]  # [batch_size, num_tasks, 3]
        time_features = task_states[:, :, 7:10]  # [batch_size, num_tasks, 3]
        status = torch.clamp(task_states[:, :, 10].long(), 0, 4)  # [batch_size, num_tasks] 限制在[0,4]范围
        
        # 特征嵌入
        pickup_embed = self.pickup_position_embedding(pickup_positions)  # [batch_size, num_tasks, 12]
        dropoff_embed = self.dropoff_position_embedding(dropoff_positions)  # [batch_size, num_tasks, 12]
        attr_embed = self.task_attr_embedding(task_attributes)  # [batch_size, num_tasks, 16]
        time_embed = self.time_embedding(time_features)  # [batch_size, num_tasks, 12]
        status_embed = self.status_embedding(status)  # [batch_size, num_tasks, 8]
        
        # 特征拼接
        combined_features = torch.cat([
            pickup_embed, dropoff_embed, attr_embed, time_embed, status_embed
        ], dim=-1)  # [batch_size, num_tasks, 60]
        
        # 特征融合
        task_embeddings = self.feature_fusion(combined_features)  # [batch_size, num_tasks, embed_dim]
        
        # 添加位置编码
        if self.use_positional_encoding:
            # 转换为序列格式进行位置编码
            task_embeddings = task_embeddings.transpose(0, 1)  # [num_tasks, batch_size, embed_dim]
            task_embeddings = self.pos_encoding(task_embeddings)
            task_embeddings = task_embeddings.transpose(0, 1)  # [batch_size, num_tasks, embed_dim]
        
        return task_embeddings


class UnifiedFeatureEmbedding(nn.Module):
    """统一特征嵌入层"""
    
    def __init__(self, 
                 agv_state_dim: int = 10,
                 task_state_dim: int = 11,
                 embed_dim: int = 64,
                 use_positional_encoding: bool = True,
                 dropout: float = 0.1):
        """
        初始化统一特征嵌入层
        
        Args:
            agv_state_dim: AGV状态维度
            task_state_dim: 任务状态维度
            embed_dim: 嵌入维度
            use_positional_encoding: 是否使用位置编码
            dropout: Dropout概率
        """
        super(UnifiedFeatureEmbedding, self).__init__()
        
        self.agv_embedding = AGVFeatureEmbedding(
            agv_state_dim, embed_dim, use_positional_encoding, dropout
        )
        
        self.task_embedding = TaskFeatureEmbedding(
            task_state_dim, embed_dim, use_positional_encoding, dropout
        )
        
        self.embed_dim = embed_dim
    
    def forward(self, 
                agv_states: torch.Tensor,
                task_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_states: AGV状态 [batch_size, num_agvs, agv_state_dim]
            task_states: 任务状态 [batch_size, num_tasks, task_state_dim]
            
        Returns:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            task_embeddings: 任务嵌入 [batch_size, num_tasks, embed_dim]
        """
        agv_embeddings = self.agv_embedding(agv_states)
        task_embeddings = self.task_embedding(task_states)
        
        return agv_embeddings, task_embeddings
    
    def get_embedding_dim(self) -> int:
        """获取嵌入维度"""
        return self.embed_dim
