"""
相对位置编码实现

实现AGV间的相对位置编码，捕捉空间关系和运动模式
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, Tuple, Optional, List
from enum import Enum


class PositionType(Enum):
    """位置类型枚举"""
    EUCLIDEAN = "euclidean"      # 欧几里得距离
    MANHATTAN = "manhattan"      # 曼哈顿距离
    GRID_BASED = "grid_based"    # 基于网格的位置
    DIRECTIONAL = "directional"  # 方向性位置


class RelativePositionEncoding(nn.Module):
    """相对位置编码基类"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 max_distance: float = 50.0,
                 position_type: PositionType = PositionType.EUCLIDEAN,
                 learnable: bool = True,
                 temperature: float = 1.0):
        """
        初始化相对位置编码
        
        Args:
            embed_dim: 嵌入维度
            max_distance: 最大距离
            position_type: 位置类型
            learnable: 是否可学习
            temperature: 温度参数
        """
        super(RelativePositionEncoding, self).__init__()
        
        self.embed_dim = embed_dim
        self.max_distance = max_distance
        self.position_type = position_type
        self.learnable = learnable
        self.temperature = temperature
        
        # 距离编码维度
        self.distance_dim = embed_dim // 4
        self.angle_dim = embed_dim // 4
        self.velocity_dim = embed_dim // 4
        self.context_dim = embed_dim - self.distance_dim - self.angle_dim - self.velocity_dim
        
        # 距离编码网络
        if learnable:
            self.distance_encoder = nn.Sequential(
                nn.Linear(1, self.distance_dim),
                nn.ReLU(),
                nn.Linear(self.distance_dim, self.distance_dim)
            )
            
            self.angle_encoder = nn.Sequential(
                nn.Linear(2, self.angle_dim),  # sin, cos
                nn.ReLU(),
                nn.Linear(self.angle_dim, self.angle_dim)
            )
            
            self.velocity_encoder = nn.Sequential(
                nn.Linear(2, self.velocity_dim),  # 相对速度
                nn.ReLU(),
                nn.Linear(self.velocity_dim, self.velocity_dim)
            )
            
            self.context_encoder = nn.Sequential(
                nn.Linear(4, self.context_dim),  # 额外上下文信息
                nn.ReLU(),
                nn.Linear(self.context_dim, self.context_dim)
            )
        else:
            # 固定的正弦位置编码
            self.register_buffer('distance_encoding', self._create_sinusoidal_encoding(self.distance_dim))
            self.register_buffer('angle_encoding', self._create_sinusoidal_encoding(self.angle_dim))
    
    def _create_sinusoidal_encoding(self, dim: int) -> torch.Tensor:
        """创建正弦位置编码"""
        position = torch.arange(0, 1000, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, dim, 2).float() * 
                           (-math.log(10000.0) / dim))
        
        encoding = torch.zeros(1000, dim)
        encoding[:, 0::2] = torch.sin(position * div_term)
        encoding[:, 1::2] = torch.cos(position * div_term)
        
        return encoding
    
    def compute_relative_positions(self, 
                                 agv_positions: torch.Tensor,
                                 agv_velocities: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        计算相对位置信息
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            
        Returns:
            relative_info: 相对位置信息字典
        """
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算相对位置向量
        pos_i = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        pos_j = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        relative_pos = pos_j - pos_i  # [batch_size, num_agvs, num_agvs, 2]
        
        # 计算距离
        if self.position_type == PositionType.EUCLIDEAN:
            distances = torch.norm(relative_pos, dim=-1)  # [batch_size, num_agvs, num_agvs]
        elif self.position_type == PositionType.MANHATTAN:
            distances = torch.sum(torch.abs(relative_pos), dim=-1)
        else:
            distances = torch.norm(relative_pos, dim=-1)
        
        # 计算角度
        angles = torch.atan2(relative_pos[..., 1], relative_pos[..., 0])  # [batch_size, num_agvs, num_agvs]
        angle_features = torch.stack([torch.sin(angles), torch.cos(angles)], dim=-1)
        
        # 计算相对速度（如果提供）
        if agv_velocities is not None:
            vel_i = agv_velocities.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
            vel_j = agv_velocities.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
            relative_vel = vel_j - vel_i  # [batch_size, num_agvs, num_agvs, 2]
        else:
            relative_vel = torch.zeros_like(relative_pos)
        
        # 归一化距离
        normalized_distances = torch.clamp(distances / self.max_distance, 0, 1)
        
        return {
            'distances': distances,
            'normalized_distances': normalized_distances,
            'angles': angles,
            'angle_features': angle_features,
            'relative_positions': relative_pos,
            'relative_velocities': relative_vel
        }
    
    def forward(self, 
                agv_positions: torch.Tensor,
                agv_velocities: Optional[torch.Tensor] = None,
                agv_states: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            agv_states: AGV状态 [batch_size, num_agvs, state_dim]
            
        Returns:
            position_encodings: 位置编码 [batch_size, num_agvs, num_agvs, embed_dim]
        """
        # 计算相对位置信息
        relative_info = self.compute_relative_positions(agv_positions, agv_velocities)
        
        batch_size, num_agvs, _ = agv_positions.shape
        
        if self.learnable:
            # 可学习的位置编码
            
            # 距离编码
            distance_input = relative_info['normalized_distances'].unsqueeze(-1)
            distance_encoding = self.distance_encoder(distance_input)
            
            # 角度编码
            angle_encoding = self.angle_encoder(relative_info['angle_features'])
            
            # 速度编码
            velocity_encoding = self.velocity_encoder(relative_info['relative_velocities'])
            
            # 上下文编码（基于AGV状态）
            if agv_states is not None:
                # 计算状态差异
                state_i = agv_states.unsqueeze(2)  # [batch_size, num_agvs, 1, state_dim]
                state_j = agv_states.unsqueeze(1)  # [batch_size, 1, num_agvs, state_dim]
                
                # 选择关键状态特征：载重、状态、任务数量、速度
                key_features_i = state_i[..., [4, 3, 6, 9]]  # 假设这些是关键特征的索引
                key_features_j = state_j[..., [4, 3, 6, 9]]
                
                context_features = torch.cat([
                    key_features_i, key_features_j
                ], dim=-1)  # [batch_size, num_agvs, num_agvs, 8]
                
                # 降维到4维
                context_features = context_features[..., :4]
                context_encoding = self.context_encoder(context_features)
            else:
                context_encoding = torch.zeros(
                    batch_size, num_agvs, num_agvs, self.context_dim,
                    device=agv_positions.device
                )
            
            # 拼接所有编码
            position_encodings = torch.cat([
                distance_encoding, angle_encoding, velocity_encoding, context_encoding
            ], dim=-1)
            
        else:
            # 固定的正弦位置编码
            distance_indices = (relative_info['normalized_distances'] * 999).long()
            distance_indices = torch.clamp(distance_indices, 0, 999)
            
            angle_indices = ((relative_info['angles'] + math.pi) / (2 * math.pi) * 999).long()
            angle_indices = torch.clamp(angle_indices, 0, 999)
            
            distance_encoding = self.distance_encoding[distance_indices]
            angle_encoding = self.angle_encoding[angle_indices]
            
            position_encodings = torch.cat([distance_encoding, angle_encoding], dim=-1)
        
        return position_encodings


class GridRelativePositionEncoding(RelativePositionEncoding):
    """基于网格的相对位置编码"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 grid_width: int = 26,
                 grid_height: int = 10,
                 max_distance: float = 50.0,
                 include_obstacles: bool = True):
        """
        初始化网格相对位置编码
        
        Args:
            embed_dim: 嵌入维度
            grid_width: 网格宽度
            grid_height: 网格高度
            max_distance: 最大距离
            include_obstacles: 是否包含障碍物信息
        """
        super().__init__(embed_dim, max_distance, PositionType.GRID_BASED, True)
        
        self.grid_width = grid_width
        self.grid_height = grid_height
        self.include_obstacles = include_obstacles
        
        # 网格位置编码
        self.grid_x_embedding = nn.Embedding(grid_width, embed_dim // 4)
        self.grid_y_embedding = nn.Embedding(grid_height, embed_dim // 4)
        
        # 路径距离编码（考虑障碍物）
        if include_obstacles:
            self.path_distance_encoder = nn.Sequential(
                nn.Linear(1, embed_dim // 4),
                nn.ReLU(),
                nn.Linear(embed_dim // 4, embed_dim // 4)
            )
        
        # 区域编码（将网格划分为不同区域）
        self.region_encoder = nn.Sequential(
            nn.Linear(2, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, embed_dim // 4)
        )
    
    def compute_grid_positions(self, agv_positions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算网格位置
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            
        Returns:
            grid_x: 网格X坐标 [batch_size, num_agvs]
            grid_y: 网格Y坐标 [batch_size, num_agvs]
        """
        # 将连续坐标转换为网格坐标
        grid_x = torch.clamp(agv_positions[..., 0].long(), 0, self.grid_width - 1)
        grid_y = torch.clamp(agv_positions[..., 1].long(), 0, self.grid_height - 1)
        
        return grid_x, grid_y
    
    def compute_path_distance(self, 
                            pos_i: torch.Tensor,
                            pos_j: torch.Tensor,
                            obstacle_map: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算考虑障碍物的路径距离
        
        Args:
            pos_i: 起始位置 [batch_size, num_agvs, 1, 2]
            pos_j: 目标位置 [batch_size, 1, num_agvs, 2]
            obstacle_map: 障碍物地图 [grid_height, grid_width]
            
        Returns:
            path_distances: 路径距离 [batch_size, num_agvs, num_agvs]
        """
        if obstacle_map is None:
            # 如果没有障碍物地图，使用曼哈顿距离近似
            return torch.sum(torch.abs(pos_j - pos_i), dim=-1)
        
        # 简化的路径距离计算（实际应用中可以使用A*算法）
        manhattan_dist = torch.sum(torch.abs(pos_j - pos_i), dim=-1)
        
        # 添加障碍物惩罚（简化版本）
        # 在实际实现中，这里应该使用更复杂的路径规划算法
        obstacle_penalty = manhattan_dist * 0.1  # 简单的惩罚项
        
        return manhattan_dist + obstacle_penalty
    
    def forward(self, 
                agv_positions: torch.Tensor,
                agv_velocities: Optional[torch.Tensor] = None,
                agv_states: Optional[torch.Tensor] = None,
                obstacle_map: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播（网格版本）
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            agv_states: AGV状态 [batch_size, num_agvs, state_dim]
            obstacle_map: 障碍物地图 [grid_height, grid_width]
            
        Returns:
            position_encodings: 位置编码 [batch_size, num_agvs, num_agvs, embed_dim]
        """
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算网格位置
        grid_x, grid_y = self.compute_grid_positions(agv_positions)
        
        # 网格位置编码
        x_encoding = self.grid_x_embedding(grid_x)  # [batch_size, num_agvs, embed_dim//4]
        y_encoding = self.grid_y_embedding(grid_y)  # [batch_size, num_agvs, embed_dim//4]
        
        # 计算相对网格位置
        grid_x_i = grid_x.unsqueeze(2)  # [batch_size, num_agvs, 1]
        grid_x_j = grid_x.unsqueeze(1)  # [batch_size, 1, num_agvs]
        grid_y_i = grid_y.unsqueeze(2)  # [batch_size, num_agvs, 1]
        grid_y_j = grid_y.unsqueeze(1)  # [batch_size, 1, num_agvs]
        
        relative_grid_x = grid_x_j - grid_x_i  # [batch_size, num_agvs, num_agvs]
        relative_grid_y = grid_y_j - grid_y_i  # [batch_size, num_agvs, num_agvs]
        
        # 相对位置编码
        pos_i = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        pos_j = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        
        # 路径距离编码
        if self.include_obstacles:
            path_distances = self.compute_path_distance(pos_i, pos_j, obstacle_map)
            normalized_path_distances = torch.clamp(path_distances / self.max_distance, 0, 1)
            path_encoding = self.path_distance_encoder(normalized_path_distances.unsqueeze(-1))
        else:
            path_encoding = torch.zeros(
                batch_size, num_agvs, num_agvs, self.embed_dim // 4,
                device=agv_positions.device
            )
        
        # 区域编码
        region_features = torch.stack([
            relative_grid_x.float() / self.grid_width,
            relative_grid_y.float() / self.grid_height
        ], dim=-1)
        region_encoding = self.region_encoder(region_features)
        
        # 扩展位置编码到相对位置矩阵
        x_encoding_expanded = x_encoding.unsqueeze(2).expand(-1, -1, num_agvs, -1)
        y_encoding_expanded = y_encoding.unsqueeze(2).expand(-1, -1, num_agvs, -1)
        
        # 拼接所有编码
        position_encodings = torch.cat([
            x_encoding_expanded, y_encoding_expanded, path_encoding, region_encoding
        ], dim=-1)
        
        return position_encodings


class DirectionalRelativePositionEncoding(RelativePositionEncoding):
    """方向性相对位置编码"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 max_distance: float = 50.0,
                 num_directions: int = 8,
                 include_motion_prediction: bool = True):
        """
        初始化方向性相对位置编码
        
        Args:
            embed_dim: 嵌入维度
            max_distance: 最大距离
            num_directions: 方向数量
            include_motion_prediction: 是否包含运动预测
        """
        super().__init__(embed_dim, max_distance, PositionType.DIRECTIONAL, True)
        
        self.num_directions = num_directions
        self.include_motion_prediction = include_motion_prediction
        
        # 方向编码
        self.direction_embedding = nn.Embedding(num_directions, embed_dim // 4)
        
        # 相对方向编码
        self.relative_direction_encoder = nn.Sequential(
            nn.Linear(2, embed_dim // 4),  # 相对方向向量
            nn.ReLU(),
            nn.Linear(embed_dim // 4, embed_dim // 4)
        )
        
        # 运动预测编码
        if include_motion_prediction:
            self.motion_predictor = nn.Sequential(
                nn.Linear(4, embed_dim // 4),  # 位置 + 速度
                nn.ReLU(),
                nn.Linear(embed_dim // 4, embed_dim // 4)
            )
        
        # 接近性编码
        self.proximity_encoder = nn.Sequential(
            nn.Linear(3, embed_dim // 4),  # 距离、接近速度、碰撞风险
            nn.ReLU(),
            nn.Linear(embed_dim // 4, embed_dim // 4)
        )
    
    def compute_directions(self, agv_positions: torch.Tensor, agv_velocities: torch.Tensor) -> torch.Tensor:
        """
        计算AGV的运动方向
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            
        Returns:
            directions: 离散方向 [batch_size, num_agvs]
        """
        # 计算速度角度
        angles = torch.atan2(agv_velocities[..., 1], agv_velocities[..., 0])
        
        # 将角度离散化为方向
        angle_step = 2 * math.pi / self.num_directions
        directions = ((angles + math.pi) / angle_step).long()
        directions = torch.clamp(directions, 0, self.num_directions - 1)
        
        return directions
    
    def compute_collision_risk(self, 
                             agv_positions: torch.Tensor,
                             agv_velocities: torch.Tensor,
                             time_horizon: float = 2.0) -> torch.Tensor:
        """
        计算碰撞风险
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            time_horizon: 时间范围
            
        Returns:
            collision_risks: 碰撞风险 [batch_size, num_agvs, num_agvs]
        """
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 预测未来位置
        future_positions = agv_positions + agv_velocities * time_horizon
        
        # 计算未来距离
        pos_i = future_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        pos_j = future_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        future_distances = torch.norm(pos_j - pos_i, dim=-1)
        
        # 计算当前距离
        current_pos_i = agv_positions.unsqueeze(2)
        current_pos_j = agv_positions.unsqueeze(1)
        current_distances = torch.norm(current_pos_j - current_pos_i, dim=-1)
        
        # 计算接近速度
        approach_speed = (current_distances - future_distances) / time_horizon
        
        # 碰撞风险评估
        collision_threshold = 2.0  # AGV安全距离
        collision_risks = torch.sigmoid(-(future_distances - collision_threshold))
        
        # 如果正在远离，降低风险
        collision_risks = collision_risks * torch.sigmoid(approach_speed)
        
        return collision_risks
    
    def forward(self, 
                agv_positions: torch.Tensor,
                agv_velocities: torch.Tensor,
                agv_states: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播（方向性版本）
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            agv_states: AGV状态 [batch_size, num_agvs, state_dim]
            
        Returns:
            position_encodings: 位置编码 [batch_size, num_agvs, num_agvs, embed_dim]
        """
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算方向
        directions = self.compute_directions(agv_positions, agv_velocities)
        direction_encoding = self.direction_embedding(directions)
        
        # 计算相对位置和方向
        pos_i = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        pos_j = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        relative_pos = pos_j - pos_i  # [batch_size, num_agvs, num_agvs, 2]
        
        # 归一化相对位置
        distances = torch.norm(relative_pos, dim=-1, keepdim=True)
        normalized_relative_pos = relative_pos / (distances + 1e-8)
        
        relative_direction_encoding = self.relative_direction_encoder(normalized_relative_pos)
        
        # 运动预测编码
        if self.include_motion_prediction:
            vel_i = agv_velocities.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
            vel_j = agv_velocities.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
            
            motion_features = torch.cat([relative_pos, vel_j - vel_i], dim=-1)
            motion_encoding = self.motion_predictor(motion_features)
        else:
            motion_encoding = torch.zeros(
                batch_size, num_agvs, num_agvs, self.embed_dim // 4,
                device=agv_positions.device
            )
        
        # 接近性编码
        collision_risks = self.compute_collision_risk(agv_positions, agv_velocities)
        
        # 计算接近速度
        vel_i = agv_velocities.unsqueeze(2)
        vel_j = agv_velocities.unsqueeze(1)
        relative_vel = vel_j - vel_i
        approach_speed = torch.sum(relative_vel * normalized_relative_pos, dim=-1)
        
        proximity_features = torch.stack([
            distances.squeeze(-1) / self.max_distance,
            torch.sigmoid(approach_speed),
            collision_risks
        ], dim=-1)
        
        proximity_encoding = self.proximity_encoder(proximity_features)
        
        # 扩展方向编码
        direction_encoding_expanded = direction_encoding.unsqueeze(2).expand(-1, -1, num_agvs, -1)
        
        # 拼接所有编码
        position_encodings = torch.cat([
            direction_encoding_expanded, relative_direction_encoding, 
            motion_encoding, proximity_encoding
        ], dim=-1)
        
        return position_encodings


def create_relative_position_encoding(encoding_type: str = "euclidean",
                                    embed_dim: int = 64,
                                    **kwargs) -> RelativePositionEncoding:
    """
    创建相对位置编码的工厂函数
    
    Args:
        encoding_type: 编码类型 ("euclidean", "grid", "directional")
        embed_dim: 嵌入维度
        **kwargs: 额外参数
        
    Returns:
        position_encoding: 相对位置编码实例
    """
    if encoding_type == "euclidean":
        return RelativePositionEncoding(embed_dim=embed_dim, **kwargs)
    elif encoding_type == "grid":
        return GridRelativePositionEncoding(embed_dim=embed_dim, **kwargs)
    elif encoding_type == "directional":
        return DirectionalRelativePositionEncoding(embed_dim=embed_dim, **kwargs)
    else:
        raise ValueError(f"未知的编码类型: {encoding_type}")
