# 多AGV协同调度系统奖励函数设计

## 奖励函数概述

基于原项目的深入分析，我们设计了一个多层次、多目标的奖励函数，旨在平衡任务完成效率、路径优化、碰撞避免和AGV间协作。

## 奖励函数组成

### 1. 基础时间惩罚 (-0.1)
```python
reward -= 0.1  # 每个时间步的基础惩罚
```
**目的**: 鼓励AGV快速完成任务，避免无意义的延迟

### 2. 动作成本
```python
if action != AGVAction.WAIT:
    reward -= 0.05  # 移动成本
else:
    reward -= 0.1   # 等待惩罚
```
**目的**: 
- 移动有成本，鼓励高效路径规划
- 等待惩罚更高，鼓励积极行动

### 3. 目标导向奖励 (+0.5/-0.2)
```python
if new_distance < old_distance:
    reward += 0.5  # 接近目标奖励
elif new_distance > old_distance:
    reward -= 0.2  # 远离目标惩罚

# 距离奖励
distance_bonus = 0.1 * (1.0 - new_distance / max_distance)
reward += distance_bonus
```
**目的**: 
- 强化朝向目标的移动
- 距离越近奖励越高
- 防止无目的游荡

### 4. 任务完成奖励 (+20.0 基础)
```python
base_completion_reward = 20.0

# 优先级奖励
if task.priority == TaskPriority.HIGH:
    base_completion_reward += 5.0
elif task.priority == TaskPriority.URGENT:
    base_completion_reward += 10.0

reward += completed_tasks * base_completion_reward
```
**目的**: 
- 任务完成是主要目标
- 高优先级任务获得额外奖励
- 鼓励处理紧急任务

### 5. 效率奖励 (+2.0 * 效率比)
```python
efficiency_ratio = agv.total_tasks_completed / max(agv.total_distance, 1)
reward += efficiency_ratio * 2.0
```
**目的**: 
- 奖励高效的路径规划
- 鼓励用最短路径完成更多任务

### 6. 负载均衡奖励 (+0.2/-0.3)
```python
load_ratio = agv.load_ratio
if 0.3 <= load_ratio <= 0.8:  # 理想负载范围
    reward += 0.2
elif load_ratio > 0.9:  # 过载惩罚
    reward -= 0.3
```
**目的**: 
- 维持合理的负载水平
- 避免过载影响效率
- 鼓励负载均衡

### 7. 协作奖励 (动态计算)
```python
def _calculate_collaboration_reward(self, agv: AGVModel) -> float:
    collaboration_reward = 0.0
    
    for other_agv in self.agvs:
        distance = self.grid_world.get_distance(agv.position, other_agv.position)
        
        # 避免过于接近
        if distance == 1:
            collaboration_reward -= 0.1
        elif distance == 0:
            collaboration_reward -= 0.5
        
        # 目标相近的协作奖励
        if target_distance <= 3:
            collaboration_reward += 0.1
    
    return collaboration_reward
```
**目的**: 
- 防止AGV拥堵
- 鼓励合理的空间分布
- 奖励协作行为

### 8. 奖励裁剪 (稳定性保证)
```python
reward = np.clip(reward, -10.0, 50.0)
```
**目的**: 
- 防止奖励爆炸
- 确保训练稳定性
- 控制奖励方差

## 奖励函数权重设计

### 权重分配原理
1. **任务完成** (权重最高): 20.0 基础奖励
2. **目标导向** (权重中等): 0.5 接近奖励
3. **效率优化** (权重中等): 2.0 * 效率比
4. **协作行为** (权重较低): ±0.1-0.5
5. **基础惩罚** (权重最低): -0.1

### 权重平衡考虑
- **完成率 vs 效率**: 优先保证任务完成，其次考虑效率
- **个体 vs 协作**: 个体任务完成为主，协作为辅
- **奖励 vs 惩罚**: 正向激励为主，负向约束为辅

## 奖励函数特性

### 1. 多目标优化
- 同时优化完成率、效率、协作质量
- 平衡短期收益和长期策略

### 2. 自适应性
- 根据任务优先级动态调整
- 考虑AGV负载状态
- 响应环境复杂度变化

### 3. 稳定性
- 奖励裁剪防止梯度爆炸
- 平滑的奖励曲线
- 避免奖励稀疏性问题

### 4. 可解释性
- 每个组件有明确的业务含义
- 权重设置有理论依据
- 便于调试和优化

## 与课程学习的结合

### 阶段性调整
```python
# 阶段1 (2 AGV): 注重基础任务完成
# 阶段2 (3 AGV): 增加协作奖励权重
# 阶段3 (4 AGV): 强化效率和负载均衡
```

### 难度递增
- 早期阶段：简化奖励函数，专注任务完成
- 中期阶段：引入效率和协作考虑
- 后期阶段：全面优化，精细调节

## 性能指标

### 训练指标
- **平均奖励**: 反映整体性能
- **任务完成率**: 核心业务指标
- **路径效率**: 资源利用效率
- **碰撞率**: 安全性指标

### 评估指标
- **收敛速度**: 奖励函数学习效率
- **稳定性**: 训练过程稳定性
- **泛化能力**: 不同场景适应性

## 调优策略

### 1. 权重调优
- 使用网格搜索优化关键权重
- 基于业务优先级调整权重比例
- 监控各组件贡献度

### 2. 阈值调优
- 负载均衡阈值优化
- 协作距离阈值调整
- 奖励裁剪范围优化

### 3. 动态调整
- 基于训练进度动态调整权重
- 根据性能指标自适应优化
- 结合课程学习阶段性调整

## 实验验证

### 对比实验
1. **基础奖励 vs 完整奖励**: 验证各组件有效性
2. **固定权重 vs 动态权重**: 验证自适应性
3. **单目标 vs 多目标**: 验证平衡性

### 消融实验
- 逐个移除奖励组件，观察性能变化
- 验证每个组件的必要性
- 优化组件间的权重配比

## 结论

本奖励函数设计充分考虑了多AGV协同调度的复杂性，通过多层次、多目标的奖励机制，有效平衡了任务完成、效率优化、安全性和协作性等多个目标。结合课程学习机制，能够实现从简单到复杂的渐进式学习，确保训练的稳定性和最终性能的优越性。
