"""
注意力权重计算与输出

实现注意力权重的softmax计算和加权求和输出
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List
import math


class AttentionOutput(nn.Module):
    """注意力输出层"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 output_dim: int = 64,
                 temperature: float = 1.0,
                 use_gumbel_softmax: bool = False,
                 gumbel_tau: float = 1.0,
                 use_sparsemax: bool = False,
                 dropout: float = 0.1):
        """
        初始化注意力输出层
        
        Args:
            embed_dim: 嵌入维度
            output_dim: 输出维度
            temperature: 温度参数
            use_gumbel_softmax: 是否使用Gumbel Softmax
            gumbel_tau: Gumbel Softmax温度
            use_sparsemax: 是否使用Sparsemax
            dropout: Dropout概率
        """
        super(AttentionOutput, self).__init__()
        
        self.embed_dim = embed_dim
        self.output_dim = output_dim
        self.temperature = temperature
        self.use_gumbel_softmax = use_gumbel_softmax
        self.gumbel_tau = gumbel_tau
        self.use_sparsemax = use_sparsemax
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Linear(embed_dim, output_dim),
            nn.LayerNorm(output_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim, output_dim)
        )
        
        # 注意力权重后处理
        self.attention_post_process = nn.Sequential(
            nn.Linear(1, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def _sparsemax(self, logits: torch.Tensor, dim: int = -1) -> torch.Tensor:
        """
        Sparsemax激活函数
        
        Args:
            logits: 输入logits
            dim: 计算维度
            
        Returns:
            sparse_probs: 稀疏概率分布
        """
        # 对logits进行排序
        sorted_logits, _ = torch.sort(logits, dim=dim, descending=True)
        
        # 计算累积和
        cumsum = torch.cumsum(sorted_logits, dim=dim)
        
        # 计算k值
        k_range = torch.arange(1, logits.size(dim) + 1, device=logits.device, dtype=logits.dtype)
        if dim != -1:
            # 调整k_range的形状以匹配logits
            shape = [1] * logits.dim()
            shape[dim] = -1
            k_range = k_range.view(shape)
        
        # 计算条件
        condition = 1 + k_range * sorted_logits > cumsum
        
        # 找到最大的k
        k_max = torch.sum(condition, dim=dim, keepdim=True)
        
        # 计算阈值
        tau = (torch.gather(cumsum, dim, k_max - 1) - 1) / k_max.float()
        
        # 应用阈值
        sparse_probs = torch.clamp(logits - tau, min=0)
        
        return sparse_probs
    
    def compute_attention_weights(self, 
                                attention_scores: torch.Tensor,
                                attention_mask: Optional[torch.Tensor] = None,
                                training: bool = True) -> torch.Tensor:
        """
        计算注意力权重
        
        Args:
            attention_scores: 注意力分数 [batch_size, num_agvs, num_tasks]
            attention_mask: 注意力掩码 [batch_size, num_agvs, num_tasks]
            training: 是否在训练模式
            
        Returns:
            attention_weights: 注意力权重 [batch_size, num_agvs, num_tasks]
        """
        # 应用温度缩放
        scaled_scores = attention_scores / self.temperature
        
        # 应用注意力掩码
        if attention_mask is not None:
            scaled_scores = scaled_scores.masked_fill(attention_mask == 0, float('-inf'))
        
        # 计算注意力权重
        if self.use_sparsemax:
            # 使用Sparsemax
            attention_weights = self._sparsemax(scaled_scores, dim=-1)
        elif self.use_gumbel_softmax and training:
            # 使用Gumbel Softmax（仅在训练时）
            attention_weights = F.gumbel_softmax(scaled_scores, tau=self.gumbel_tau, hard=False, dim=-1)
        else:
            # 标准Softmax
            attention_weights = F.softmax(scaled_scores, dim=-1)
        
        return attention_weights
    
    def apply_attention(self, 
                       attention_weights: torch.Tensor,
                       values: torch.Tensor) -> torch.Tensor:
        """
        应用注意力权重到值
        
        Args:
            attention_weights: 注意力权重 [batch_size, num_agvs, num_tasks]
            values: 值张量 [batch_size, num_tasks, embed_dim]
            
        Returns:
            attended_output: 注意力输出 [batch_size, num_agvs, embed_dim]
        """
        # 加权求和
        attended_output = torch.bmm(attention_weights, values)
        # [batch_size, num_agvs, embed_dim]
        
        return attended_output
    
    def forward(self, 
                attention_scores: torch.Tensor,
                values: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                return_weights: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播
        
        Args:
            attention_scores: 注意力分数 [batch_size, num_agvs, num_tasks]
            values: 值张量 [batch_size, num_tasks, embed_dim]
            attention_mask: 注意力掩码 [batch_size, num_agvs, num_tasks]
            return_weights: 是否返回注意力权重
            
        Returns:
            output: 最终输出 [batch_size, num_agvs, output_dim]
            attention_weights: 注意力权重（可选）
        """
        # 计算注意力权重
        attention_weights = self.compute_attention_weights(
            attention_scores, attention_mask, self.training
        )
        
        # 应用注意力
        attended_output = self.apply_attention(attention_weights, values)
        
        # 输出投影
        output = self.output_projection(attended_output)
        
        if return_weights:
            return output, attention_weights
        else:
            return output, None


class TaskAllocationOutput(nn.Module):
    """任务分配输出层"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_allocation_modes: int = 3,
                 temperature: float = 1.0,
                 use_hard_assignment: bool = False,
                 assignment_threshold: float = 0.5):
        """
        初始化任务分配输出层
        
        Args:
            embed_dim: 嵌入维度
            num_allocation_modes: 分配模式数量
            temperature: 温度参数
            use_hard_assignment: 是否使用硬分配
            assignment_threshold: 分配阈值
        """
        super(TaskAllocationOutput, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_allocation_modes = num_allocation_modes
        self.temperature = temperature
        self.use_hard_assignment = use_hard_assignment
        self.assignment_threshold = assignment_threshold
        
        # 分配概率计算
        self.allocation_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim // 2, 1),
            nn.Sigmoid()
        )
        
        # 分配模式选择
        self.mode_selector = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, num_allocation_modes)
        )
        
        # 优先级计算
        self.priority_head = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1)
        )
    
    def compute_allocation_probabilities(self, 
                                       attended_features: torch.Tensor,
                                       attention_weights: torch.Tensor) -> torch.Tensor:
        """
        计算分配概率
        
        Args:
            attended_features: 注意力特征 [batch_size, num_agvs, embed_dim]
            attention_weights: 注意力权重 [batch_size, num_agvs, num_tasks]
            
        Returns:
            allocation_probs: 分配概率 [batch_size, num_agvs, num_tasks]
        """
        # 基于注意力特征计算分配倾向
        allocation_tendency = self.allocation_head(attended_features)  # [batch_size, num_agvs, 1]
        
        # 结合注意力权重
        allocation_probs = attention_weights * allocation_tendency
        
        # 温度缩放
        allocation_probs = allocation_probs / self.temperature
        
        # 归一化（每个AGV的分配概率和为1）
        allocation_probs = F.softmax(allocation_probs, dim=-1)
        
        return allocation_probs
    
    def compute_allocation_modes(self, 
                               attended_features: torch.Tensor) -> torch.Tensor:
        """
        计算分配模式
        
        Args:
            attended_features: 注意力特征 [batch_size, num_agvs, embed_dim]
            
        Returns:
            mode_probs: 模式概率 [batch_size, num_agvs, num_allocation_modes]
        """
        mode_logits = self.mode_selector(attended_features)
        mode_probs = F.softmax(mode_logits, dim=-1)
        
        return mode_probs
    
    def compute_task_priorities(self, 
                              attended_features: torch.Tensor) -> torch.Tensor:
        """
        计算任务优先级
        
        Args:
            attended_features: 注意力特征 [batch_size, num_agvs, embed_dim]
            
        Returns:
            priorities: 任务优先级 [batch_size, num_agvs, 1]
        """
        priorities = self.priority_head(attended_features)
        return priorities
    
    def make_hard_assignment(self, 
                           allocation_probs: torch.Tensor) -> torch.Tensor:
        """
        进行硬分配
        
        Args:
            allocation_probs: 分配概率 [batch_size, num_agvs, num_tasks]
            
        Returns:
            hard_assignment: 硬分配结果 [batch_size, num_agvs, num_tasks]
        """
        if self.use_hard_assignment:
            # 每个AGV选择概率最高的任务
            max_indices = torch.argmax(allocation_probs, dim=-1, keepdim=True)
            hard_assignment = torch.zeros_like(allocation_probs)
            hard_assignment.scatter_(-1, max_indices, 1.0)
            
            # 应用阈值
            mask = allocation_probs.gather(-1, max_indices) > self.assignment_threshold
            hard_assignment = hard_assignment * mask.float()
            
            return hard_assignment
        else:
            return allocation_probs
    
    def forward(self, 
                attended_features: torch.Tensor,
                attention_weights: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            attended_features: 注意力特征 [batch_size, num_agvs, embed_dim]
            attention_weights: 注意力权重 [batch_size, num_agvs, num_tasks]
            
        Returns:
            output_dict: 输出字典
        """
        # 计算分配概率
        allocation_probs = self.compute_allocation_probabilities(
            attended_features, attention_weights
        )
        
        # 计算分配模式
        mode_probs = self.compute_allocation_modes(attended_features)
        
        # 计算任务优先级
        priorities = self.compute_task_priorities(attended_features)
        
        # 硬分配（如果启用）
        final_assignment = self.make_hard_assignment(allocation_probs)
        
        return {
            'allocation_probs': allocation_probs,
            'final_assignment': final_assignment,
            'mode_probs': mode_probs,
            'priorities': priorities,
            'attention_weights': attention_weights
        }


class AdaptiveAttentionOutput(AttentionOutput):
    """自适应注意力输出"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 output_dim: int = 64,
                 temperature: float = 1.0,
                 adaptive_temperature: bool = True,
                 dropout: float = 0.1):
        """
        初始化自适应注意力输出
        
        Args:
            embed_dim: 嵌入维度
            output_dim: 输出维度
            temperature: 初始温度参数
            adaptive_temperature: 是否自适应调整温度
            dropout: Dropout概率
        """
        super().__init__(embed_dim, output_dim, temperature, False, 1.0, False, dropout)
        
        self.adaptive_temperature = adaptive_temperature
        
        if adaptive_temperature:
            # 温度预测网络
            self.temperature_predictor = nn.Sequential(
                nn.Linear(embed_dim, embed_dim // 4),
                nn.ReLU(),
                nn.Linear(embed_dim // 4, 1),
                nn.Softplus()  # 确保温度为正
            )
    
    def _compute_adaptive_temperature(self, 
                                    attention_scores: torch.Tensor,
                                    values: torch.Tensor) -> torch.Tensor:
        """
        计算自适应温度
        
        Args:
            attention_scores: 注意力分数
            values: 值张量
            
        Returns:
            adaptive_temp: 自适应温度
        """
        # 计算值的平均特征
        value_mean = values.mean(dim=(0, 1))  # [embed_dim]
        
        # 预测温度
        adaptive_temp = self.temperature_predictor(value_mean)
        
        # 限制温度范围
        adaptive_temp = torch.clamp(adaptive_temp, 0.1, 5.0)
        
        return adaptive_temp
    
    def forward(self, 
                attention_scores: torch.Tensor,
                values: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                return_weights: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播（自适应版本）
        """
        # 计算自适应温度
        if self.adaptive_temperature:
            adaptive_temp = self._compute_adaptive_temperature(attention_scores, values)
            original_temp = self.temperature
            self.temperature = adaptive_temp.item()
        
        # 调用父类方法
        output, attention_weights = super().forward(
            attention_scores, values, attention_mask, return_weights
        )
        
        # 恢复原始温度
        if self.adaptive_temperature:
            self.temperature = original_temp
        
        return output, attention_weights


class MultiScaleAttentionOutput(nn.Module):
    """多尺度注意力输出"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 output_dim: int = 64,
                 scales: List[float] = [0.5, 1.0, 2.0],
                 fusion_method: str = "weighted"):
        """
        初始化多尺度注意力输出
        
        Args:
            embed_dim: 嵌入维度
            output_dim: 输出维度
            scales: 温度尺度列表
            fusion_method: 融合方法
        """
        super(MultiScaleAttentionOutput, self).__init__()
        
        self.scales = scales
        self.num_scales = len(scales)
        self.fusion_method = fusion_method
        
        # 为每个尺度创建输出层
        self.scale_outputs = nn.ModuleList([
            AttentionOutput(embed_dim, output_dim, temperature=scale)
            for scale in scales
        ])
        
        # 尺度融合
        if fusion_method == "weighted":
            self.scale_weights = nn.Parameter(torch.ones(self.num_scales) / self.num_scales)
        elif fusion_method == "concat":
            self.fusion_layer = nn.Sequential(
                nn.Linear(output_dim * self.num_scales, output_dim),
                nn.LayerNorm(output_dim),
                nn.ReLU()
            )
    
    def forward(self, 
                attention_scores: torch.Tensor,
                values: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                return_weights: bool = False) -> Tuple[torch.Tensor, Optional[List[torch.Tensor]]]:
        """
        前向传播
        """
        scale_outputs = []
        scale_weights_list = []
        
        # 计算每个尺度的输出
        for scale_output in self.scale_outputs:
            output, weights = scale_output(
                attention_scores, values, attention_mask, return_weights
            )
            scale_outputs.append(output)
            if return_weights:
                scale_weights_list.append(weights)
        
        # 融合不同尺度的输出
        if self.fusion_method == "weighted":
            weights = F.softmax(self.scale_weights, dim=0)
            final_output = sum(
                weight * output for weight, output in zip(weights, scale_outputs)
            )
        elif self.fusion_method == "concat":
            concatenated = torch.cat(scale_outputs, dim=-1)
            final_output = self.fusion_layer(concatenated)
        else:
            # 默认平均
            final_output = sum(scale_outputs) / self.num_scales
        
        if return_weights:
            return final_output, scale_weights_list
        else:
            return final_output, None
