"""
配置管理系统

支持YAML配置文件的加载、验证和管理
"""

import os
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass, field


@dataclass
class EnvironmentConfig:
    """环境配置"""
    map_width: int = 26
    map_height: int = 10
    num_shelves: int = 15
    shelf_width: int = 4
    shelf_height: int = 2
    num_agvs: int = 4
    num_tasks: int = 16
    agv_capacity: int = 25
    task_weights: list = field(default_factory=lambda: [5, 10])
    render_mode: str = "human"
    max_episode_steps: int = 1000


@dataclass
class AttentionConfig:
    """注意力机制配置"""
    embed_dim: int = 64
    num_heads: int = 8
    sparse_k: int = 8
    temperature: float = 1.0
    dropout: float = 0.1
    
    # 约束权重
    lambda_distance: float = 2.0
    lambda_capacity: float = 1.0
    lambda_priority: float = 0.5
    lambda_deadline: float = 1.5
    
    # 时序一致性
    lambda_temporal: float = 0.1
    temporal_window: int = 5


@dataclass
class NetworkConfig:
    """网络架构配置"""
    policy_hidden_dims: list = field(default_factory=lambda: [512, 256])
    value_hidden_dims: list = field(default_factory=lambda: [512, 256])
    activation: str = "relu"
    layer_norm: bool = True
    dropout: float = 0.1


@dataclass
class TrainingConfig:
    """训练配置"""
    # PPO参数
    lr: float = 3e-4
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_param: float = 0.2
    vf_loss_coeff: float = 0.5
    entropy_coeff: float = 0.01
    
    # 训练参数
    num_sgd_iter: int = 10
    sgd_minibatch_size: int = 128
    train_batch_size: int = 4000
    num_workers: int = 4
    
    # 课程学习
    curriculum_stages: list = field(default_factory=lambda: [
        {"num_agvs": 2, "num_tasks": 8, "episodes": 1000},
        {"num_agvs": 3, "num_tasks": 12, "episodes": 1500},
        {"num_agvs": 4, "num_tasks": 16, "episodes": 2000}
    ])
    
    # 经验回放
    buffer_size: int = 100000
    prioritized_replay: bool = True
    alpha: float = 0.6
    beta: float = 0.4
    
    # 元学习
    meta_learning: bool = True
    inner_lr: float = 1e-3
    meta_lr: float = 1e-4
    num_inner_updates: int = 5


@dataclass
class ExperimentConfig:
    """实验配置"""
    name: str = "mappo_dual_attention"
    seed: int = 42
    num_episodes: int = 5000
    eval_interval: int = 100
    save_interval: int = 500
    log_interval: int = 10
    
    # 设备配置
    device: str = "cuda"
    num_gpus: int = 1
    
    # 输出路径
    checkpoint_dir: str = "checkpoints"
    log_dir: str = "logs"
    result_dir: str = "results"


class Config:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.env = EnvironmentConfig()
        self.attention = AttentionConfig()
        self.network = NetworkConfig()
        self.training = TrainingConfig()
        self.experiment = ExperimentConfig()
        
        if config_path and os.path.exists(config_path):
            self.load_from_yaml(config_path)
    
    def load_from_yaml(self, config_path: str):
        """从YAML文件加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        
        # 更新各个配置模块
        if 'environment' in config_dict:
            self._update_config(self.env, config_dict['environment'])
        
        if 'attention' in config_dict:
            self._update_config(self.attention, config_dict['attention'])
        
        if 'network' in config_dict:
            self._update_config(self.network, config_dict['network'])
        
        if 'training' in config_dict:
            self._update_config(self.training, config_dict['training'])
        
        if 'experiment' in config_dict:
            self._update_config(self.experiment, config_dict['experiment'])
    
    def _update_config(self, config_obj, config_dict: Dict[str, Any]):
        """更新配置对象"""
        for key, value in config_dict.items():
            if hasattr(config_obj, key):
                setattr(config_obj, key, value)
    
    def save_to_yaml(self, config_path: str):
        """保存配置到YAML文件"""
        config_dict = {
            'environment': self._config_to_dict(self.env),
            'attention': self._config_to_dict(self.attention),
            'network': self._config_to_dict(self.network),
            'training': self._config_to_dict(self.training),
            'experiment': self._config_to_dict(self.experiment)
        }
        
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
    
    def _config_to_dict(self, config_obj) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        return {k: v for k, v in config_obj.__dict__.items() if not k.startswith('_')}
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            # 环境配置验证
            assert self.env.map_width > 0 and self.env.map_height > 0
            assert self.env.num_agvs > 0 and self.env.num_tasks > 0
            assert self.env.agv_capacity > 0
            
            # 注意力配置验证
            assert self.attention.embed_dim > 0
            assert self.attention.num_heads > 0
            assert self.attention.sparse_k > 0
            
            # 训练配置验证
            assert 0 < self.training.lr < 1
            assert 0 < self.training.gamma <= 1
            assert 0 < self.training.clip_param < 1
            
            return True
        except AssertionError:
            return False
    
    def get_run_name(self) -> str:
        """生成运行名称"""
        return f"{self.experiment.name}_agv{self.env.num_agvs}_task{self.env.num_tasks}_seed{self.experiment.seed}"
    
    def __str__(self) -> str:
        """配置信息字符串表示"""
        return f"""
配置信息:
- 环境: {self.env.num_agvs}个AGV, {self.env.num_tasks}个任务
- 地图: {self.env.map_width}x{self.env.map_height}
- 注意力: {self.attention.num_heads}头, 嵌入维度{self.attention.embed_dim}
- 训练: 学习率{self.training.lr}, 批量大小{self.training.train_batch_size}
- 实验: {self.experiment.name}, 种子{self.experiment.seed}
"""
