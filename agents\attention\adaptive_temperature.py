"""
自适应温度机制

实现根据环境复杂度动态调节注意力分布的温度参数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, Tuple, Optional, List, Union
from enum import Enum


class TemperatureAdaptationMethod(Enum):
    """温度自适应方法枚举"""
    LEARNED = "learned"              # 学习的自适应
    ENTROPY_BASED = "entropy_based"  # 基于熵的自适应
    DENSITY_BASED = "density_based"  # 基于密度的自适应
    CONFLICT_BASED = "conflict_based" # 基于冲突的自适应
    HYBRID = "hybrid"                # 混合方法


class AdaptiveTemperatureController(nn.Module):
    """自适应温度控制器"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 base_temperature: float = 1.0,
                 temperature_range: Tuple[float, float] = (0.1, 5.0),
                 adaptation_method: TemperatureAdaptationMethod = TemperatureAdaptationMethod.HYBRID,
                 update_frequency: int = 10,
                 smoothing_factor: float = 0.9):
        """
        初始化自适应温度控制器
        
        Args:
            embed_dim: 嵌入维度
            base_temperature: 基础温度
            temperature_range: 温度范围
            adaptation_method: 自适应方法
            update_frequency: 更新频率
            smoothing_factor: 平滑因子
        """
        super(AdaptiveTemperatureController, self).__init__()
        
        self.embed_dim = embed_dim
        self.base_temperature = base_temperature
        self.temperature_range = temperature_range
        self.adaptation_method = adaptation_method
        self.update_frequency = update_frequency
        self.smoothing_factor = smoothing_factor
        
        # 当前温度（可学习参数）
        self.current_temperature = nn.Parameter(torch.tensor(base_temperature))
        
        # 温度历史记录
        self.register_buffer('temperature_history', torch.zeros(100))
        self.register_buffer('history_pointer', torch.tensor(0, dtype=torch.long))
        
        # 学习的温度预测器
        if adaptation_method in [TemperatureAdaptationMethod.LEARNED, TemperatureAdaptationMethod.HYBRID]:
            self.temperature_predictor = nn.Sequential(
                nn.Linear(embed_dim + 6, embed_dim // 2),  # 环境特征 + 统计特征
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(embed_dim // 2, embed_dim // 4),
                nn.ReLU(),
                nn.Linear(embed_dim // 4, 1),
                nn.Sigmoid()  # 输出0-1，后续映射到温度范围
            )
        
        # 环境复杂度评估器
        self.complexity_evaluator = EnvironmentComplexityEvaluator(embed_dim)
        
        # 冲突检测器
        self.conflict_detector = ConflictDetector(embed_dim)
        
        # 温度调整历史
        self.adjustment_history = []
        self.step_counter = 0
    
    def compute_entropy_based_temperature(self, 
                                        attention_scores: torch.Tensor) -> torch.Tensor:
        """
        基于注意力熵计算温度
        
        Args:
            attention_scores: 注意力分数 [batch_size, num_agvs, num_agvs]
            
        Returns:
            temperature: 自适应温度
        """
        # 计算注意力分布的熵
        attention_probs = F.softmax(attention_scores / self.current_temperature, dim=-1)
        entropy = -torch.sum(attention_probs * torch.log(attention_probs + 1e-8), dim=-1)
        
        # 归一化熵
        max_entropy = math.log(attention_scores.shape[-1])
        normalized_entropy = entropy / max_entropy
        
        # 平均熵
        avg_entropy = torch.mean(normalized_entropy)
        
        # 根据熵调整温度
        # 高熵 -> 低温度（更尖锐的分布）
        # 低熵 -> 高温度（更平滑的分布）
        target_temperature = self.temperature_range[0] + (self.temperature_range[1] - self.temperature_range[0]) * (1 - avg_entropy)
        
        return target_temperature
    
    def compute_density_based_temperature(self, 
                                        agv_positions: torch.Tensor,
                                        agv_embeddings: torch.Tensor) -> torch.Tensor:
        """
        基于环境密度计算温度
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            
        Returns:
            temperature: 自适应温度
        """
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算局部密度
        pos_i = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        pos_j = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        distances = torch.norm(pos_j - pos_i, dim=-1)  # [batch_size, num_agvs, num_agvs]
        
        # 计算每个AGV周围5米内的邻居数量
        neighbor_counts = torch.sum((distances < 5.0) & (distances > 0), dim=-1)
        avg_density = torch.mean(neighbor_counts.float())
        
        # 计算全局分散程度
        center = torch.mean(agv_positions, dim=1, keepdim=True)  # [batch_size, 1, 2]
        distances_to_center = torch.norm(agv_positions - center, dim=-1)
        dispersion = torch.std(distances_to_center)
        
        # 密度归一化
        normalized_density = torch.clamp(avg_density / (num_agvs - 1), 0, 1)
        normalized_dispersion = torch.clamp(dispersion / 15.0, 0, 1)  # 假设最大分散距离为15
        
        # 高密度 -> 低温度（需要更精确的协作）
        # 高分散 -> 高温度（需要更广泛的关注）
        density_factor = 1 - normalized_density
        dispersion_factor = normalized_dispersion
        
        combined_factor = (density_factor + dispersion_factor) / 2
        target_temperature = self.temperature_range[0] + (self.temperature_range[1] - self.temperature_range[0]) * combined_factor
        
        return target_temperature
    
    def compute_conflict_based_temperature(self, 
                                         agv_positions: torch.Tensor,
                                         agv_velocities: torch.Tensor,
                                         agv_embeddings: torch.Tensor) -> torch.Tensor:
        """
        基于冲突程度计算温度
        
        Args:
            agv_positions: AGV位置
            agv_velocities: AGV速度
            agv_embeddings: AGV嵌入
            
        Returns:
            temperature: 自适应温度
        """
        # 检测潜在冲突
        conflict_score = self.conflict_detector.detect_conflicts(
            agv_positions, agv_velocities, agv_embeddings
        )
        
        # 高冲突 -> 低温度（需要更精确的协调）
        # 低冲突 -> 高温度（可以更灵活）
        normalized_conflict = torch.clamp(conflict_score, 0, 1)
        target_temperature = self.temperature_range[1] - (self.temperature_range[1] - self.temperature_range[0]) * normalized_conflict
        
        return target_temperature
    
    def compute_learned_temperature(self, 
                                  agv_embeddings: torch.Tensor,
                                  env_features: torch.Tensor) -> torch.Tensor:
        """
        基于学习的方法计算温度
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            env_features: 环境特征 [batch_size, 6]
            
        Returns:
            temperature: 自适应温度
        """
        # 计算全局AGV特征
        global_agv_features = torch.mean(agv_embeddings, dim=1)  # [batch_size, embed_dim]
        
        # 组合输入特征
        input_features = torch.cat([global_agv_features, env_features], dim=-1)
        
        # 预测温度
        temperature_ratio = self.temperature_predictor(input_features)  # [batch_size, 1]
        
        # 映射到温度范围
        target_temperature = (self.temperature_range[0] + 
                            (self.temperature_range[1] - self.temperature_range[0]) * temperature_ratio)
        
        return target_temperature.squeeze(-1)
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: Optional[torch.Tensor] = None,
                attention_scores: Optional[torch.Tensor] = None,
                force_update: bool = False) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_embeddings: AGV嵌入
            agv_positions: AGV位置
            agv_velocities: AGV速度
            attention_scores: 注意力分数
            force_update: 是否强制更新
            
        Returns:
            result_dict: 结果字典
        """
        self.step_counter += 1
        
        # 检查是否需要更新温度
        if not force_update and self.step_counter % self.update_frequency != 0:
            return {
                'temperature': self.current_temperature,
                'adaptation_info': {'updated': False}
            }
        
        # 计算环境特征
        complexity_features = self.complexity_evaluator.evaluate_complexity(
            agv_embeddings, agv_positions, agv_velocities
        )
        
        # 根据不同方法计算目标温度
        target_temperatures = []
        adaptation_info = {}
        
        if self.adaptation_method in [TemperatureAdaptationMethod.ENTROPY_BASED, TemperatureAdaptationMethod.HYBRID]:
            if attention_scores is not None:
                entropy_temp = self.compute_entropy_based_temperature(attention_scores)
                target_temperatures.append(entropy_temp)
                adaptation_info['entropy_temperature'] = entropy_temp.item()
        
        if self.adaptation_method in [TemperatureAdaptationMethod.DENSITY_BASED, TemperatureAdaptationMethod.HYBRID]:
            density_temp = self.compute_density_based_temperature(agv_positions, agv_embeddings)
            target_temperatures.append(density_temp)
            adaptation_info['density_temperature'] = density_temp.item()
        
        if self.adaptation_method in [TemperatureAdaptationMethod.CONFLICT_BASED, TemperatureAdaptationMethod.HYBRID]:
            if agv_velocities is not None:
                conflict_temp = self.compute_conflict_based_temperature(
                    agv_positions, agv_velocities, agv_embeddings
                )
                target_temperatures.append(conflict_temp)
                adaptation_info['conflict_temperature'] = conflict_temp.item()
        
        if self.adaptation_method in [TemperatureAdaptationMethod.LEARNED, TemperatureAdaptationMethod.HYBRID]:
            learned_temp = self.compute_learned_temperature(agv_embeddings, complexity_features)
            target_temperatures.append(learned_temp)
            adaptation_info['learned_temperature'] = learned_temp.item()
        
        # 融合多个温度估计
        if len(target_temperatures) > 1:
            # 加权平均
            weights = torch.ones(len(target_temperatures)) / len(target_temperatures)
            target_temperature = sum(w * t for w, t in zip(weights, target_temperatures))
        elif len(target_temperatures) == 1:
            target_temperature = target_temperatures[0]
        else:
            target_temperature = self.current_temperature
        
        # 平滑更新温度
        new_temperature = (self.smoothing_factor * self.current_temperature + 
                          (1 - self.smoothing_factor) * target_temperature)
        
        # 限制温度范围
        new_temperature = torch.clamp(new_temperature, self.temperature_range[0], self.temperature_range[1])
        
        # 更新温度
        with torch.no_grad():
            self.current_temperature.copy_(new_temperature)
        
        # 更新历史记录
        self._update_temperature_history(new_temperature)
        
        # 记录调整信息
        adaptation_info.update({
            'updated': True,
            'old_temperature': self.current_temperature.item(),
            'new_temperature': new_temperature.item(),
            'target_temperature': target_temperature.item() if isinstance(target_temperature, torch.Tensor) else target_temperature,
            'complexity_features': complexity_features.cpu().numpy() if isinstance(complexity_features, torch.Tensor) else complexity_features
        })
        
        return {
            'temperature': new_temperature,
            'adaptation_info': adaptation_info
        }
    
    def _update_temperature_history(self, temperature: torch.Tensor):
        """更新温度历史记录"""
        with torch.no_grad():
            self.temperature_history[self.history_pointer] = temperature
            self.history_pointer = (self.history_pointer + 1) % self.temperature_history.shape[0]
    
    def get_temperature_statistics(self) -> Dict[str, float]:
        """获取温度统计信息"""
        with torch.no_grad():
            valid_history = self.temperature_history[self.temperature_history > 0]
            
            if len(valid_history) == 0:
                return {
                    'mean_temperature': self.current_temperature.item(),
                    'std_temperature': 0.0,
                    'min_temperature': self.current_temperature.item(),
                    'max_temperature': self.current_temperature.item()
                }
            
            return {
                'mean_temperature': torch.mean(valid_history).item(),
                'std_temperature': torch.std(valid_history).item(),
                'min_temperature': torch.min(valid_history).item(),
                'max_temperature': torch.max(valid_history).item(),
                'current_temperature': self.current_temperature.item()
            }


class EnvironmentComplexityEvaluator(nn.Module):
    """环境复杂度评估器"""
    
    def __init__(self, embed_dim: int = 64):
        """
        初始化环境复杂度评估器
        
        Args:
            embed_dim: 嵌入维度
        """
        super(EnvironmentComplexityEvaluator, self).__init__()
        
        self.embed_dim = embed_dim
        
        # 复杂度特征提取器
        self.complexity_extractor = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 6)  # 6个复杂度特征
        )
    
    def evaluate_complexity(self, 
                          agv_embeddings: torch.Tensor,
                          agv_positions: torch.Tensor,
                          agv_velocities: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        评估环境复杂度
        
        Args:
            agv_embeddings: AGV嵌入
            agv_positions: AGV位置
            agv_velocities: AGV速度
            
        Returns:
            complexity_features: 复杂度特征 [batch_size, 6]
        """
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 1. 空间密度复杂度
        pos_i = agv_positions.unsqueeze(2)
        pos_j = agv_positions.unsqueeze(1)
        distances = torch.norm(pos_j - pos_i, dim=-1)
        
        # 平均最近邻距离
        distances_no_self = distances + torch.eye(num_agvs, device=distances.device).unsqueeze(0) * 1000
        min_distances = torch.min(distances_no_self, dim=-1)[0]
        spatial_density = 1.0 / (torch.mean(min_distances, dim=-1) + 1e-8)
        
        # 2. 运动复杂度
        if agv_velocities is not None:
            speed_variance = torch.var(torch.norm(agv_velocities, dim=-1), dim=-1)
            
            # 相对速度复杂度
            vel_i = agv_velocities.unsqueeze(2)
            vel_j = agv_velocities.unsqueeze(1)
            relative_velocities = vel_j - vel_i
            relative_speed_variance = torch.var(torch.norm(relative_velocities, dim=-1), dim=(1, 2))
        else:
            speed_variance = torch.zeros(batch_size, device=agv_positions.device)
            relative_speed_variance = torch.zeros(batch_size, device=agv_positions.device)
        
        # 3. 分布复杂度
        center = torch.mean(agv_positions, dim=1, keepdim=True)
        distances_to_center = torch.norm(agv_positions - center, dim=-1)
        distribution_variance = torch.var(distances_to_center, dim=-1)
        
        # 4. 任务复杂度（基于嵌入的方差）
        embedding_variance = torch.var(agv_embeddings, dim=1).mean(dim=-1)
        
        # 5. 协作需求复杂度
        # 计算需要协作的AGV对数量
        close_pairs = torch.sum((distances < 5.0) & (distances > 0), dim=(1, 2)).float()
        collaboration_complexity = close_pairs / (num_agvs * (num_agvs - 1))
        
        # 组合复杂度特征
        complexity_features = torch.stack([
            spatial_density,
            speed_variance,
            relative_speed_variance,
            distribution_variance,
            embedding_variance,
            collaboration_complexity
        ], dim=-1)
        
        # 归一化
        complexity_features = torch.sigmoid(complexity_features)
        
        return complexity_features


class ConflictDetector(nn.Module):
    """冲突检测器"""
    
    def __init__(self, embed_dim: int = 64):
        """
        初始化冲突检测器
        
        Args:
            embed_dim: 嵌入维度
        """
        super(ConflictDetector, self).__init__()
        
        self.embed_dim = embed_dim
        
        # 冲突预测网络
        self.conflict_predictor = nn.Sequential(
            nn.Linear(8, embed_dim // 4),  # 位置、速度、距离、角度特征
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1),
            nn.Sigmoid()
        )
    
    def detect_conflicts(self, 
                        agv_positions: torch.Tensor,
                        agv_velocities: torch.Tensor,
                        agv_embeddings: torch.Tensor,
                        time_horizon: float = 2.0) -> torch.Tensor:
        """
        检测潜在冲突
        
        Args:
            agv_positions: AGV位置
            agv_velocities: AGV速度
            agv_embeddings: AGV嵌入
            time_horizon: 预测时间范围
            
        Returns:
            conflict_score: 冲突分数
        """
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 预测未来位置
        future_positions = agv_positions + agv_velocities * time_horizon
        
        # 计算当前和未来距离
        pos_i = agv_positions.unsqueeze(2)
        pos_j = agv_positions.unsqueeze(1)
        current_distances = torch.norm(pos_j - pos_i, dim=-1)
        
        future_pos_i = future_positions.unsqueeze(2)
        future_pos_j = future_positions.unsqueeze(1)
        future_distances = torch.norm(future_pos_j - future_pos_i, dim=-1)
        
        # 计算接近速度
        approach_speed = (current_distances - future_distances) / time_horizon
        
        # 计算相对角度
        relative_pos = pos_j - pos_i
        relative_vel = agv_velocities.unsqueeze(1) - agv_velocities.unsqueeze(2)
        
        # 角度特征
        pos_angles = torch.atan2(relative_pos[..., 1], relative_pos[..., 0])
        vel_angles = torch.atan2(relative_vel[..., 1], relative_vel[..., 0])
        angle_diff = torch.abs(pos_angles - vel_angles)
        
        # 组合冲突特征
        conflict_features = torch.stack([
            current_distances / 10.0,  # 归一化距离
            future_distances / 10.0,
            torch.clamp(approach_speed / 5.0, 0, 1),  # 归一化接近速度
            torch.sin(angle_diff),
            torch.cos(angle_diff),
            torch.norm(agv_velocities.unsqueeze(1), dim=-1) / 5.0,  # 归一化速度
            torch.norm(agv_velocities.unsqueeze(2), dim=-1) / 5.0,
            torch.sigmoid(-(current_distances - 2.0))  # 危险接近度
        ], dim=-1)
        
        # 预测冲突概率
        conflict_probs = self.conflict_predictor(conflict_features)
        
        # 排除自己与自己的冲突
        mask = torch.eye(num_agvs, device=agv_positions.device).unsqueeze(0) == 0
        conflict_probs = conflict_probs * mask.unsqueeze(-1)
        
        # 计算总体冲突分数
        conflict_score = torch.mean(conflict_probs)
        
        return conflict_score


class MultiLevelTemperatureController(nn.Module):
    """多层次温度控制器"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_levels: int = 3,
                 base_temperatures: List[float] = [0.5, 1.0, 2.0],
                 adaptation_method: TemperatureAdaptationMethod = TemperatureAdaptationMethod.HYBRID):
        """
        初始化多层次温度控制器
        
        Args:
            embed_dim: 嵌入维度
            num_levels: 层次数量
            base_temperatures: 基础温度列表
            adaptation_method: 自适应方法
        """
        super(MultiLevelTemperatureController, self).__init__()
        
        self.num_levels = num_levels
        self.base_temperatures = base_temperatures
        
        # 为每个层次创建温度控制器
        self.level_controllers = nn.ModuleList([
            AdaptiveTemperatureController(
                embed_dim=embed_dim,
                base_temperature=base_temp,
                adaptation_method=adaptation_method
            ) for base_temp in base_temperatures
        ])
        
        # 层次权重预测器
        self.level_weight_predictor = nn.Sequential(
            nn.Linear(embed_dim + 6, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, num_levels),
            nn.Softmax(dim=-1)
        )
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: Optional[torch.Tensor] = None,
                attention_scores: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        """
        level_results = []
        level_temperatures = []
        
        # 计算每个层次的温度
        for controller in self.level_controllers:
            result = controller(agv_embeddings, agv_positions, agv_velocities, attention_scores)
            level_results.append(result)
            level_temperatures.append(result['temperature'])
        
        # 计算层次权重
        global_features = torch.mean(agv_embeddings, dim=1)
        complexity_features = self.level_controllers[0].complexity_evaluator.evaluate_complexity(
            agv_embeddings, agv_positions, agv_velocities
        )
        
        weight_input = torch.cat([global_features, complexity_features], dim=-1)
        level_weights = self.level_weight_predictor(weight_input)
        
        # 加权融合温度
        final_temperature = sum(
            weight * temp for weight, temp in zip(level_weights[0], level_temperatures)
        )
        
        return {
            'temperature': final_temperature,
            'level_temperatures': level_temperatures,
            'level_weights': level_weights[0],
            'level_results': level_results
        }
