"""
双层注意力融合机制

实现任务分配注意力和协作感知注意力的门控融合机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, Tuple, Optional, List, Union
from enum import Enum

from .task_allocation_attention import TaskAllocationAttention
from .collaboration_attention import CollaborationAwareAttention


class FusionMethod(Enum):
    """融合方法枚举"""
    GATED = "gated"                    # 门控融合
    WEIGHTED = "weighted"              # 加权融合
    ATTENTION = "attention"            # 注意力融合
    HIERARCHICAL = "hierarchical"      # 层次化融合
    ADAPTIVE = "adaptive"              # 自适应融合


class DualLayerAttentionFusion(nn.Module):
    """双层注意力融合机制"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_agvs: int = 4,
                 num_tasks: int = 10,
                 fusion_method: FusionMethod = FusionMethod.GATED,
                 use_residual: bool = True,
                 use_layer_norm: bool = True,
                 use_adaptive_weights: bool = True,
                 temperature: float = 1.0,
                 dropout: float = 0.1):
        """
        初始化双层注意力融合机制
        
        Args:
            embed_dim: 嵌入维度
            num_agvs: AGV数量
            num_tasks: 任务数量
            fusion_method: 融合方法
            use_residual: 是否使用残差连接
            use_layer_norm: 是否使用层归一化
            use_adaptive_weights: 是否使用自适应权重
            temperature: 温度参数
            dropout: Dropout概率
        """
        super(DualLayerAttentionFusion, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_agvs = num_agvs
        self.num_tasks = num_tasks
        self.fusion_method = fusion_method
        self.use_residual = use_residual
        self.use_layer_norm = use_layer_norm
        self.use_adaptive_weights = use_adaptive_weights
        self.temperature = temperature
        
        # 门控融合网络
        if fusion_method == FusionMethod.GATED:
            self.gate_network = GatedFusionNetwork(embed_dim, dropout)
        elif fusion_method == FusionMethod.WEIGHTED:
            self.weight_network = WeightedFusionNetwork(embed_dim, dropout)
        elif fusion_method == FusionMethod.ATTENTION:
            self.attention_fusion = AttentionFusionNetwork(embed_dim, dropout)
        elif fusion_method == FusionMethod.HIERARCHICAL:
            self.hierarchical_fusion = HierarchicalFusionNetwork(embed_dim, dropout)
        elif fusion_method == FusionMethod.ADAPTIVE:
            self.adaptive_fusion = AdaptiveFusionNetwork(embed_dim, dropout)
        
        # 环境复杂度评估器
        self.complexity_evaluator = EnvironmentComplexityEvaluator(embed_dim)
        
        # 自适应权重预测器
        if use_adaptive_weights:
            self.weight_predictor = AdaptiveWeightPredictor(embed_dim, dropout)
        
        # 层归一化
        if use_layer_norm:
            self.layer_norm1 = nn.LayerNorm(embed_dim)
            self.layer_norm2 = nn.LayerNorm(embed_dim)
            self.layer_norm_final = nn.LayerNorm(embed_dim)
        
        # 输出投影
        self.output_projection = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 融合质量评估器
        self.quality_assessor = FusionQualityAssessor(embed_dim)
        
        # 融合历史
        self.fusion_history = {}
    
    def forward(self, 
                task_attention_output: torch.Tensor,
                collaboration_attention_output: torch.Tensor,
                agv_states: torch.Tensor,
                task_states: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: torch.Tensor,
                global_context: Optional[torch.Tensor] = None,
                batch_id: str = "default") -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            task_attention_output: 任务分配注意力输出 [batch_size, num_agvs, embed_dim]
            collaboration_attention_output: 协作感知注意力输出 [batch_size, num_agvs, embed_dim]
            agv_states: AGV状态 [batch_size, num_agvs, state_dim]
            task_states: 任务状态 [batch_size, num_tasks, task_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            global_context: 全局上下文 [batch_size, embed_dim]
            batch_id: 批次ID
            
        Returns:
            fusion_result: 融合结果字典
        """
        batch_size, num_agvs, embed_dim = task_attention_output.shape
        
        # 层归一化
        if self.use_layer_norm:
            task_output_norm = self.layer_norm1(task_attention_output)
            collab_output_norm = self.layer_norm2(collaboration_attention_output)
        else:
            task_output_norm = task_attention_output
            collab_output_norm = collaboration_attention_output
        
        # 评估环境复杂度
        complexity_features = self.complexity_evaluator.evaluate_complexity(
            agv_states, task_states, agv_positions, agv_velocities
        )
        
        # 计算自适应权重
        if self.use_adaptive_weights:
            weight_result = self.weight_predictor(
                task_output_norm, collab_output_norm, complexity_features, global_context
            )
            adaptive_weights = weight_result['weights']
            weight_info = weight_result
        else:
            # 使用固定权重
            adaptive_weights = torch.tensor([0.5, 0.5], device=task_attention_output.device)
            adaptive_weights = adaptive_weights.unsqueeze(0).unsqueeze(0).expand(batch_size, num_agvs, -1)
            weight_info = {'weights': adaptive_weights, 'method': 'fixed'}
        
        # 根据融合方法进行融合
        if self.fusion_method == FusionMethod.GATED:
            fusion_result = self.gate_network(
                task_output_norm, collab_output_norm, adaptive_weights, complexity_features
            )
        elif self.fusion_method == FusionMethod.WEIGHTED:
            fusion_result = self.weight_network(
                task_output_norm, collab_output_norm, adaptive_weights
            )
        elif self.fusion_method == FusionMethod.ATTENTION:
            fusion_result = self.attention_fusion(
                task_output_norm, collab_output_norm, complexity_features
            )
        elif self.fusion_method == FusionMethod.HIERARCHICAL:
            fusion_result = self.hierarchical_fusion(
                task_output_norm, collab_output_norm, adaptive_weights, complexity_features
            )
        elif self.fusion_method == FusionMethod.ADAPTIVE:
            fusion_result = self.adaptive_fusion(
                task_output_norm, collab_output_norm, adaptive_weights, complexity_features, global_context
            )
        else:
            # 默认简单加权融合
            fused_output = (adaptive_weights[:, :, 0:1] * task_output_norm + 
                          adaptive_weights[:, :, 1:2] * collab_output_norm)
            fusion_result = {'fused_output': fused_output, 'method': 'simple_weighted'}
        
        fused_output = fusion_result['fused_output']
        
        # 残差连接
        if self.use_residual:
            # 使用任务注意力输出作为残差
            fused_output = fused_output + task_output_norm
        
        # 最终层归一化
        if self.use_layer_norm:
            fused_output = self.layer_norm_final(fused_output)
        
        # 输出投影
        final_output = self.output_projection(fused_output)
        
        # 质量评估
        quality_metrics = self.quality_assessor.assess_quality(
            task_output_norm, collab_output_norm, final_output, adaptive_weights
        )
        
        # 更新融合历史
        self._update_fusion_history(adaptive_weights, quality_metrics, batch_id)
        
        # 组装结果
        result = {
            'fused_output': final_output,
            'raw_fused_output': fused_output,
            'task_attention_output': task_output_norm,
            'collaboration_attention_output': collab_output_norm,
            'adaptive_weights': adaptive_weights,
            'weight_info': weight_info,
            'fusion_info': fusion_result,
            'complexity_features': complexity_features,
            'quality_metrics': quality_metrics
        }
        
        return result
    
    def _update_fusion_history(self, 
                             weights: torch.Tensor,
                             quality_metrics: Dict[str, torch.Tensor],
                             batch_id: str):
        """更新融合历史"""
        if batch_id not in self.fusion_history:
            self.fusion_history[batch_id] = {
                'weights_history': [],
                'quality_history': []
            }
        
        # 添加当前权重和质量
        self.fusion_history[batch_id]['weights_history'].append(weights.detach().cpu())
        self.fusion_history[batch_id]['quality_history'].append({
            k: v.detach().cpu() if isinstance(v, torch.Tensor) else v 
            for k, v in quality_metrics.items()
        })
        
        # 限制历史长度
        max_history = 100
        if len(self.fusion_history[batch_id]['weights_history']) > max_history:
            self.fusion_history[batch_id]['weights_history'].pop(0)
            self.fusion_history[batch_id]['quality_history'].pop(0)
    
    def get_fusion_statistics(self, batch_id: str = "default") -> Dict[str, float]:
        """获取融合统计信息"""
        if batch_id not in self.fusion_history:
            return {}
        
        history = self.fusion_history[batch_id]
        
        if not history['weights_history']:
            return {}
        
        # 权重统计
        weights_tensor = torch.stack(history['weights_history'])  # [history_len, batch_size, num_agvs, 2]
        
        stats = {
            'avg_task_weight': torch.mean(weights_tensor[:, :, :, 0]).item(),
            'avg_collab_weight': torch.mean(weights_tensor[:, :, :, 1]).item(),
            'weight_variance': torch.var(weights_tensor, dim=0).mean().item(),
            'weight_stability': 1.0 - torch.mean(torch.std(weights_tensor, dim=0)).item()
        }
        
        # 质量统计
        if history['quality_history']:
            quality_keys = history['quality_history'][0].keys()
            for key in quality_keys:
                if isinstance(history['quality_history'][0][key], torch.Tensor):
                    values = [q[key] for q in history['quality_history']]
                    if values:
                        values_tensor = torch.stack(values)
                        stats[f'avg_{key}'] = torch.mean(values_tensor).item()
                        stats[f'std_{key}'] = torch.std(values_tensor).item()
        
        return stats


class GatedFusionNetwork(nn.Module):
    """门控融合网络"""
    
    def __init__(self, embed_dim: int, dropout: float):
        super(GatedFusionNetwork, self).__init__()
        
        self.embed_dim = embed_dim
        
        # 门控网络
        self.gate_network = nn.Sequential(
            nn.Linear(embed_dim * 2 + 6, embed_dim),  # 两个输入 + 复杂度特征
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 2),
            nn.Sigmoid()
        )
        
        # 特征变换网络
        self.task_transform = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim)
        )
        
        self.collab_transform = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, 
                task_output: torch.Tensor,
                collab_output: torch.Tensor,
                adaptive_weights: torch.Tensor,
                complexity_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """门控融合前向传播"""
        batch_size, num_agvs, embed_dim = task_output.shape
        
        # 计算门控值
        gate_input = torch.cat([
            task_output, 
            collab_output, 
            complexity_features.unsqueeze(1).expand(-1, num_agvs, -1)
        ], dim=-1)
        
        gates = self.gate_network(gate_input)  # [batch_size, num_agvs, 2]
        
        # 特征变换
        task_transformed = self.task_transform(task_output)
        collab_transformed = self.collab_transform(collab_output)
        
        # 门控融合
        gated_task = gates[:, :, 0:1] * task_transformed
        gated_collab = gates[:, :, 1:2] * collab_transformed
        
        # 组合权重（门控权重 + 自适应权重）
        combined_weights = 0.7 * gates + 0.3 * adaptive_weights
        combined_weights = F.softmax(combined_weights, dim=-1)
        
        # 最终融合
        fused_output = (combined_weights[:, :, 0:1] * gated_task + 
                       combined_weights[:, :, 1:2] * gated_collab)
        
        fused_output = self.fusion_network(fused_output)
        
        return {
            'fused_output': fused_output,
            'gates': gates,
            'combined_weights': combined_weights,
            'gated_task': gated_task,
            'gated_collab': gated_collab,
            'method': 'gated_fusion'
        }


class WeightedFusionNetwork(nn.Module):
    """加权融合网络"""
    
    def __init__(self, embed_dim: int, dropout: float):
        super(WeightedFusionNetwork, self).__init__()
        
        self.embed_dim = embed_dim
        
        # 权重调整网络
        self.weight_adjuster = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, 2),
            nn.Tanh()
        )
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, 
                task_output: torch.Tensor,
                collab_output: torch.Tensor,
                adaptive_weights: torch.Tensor) -> Dict[str, torch.Tensor]:
        """加权融合前向传播"""
        # 计算权重调整
        weight_input = torch.cat([task_output, collab_output], dim=-1)
        weight_adjustment = self.weight_adjuster(weight_input)  # [batch_size, num_agvs, 2]
        
        # 调整权重
        adjusted_weights = adaptive_weights + weight_adjustment * 0.1
        adjusted_weights = F.softmax(adjusted_weights, dim=-1)
        
        # 加权融合
        fused_output = (adjusted_weights[:, :, 0:1] * task_output + 
                       adjusted_weights[:, :, 1:2] * collab_output)
        
        fused_output = self.fusion_network(fused_output)
        
        return {
            'fused_output': fused_output,
            'adjusted_weights': adjusted_weights,
            'weight_adjustment': weight_adjustment,
            'method': 'weighted_fusion'
        }


class AttentionFusionNetwork(nn.Module):
    """注意力融合网络"""
    
    def __init__(self, embed_dim: int, dropout: float):
        super(AttentionFusionNetwork, self).__init__()
        
        self.embed_dim = embed_dim
        
        # 交叉注意力
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        # 自注意力
        self.self_attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, 
                task_output: torch.Tensor,
                collab_output: torch.Tensor,
                complexity_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """注意力融合前向传播"""
        # 交叉注意力
        task_attended, task_attention = self.cross_attention(
            task_output, collab_output, collab_output
        )
        collab_attended, collab_attention = self.cross_attention(
            collab_output, task_output, task_output
        )
        
        # 拼接
        concatenated = torch.cat([task_attended, collab_attended], dim=-1)
        
        # 融合
        fused_output = self.fusion_network(concatenated)
        
        # 自注意力
        final_output, self_attention = self.self_attention(
            fused_output, fused_output, fused_output
        )
        
        return {
            'fused_output': final_output,
            'task_attended': task_attended,
            'collab_attended': collab_attended,
            'task_attention': task_attention,
            'collab_attention': collab_attention,
            'self_attention': self_attention,
            'method': 'attention_fusion'
        }


class HierarchicalFusionNetwork(nn.Module):
    """层次化融合网络"""

    def __init__(self, embed_dim: int, dropout: float):
        super(HierarchicalFusionNetwork, self).__init__()

        self.embed_dim = embed_dim

        # 第一层融合
        self.first_fusion = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )

        # 第二层融合
        self.second_fusion = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )

        # 层次权重网络
        self.hierarchy_weights = nn.Sequential(
            nn.Linear(embed_dim + 6, embed_dim // 2),  # 融合特征 + 复杂度
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 2),
            nn.Softmax(dim=-1)
        )

    def forward(self,
                task_output: torch.Tensor,
                collab_output: torch.Tensor,
                adaptive_weights: torch.Tensor,
                complexity_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """层次化融合前向传播"""
        batch_size, num_agvs, embed_dim = task_output.shape

        # 第一层：直接拼接融合
        first_input = torch.cat([task_output, collab_output], dim=-1)
        first_fused = self.first_fusion(first_input)

        # 第二层：加权融合
        weighted_sum = (adaptive_weights[:, :, 0:1] * task_output +
                       adaptive_weights[:, :, 1:2] * collab_output)
        second_input = torch.cat([first_fused, weighted_sum], dim=-1)
        second_fused = self.second_fusion(second_input)

        # 层次权重
        hierarchy_input = torch.cat([
            second_fused,
            complexity_features.unsqueeze(1).expand(-1, num_agvs, -1)
        ], dim=-1)
        hierarchy_weights = self.hierarchy_weights(hierarchy_input)

        # 最终融合
        final_output = (hierarchy_weights[:, :, 0:1] * first_fused +
                       hierarchy_weights[:, :, 1:2] * second_fused)

        return {
            'fused_output': final_output,
            'first_fused': first_fused,
            'second_fused': second_fused,
            'hierarchy_weights': hierarchy_weights,
            'method': 'hierarchical_fusion'
        }


class AdaptiveFusionNetwork(nn.Module):
    """自适应融合网络"""

    def __init__(self, embed_dim: int, dropout: float):
        super(AdaptiveFusionNetwork, self).__init__()

        self.embed_dim = embed_dim

        # 自适应权重网络
        self.adaptive_network = nn.Sequential(
            nn.Linear(embed_dim * 2 + 6, embed_dim),  # 两个输入 + 复杂度
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 3),  # 三种融合策略的权重
            nn.Softmax(dim=-1)
        )

        # 三种融合策略
        self.strategy1 = nn.Sequential(  # 简单加权
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim)
        )

        self.strategy2 = nn.Sequential(  # 门控融合
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim)
        )

        self.strategy3 = nn.Sequential(  # 残差融合
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim)
        )

    def forward(self,
                task_output: torch.Tensor,
                collab_output: torch.Tensor,
                adaptive_weights: torch.Tensor,
                complexity_features: torch.Tensor,
                global_context: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """自适应融合前向传播"""
        batch_size, num_agvs, embed_dim = task_output.shape

        # 计算策略权重
        strategy_input = torch.cat([
            task_output,
            collab_output,
            complexity_features.unsqueeze(1).expand(-1, num_agvs, -1)
        ], dim=-1)

        strategy_weights = self.adaptive_network(strategy_input)  # [batch_size, num_agvs, 3]

        # 策略1：简单加权
        weighted_sum = (adaptive_weights[:, :, 0:1] * task_output +
                       adaptive_weights[:, :, 1:2] * collab_output)
        output1 = self.strategy1(weighted_sum)

        # 策略2：门控融合
        concatenated = torch.cat([task_output, collab_output], dim=-1)
        output2 = self.strategy2(concatenated)

        # 策略3：残差融合
        residual_sum = task_output + collab_output
        output3 = self.strategy3(residual_sum)

        # 自适应组合
        final_output = (strategy_weights[:, :, 0:1] * output1 +
                       strategy_weights[:, :, 1:2] * output2 +
                       strategy_weights[:, :, 2:3] * output3)

        return {
            'fused_output': final_output,
            'strategy_weights': strategy_weights,
            'strategy_outputs': [output1, output2, output3],
            'method': 'adaptive_fusion'
        }


class EnvironmentComplexityEvaluator(nn.Module):
    """环境复杂度评估器"""

    def __init__(self, embed_dim: int):
        super(EnvironmentComplexityEvaluator, self).__init__()

        self.embed_dim = embed_dim

        # 复杂度特征提取器
        self.complexity_extractor = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 6)  # 6个复杂度特征
        )

    def evaluate_complexity(self,
                          agv_states: torch.Tensor,
                          task_states: torch.Tensor,
                          agv_positions: torch.Tensor,
                          agv_velocities: torch.Tensor) -> torch.Tensor:
        """评估环境复杂度"""
        batch_size, num_agvs, _ = agv_positions.shape
        num_tasks = task_states.shape[1]

        # 1. 空间复杂度
        pos_i = agv_positions.unsqueeze(2)
        pos_j = agv_positions.unsqueeze(1)
        distances = torch.norm(pos_j - pos_i, dim=-1)
        avg_distance = torch.sum(distances) / (num_agvs * (num_agvs - 1))
        spatial_complexity = 1.0 / (avg_distance + 1e-8)

        # 2. 任务复杂度
        task_complexity = torch.tensor(num_tasks / 20.0, device=agv_positions.device)  # 归一化

        # 3. 速度方差
        speed_variance = torch.var(torch.norm(agv_velocities, dim=-1), dim=-1)

        # 4. 负载方差
        loads = agv_states[:, :, 4] if agv_states.shape[-1] > 4 else torch.zeros(batch_size, num_agvs)
        load_variance = torch.var(loads, dim=-1)

        # 5. 协作需求
        close_pairs = torch.sum((distances < 5.0) & (distances > 0), dim=(1, 2)).float()
        collaboration_need = close_pairs / (num_agvs * (num_agvs - 1))

        # 6. 动态性
        vel_i = agv_velocities.unsqueeze(2)
        vel_j = agv_velocities.unsqueeze(1)
        relative_velocities = vel_j - vel_i
        dynamics = torch.var(torch.norm(relative_velocities, dim=-1), dim=(1, 2))

        complexity_features = torch.stack([
            spatial_complexity.unsqueeze(0).expand(batch_size),
            task_complexity.unsqueeze(0).expand(batch_size),
            speed_variance,
            load_variance,
            collaboration_need,
            dynamics
        ], dim=-1)

        return complexity_features


class AdaptiveWeightPredictor(nn.Module):
    """自适应权重预测器"""

    def __init__(self, embed_dim: int, dropout: float):
        super(AdaptiveWeightPredictor, self).__init__()

        self.embed_dim = embed_dim

        # 权重预测网络
        self.weight_predictor = nn.Sequential(
            nn.Linear(embed_dim * 2 + 6, embed_dim),  # 两个输入 + 复杂度
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 2),
            nn.Softmax(dim=-1)
        )

        # 上下文调整器
        self.context_adjuster = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 2),
            nn.Tanh()
        )

    def forward(self,
                task_output: torch.Tensor,
                collab_output: torch.Tensor,
                complexity_features: torch.Tensor,
                global_context: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """预测自适应权重"""
        batch_size, num_agvs, embed_dim = task_output.shape

        # 基础权重预测
        weight_input = torch.cat([
            task_output,
            collab_output,
            complexity_features.unsqueeze(1).expand(-1, num_agvs, -1)
        ], dim=-1)

        base_weights = self.weight_predictor(weight_input)

        # 上下文调整
        if global_context is not None:
            context_input = global_context.unsqueeze(1).expand(-1, num_agvs, -1)
            context_adjustment = self.context_adjuster(context_input)

            # 调整权重
            adjusted_weights = base_weights + context_adjustment * 0.1
            adjusted_weights = F.softmax(adjusted_weights, dim=-1)
        else:
            adjusted_weights = base_weights
            context_adjustment = torch.zeros_like(base_weights)

        return {
            'weights': adjusted_weights,
            'base_weights': base_weights,
            'context_adjustment': context_adjustment,
            'method': 'adaptive'
        }


class FusionQualityAssessor(nn.Module):
    """融合质量评估器"""

    def __init__(self, embed_dim: int):
        super(FusionQualityAssessor, self).__init__()

        self.embed_dim = embed_dim

        # 质量评估网络
        self.quality_assessor = nn.Sequential(
            nn.Linear(embed_dim * 3, embed_dim),  # 两个输入 + 融合输出
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 4),  # 4个质量指标
            nn.Sigmoid()
        )

    def assess_quality(self,
                      task_output: torch.Tensor,
                      collab_output: torch.Tensor,
                      fused_output: torch.Tensor,
                      weights: torch.Tensor) -> Dict[str, torch.Tensor]:
        """评估融合质量"""
        batch_size, num_agvs, embed_dim = task_output.shape

        # 计算相似性
        task_similarity = F.cosine_similarity(
            task_output.view(batch_size, -1),
            fused_output.view(batch_size, -1),
            dim=-1
        )

        collab_similarity = F.cosine_similarity(
            collab_output.view(batch_size, -1),
            fused_output.view(batch_size, -1),
            dim=-1
        )

        # 计算权重熵
        weight_entropy = -torch.sum(weights * torch.log(weights + 1e-8), dim=-1)
        weight_entropy = torch.mean(weight_entropy, dim=-1)  # [batch_size]

        # 组合特征进行质量评估
        quality_input = torch.cat([
            torch.mean(task_output, dim=1),
            torch.mean(collab_output, dim=1),
            torch.mean(fused_output, dim=1)
        ], dim=-1)

        quality_scores = self.quality_assessor(quality_input)  # [batch_size, 4]

        return {
            'overall_quality': torch.mean(quality_scores, dim=-1),
            'task_similarity': task_similarity,
            'collab_similarity': collab_similarity,
            'weight_entropy': weight_entropy,
            'coherence_score': quality_scores[:, 0],
            'diversity_score': quality_scores[:, 1],
            'stability_score': quality_scores[:, 2],
            'effectiveness_score': quality_scores[:, 3]
        }
