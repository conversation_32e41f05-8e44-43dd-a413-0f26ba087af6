"""
神经网络模块

包含：
- policy_net.py: 策略网络
- value_net.py: 价值网络
- attention_enhanced_policy.py: 注意力增强策略网络
- attention_enhanced_value.py: 注意力增强价值网络
- normalization_residual.py: 层归一化和残差连接
"""

from .policy_net import PolicyNetwork
from .value_net import ValueNetwork
from .attention_enhanced_policy import (
    AttentionEnhancedPolicyNetwork, HierarchicalActionGenerator,
    StandardActionGenerator, AttentionPolicyWrapper
)
from .attention_enhanced_value import (
    AttentionEnhancedValueNetwork, GlobalValueAttention, ValueDecomposer,
    AttentionValueWrapper, HybridValueNetwork
)
from .normalization_residual import (
    AttentionLayerNorm, AdaptiveLayerNorm, ResidualConnection,
    PreNormResidualConnection, AttentionResidualBlock, FusionResidualBlock,
    StabilizedAttentionFusion, GradientScaler
)

__all__ = [
    # 基础网络
    'PolicyNetwork', 'ValueNetwork',

    # 注意力增强策略网络
    'AttentionEnhancedPolicyNetwork', 'HierarchicalActionGenerator',
    'StandardActionGenerator', 'AttentionPolicyWrapper',

    # 注意力增强价值网络
    'AttentionEnhancedValueNetwork', 'GlobalValueAttention', 'ValueDecomposer',
    'AttentionValueWrapper', 'HybridValueNetwork',

    # 归一化和残差连接
    'AttentionLayerNorm', 'AdaptiveLayerNorm', 'ResidualConnection',
    'PreNormResidualConnection', 'AttentionResidualBlock', 'FusionResidualBlock',
    'StabilizedAttentionFusion', 'GradientScaler'
]
