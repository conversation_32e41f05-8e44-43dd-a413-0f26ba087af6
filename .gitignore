# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/*.log

# Temporary files
*.tmp
*.temp
*_temp.py
*_test.py
test_*.py
temp_*.py

# Experiment outputs (keep only important ones)
experiments/*/checkpoint_episode_*.json
experiments/*/training_curves_episode_*.png
experiments/*/best_model_episode_*.pth

# Keep only final results
!experiments/*/final_*
!experiments/*/training_results.json

# Backup files
*.bak
*.backup
*_backup.*

# Cache
.cache/
.pytest_cache/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Documentation builds
docs/_build/
