"""
动作掩码机制

实现动作掩码机制，防止无效动作的选择，提高训练效率和安全性
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from enum import Enum

from envs.agv_model import AGVModel, AGVAction, AGVStatus
from envs.grid_world import GridWorld
from envs.task_manager import TaskManager
from envs.collision_detector import CollisionDetector


class MaskType(Enum):
    """掩码类型"""
    MOVEMENT = "movement"      # 移动掩码
    PICKUP = "pickup"          # 拾取掩码
    DROPOFF = "dropoff"        # 放置掩码
    WAIT = "wait"              # 等待掩码
    COMBINED = "combined"      # 组合掩码


class ActionMaskGenerator:
    """动作掩码生成器"""
    
    def __init__(self, 
                 grid_world: GridWorld,
                 collision_detector: CollisionDetector,
                 enable_safety_mask: bool = True,
                 enable_task_mask: bool = True,
                 enable_physics_mask: bool = True):
        """
        初始化动作掩码生成器
        
        Args:
            grid_world: 网格世界环境
            collision_detector: 碰撞检测器
            enable_safety_mask: 是否启用安全掩码
            enable_task_mask: 是否启用任务掩码
            enable_physics_mask: 是否启用物理掩码
        """
        self.grid_world = grid_world
        self.collision_detector = collision_detector
        self.enable_safety_mask = enable_safety_mask
        self.enable_task_mask = enable_task_mask
        self.enable_physics_mask = enable_physics_mask
        
        # 动作映射
        self.action_mapping = {
            AGVAction.MOVE_UP: 0,
            AGVAction.MOVE_DOWN: 1,
            AGVAction.MOVE_LEFT: 2,
            AGVAction.MOVE_RIGHT: 3,
            AGVAction.WAIT: 4,
            AGVAction.PICKUP: 5,
            AGVAction.DROPOFF: 6
        }
        
        self.num_actions = len(self.action_mapping)
    
    def generate_action_mask(self, 
                           agv: AGVModel,
                           other_agvs: List[AGVModel],
                           task_manager: TaskManager,
                           mask_type: MaskType = MaskType.COMBINED) -> np.ndarray:
        """
        生成动作掩码
        
        Args:
            agv: 当前AGV
            other_agvs: 其他AGV列表
            task_manager: 任务管理器
            mask_type: 掩码类型
            
        Returns:
            action_mask: 动作掩码 [num_actions] (1表示有效，0表示无效)
        """
        if mask_type == MaskType.COMBINED:
            return self._generate_combined_mask(agv, other_agvs, task_manager)
        elif mask_type == MaskType.MOVEMENT:
            return self._generate_movement_mask(agv, other_agvs)
        elif mask_type == MaskType.PICKUP:
            return self._generate_pickup_mask(agv, task_manager)
        elif mask_type == MaskType.DROPOFF:
            return self._generate_dropoff_mask(agv, task_manager)
        elif mask_type == MaskType.WAIT:
            return self._generate_wait_mask(agv)
        else:
            return np.ones(self.num_actions, dtype=np.float32)
    
    def _generate_combined_mask(self, 
                              agv: AGVModel,
                              other_agvs: List[AGVModel],
                              task_manager: TaskManager) -> np.ndarray:
        """生成组合掩码"""
        mask = np.ones(self.num_actions, dtype=np.float32)
        
        # 移动掩码
        if self.enable_safety_mask or self.enable_physics_mask:
            movement_mask = self._generate_movement_mask(agv, other_agvs)
            mask[:4] = movement_mask[:4]  # 前4个是移动动作
        
        # 拾取掩码
        if self.enable_task_mask:
            pickup_mask = self._generate_pickup_mask(agv, task_manager)
            mask[5] = pickup_mask[5]  # PICKUP动作
        
        # 放置掩码
        if self.enable_task_mask:
            dropoff_mask = self._generate_dropoff_mask(agv, task_manager)
            mask[6] = dropoff_mask[6]  # DROPOFF动作
        
        # 等待动作通常总是有效的
        mask[4] = 1.0  # WAIT动作
        
        # 确保至少有一个动作是有效的
        if np.sum(mask) == 0:
            mask[4] = 1.0  # 强制等待动作有效
        
        return mask
    
    def _generate_movement_mask(self, 
                              agv: AGVModel,
                              other_agvs: List[AGVModel]) -> np.ndarray:
        """生成移动掩码"""
        mask = np.ones(self.num_actions, dtype=np.float32)
        
        # 检查每个移动动作的有效性
        movement_actions = [
            AGVAction.MOVE_UP, AGVAction.MOVE_DOWN,
            AGVAction.MOVE_LEFT, AGVAction.MOVE_RIGHT
        ]
        
        for action in movement_actions:
            action_idx = self.action_mapping[action]
            
            # 物理约束检查
            if self.enable_physics_mask:
                if not self._check_physics_constraints(agv, action):
                    mask[action_idx] = 0.0
                    continue
            
            # 安全约束检查
            if self.enable_safety_mask:
                is_valid, _ = self.collision_detector.check_action_validity(
                    agv, action, other_agvs
                )
                if not is_valid:
                    mask[action_idx] = 0.0
        
        return mask
    
    def _generate_pickup_mask(self, 
                            agv: AGVModel,
                            task_manager: TaskManager) -> np.ndarray:
        """生成拾取掩码"""
        mask = np.ones(self.num_actions, dtype=np.float32)
        
        # 默认拾取动作无效
        mask[5] = 0.0
        
        if not self.enable_task_mask:
            return mask
        
        # 检查拾取条件
        can_pickup = False
        
        # 1. AGV必须空闲或正在移动
        if agv.status not in [AGVStatus.IDLE, AGVStatus.MOVING]:
            return mask
        
        # 2. 检查当前位置是否有可拾取的任务
        available_tasks = task_manager.get_unassigned_tasks()
        
        for task in available_tasks:
            # 任务的拾取位置必须与AGV位置匹配
            if task.pickup_position == agv.position:
                # AGV必须有足够的载重能力
                if agv.can_take_task(task.weight):
                    can_pickup = True
                    break
        
        if can_pickup:
            mask[5] = 1.0
        
        return mask
    
    def _generate_dropoff_mask(self, 
                             agv: AGVModel,
                             task_manager: TaskManager) -> np.ndarray:
        """生成放置掩码"""
        mask = np.ones(self.num_actions, dtype=np.float32)
        
        # 默认放置动作无效
        mask[6] = 0.0
        
        if not self.enable_task_mask:
            return mask
        
        # 检查放置条件
        can_dropoff = False
        
        # 1. AGV必须携带任务
        if not agv.carried_tasks:
            return mask
        
        # 2. 检查当前位置是否是某个携带任务的放置点
        for task_id in agv.carried_tasks:
            task = task_manager.get_task(task_id)
            if task and task.dropoff_position == agv.position:
                can_dropoff = True
                break
        
        if can_dropoff:
            mask[6] = 1.0
        
        return mask
    
    def _generate_wait_mask(self, agv: AGVModel) -> np.ndarray:
        """生成等待掩码"""
        mask = np.ones(self.num_actions, dtype=np.float32)
        
        # 等待动作通常总是有效的
        mask[4] = 1.0
        
        # 其他动作设为无效
        mask[:4] = 0.0  # 移动动作
        mask[5:] = 0.0  # 拾取和放置动作
        
        return mask
    
    def _check_physics_constraints(self, agv: AGVModel, action: AGVAction) -> bool:
        """检查物理约束"""
        # 计算新位置
        new_x, new_y = agv.x, agv.y
        
        if action == AGVAction.MOVE_UP:
            new_y -= 1
        elif action == AGVAction.MOVE_DOWN:
            new_y += 1
        elif action == AGVAction.MOVE_LEFT:
            new_x -= 1
        elif action == AGVAction.MOVE_RIGHT:
            new_x += 1
        else:
            return True  # 非移动动作不需要物理约束检查
        
        # 检查边界
        if not (0 <= new_x < self.grid_world.width and 0 <= new_y < self.grid_world.height):
            return False
        
        # 检查是否可通行
        if not self.grid_world.is_valid_position(new_x, new_y):
            return False
        
        return True
    
    def generate_batch_masks(self, 
                           agvs: List[AGVModel],
                           task_manager: TaskManager) -> np.ndarray:
        """
        批量生成动作掩码
        
        Args:
            agvs: AGV列表
            task_manager: 任务管理器
            
        Returns:
            batch_masks: 批量掩码 [num_agents, num_actions]
        """
        batch_masks = []
        
        for i, agv in enumerate(agvs):
            other_agvs = [a for j, a in enumerate(agvs) if j != i]
            mask = self.generate_action_mask(agv, other_agvs, task_manager)
            batch_masks.append(mask)
        
        return np.array(batch_masks, dtype=np.float32)


class AdaptiveActionMasking:
    """自适应动作掩码"""
    
    def __init__(self, 
                 base_generator: ActionMaskGenerator,
                 learning_rate: float = 0.01,
                 exploration_bonus: float = 0.1):
        """
        初始化自适应动作掩码
        
        Args:
            base_generator: 基础掩码生成器
            learning_rate: 学习率
            exploration_bonus: 探索奖励
        """
        self.base_generator = base_generator
        self.learning_rate = learning_rate
        self.exploration_bonus = exploration_bonus
        
        # 动作成功率统计
        self.action_success_rates = {}
        self.action_counts = {}
    
    def generate_adaptive_mask(self, 
                             agv: AGVModel,
                             other_agvs: List[AGVModel],
                             task_manager: TaskManager) -> np.ndarray:
        """
        生成自适应掩码
        
        Args:
            agv: 当前AGV
            other_agvs: 其他AGV列表
            task_manager: 任务管理器
            
        Returns:
            adaptive_mask: 自适应掩码
        """
        # 获取基础掩码
        base_mask = self.base_generator.generate_action_mask(
            agv, other_agvs, task_manager
        )
        
        # 获取AGV状态键
        state_key = self._get_state_key(agv)
        
        # 初始化统计信息
        if state_key not in self.action_success_rates:
            self.action_success_rates[state_key] = np.ones(self.base_generator.num_actions) * 0.5
            self.action_counts[state_key] = np.zeros(self.base_generator.num_actions)
        
        # 计算自适应权重
        success_rates = self.action_success_rates[state_key]
        counts = self.action_counts[state_key]
        
        # 探索奖励（对尝试次数少的动作给予奖励）
        exploration_weights = self.exploration_bonus / (counts + 1)
        
        # 组合权重
        adaptive_weights = success_rates + exploration_weights
        
        # 应用到基础掩码
        adaptive_mask = base_mask * adaptive_weights
        
        # 归一化（保持掩码的0-1特性）
        if np.sum(adaptive_mask) > 0:
            adaptive_mask = adaptive_mask / np.max(adaptive_mask)
        
        # 确保基础掩码为0的动作仍然为0
        adaptive_mask = adaptive_mask * base_mask
        
        return adaptive_mask
    
    def update_success_rate(self, 
                          agv: AGVModel,
                          action: int,
                          success: bool):
        """
        更新动作成功率
        
        Args:
            agv: AGV
            action: 动作索引
            success: 是否成功
        """
        state_key = self._get_state_key(agv)
        
        if state_key not in self.action_success_rates:
            return
        
        # 更新计数
        self.action_counts[state_key][action] += 1
        
        # 更新成功率（指数移动平均）
        current_rate = self.action_success_rates[state_key][action]
        new_rate = current_rate + self.learning_rate * (float(success) - current_rate)
        self.action_success_rates[state_key][action] = new_rate
    
    def _get_state_key(self, agv: AGVModel) -> str:
        """获取状态键"""
        return f"pos_{agv.x}_{agv.y}_load_{agv.current_load}_status_{agv.status.value}"


class MaskingPolicy:
    """掩码策略包装器"""
    
    def __init__(self, 
                 base_policy: nn.Module,
                 mask_generator: ActionMaskGenerator,
                 mask_value: float = -1e8):
        """
        初始化掩码策略
        
        Args:
            base_policy: 基础策略网络
            mask_generator: 掩码生成器
            mask_value: 掩码值（用于无效动作）
        """
        self.base_policy = base_policy
        self.mask_generator = mask_generator
        self.mask_value = mask_value
    
    def forward(self, 
                states: Dict[str, torch.Tensor],
                agvs: List[AGVModel],
                task_manager: TaskManager) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播（带掩码）
        
        Args:
            states: 状态字典
            agvs: AGV列表
            task_manager: 任务管理器
            
        Returns:
            masked_logits: 掩码后的logits
            action_probs: 动作概率
        """
        # 基础策略前向传播
        logits, _ = self.base_policy(
            states['agv_states'],
            states['task_states'],
            states['global_state']
        )
        
        # 生成掩码
        masks = self.mask_generator.generate_batch_masks(agvs, task_manager)
        masks_tensor = torch.FloatTensor(masks).to(logits.device)
        
        # 应用掩码
        masked_logits = logits + (masks_tensor - 1) * (-self.mask_value)
        
        # 计算概率
        action_probs = torch.softmax(masked_logits, dim=-1)
        
        return masked_logits, action_probs
    
    def sample_action(self, 
                     states: Dict[str, torch.Tensor],
                     agvs: List[AGVModel],
                     task_manager: TaskManager,
                     deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        采样动作（带掩码）
        
        Args:
            states: 状态字典
            agvs: AGV列表
            task_manager: 任务管理器
            deterministic: 是否确定性采样
            
        Returns:
            actions: 采样的动作
            log_probs: 对数概率
        """
        masked_logits, action_probs = self.forward(states, agvs, task_manager)
        
        if deterministic:
            actions = torch.argmax(action_probs, dim=-1)
        else:
            dist = torch.distributions.Categorical(probs=action_probs)
            actions = dist.sample()
        
        # 计算对数概率
        log_probs = torch.log(action_probs.gather(1, actions.unsqueeze(1)).squeeze(1) + 1e-8)
        
        return actions, log_probs


def create_action_mask_generator(grid_world: GridWorld, 
                               collision_detector: CollisionDetector,
                               config: Dict[str, bool] = None) -> ActionMaskGenerator:
    """
    创建动作掩码生成器的工厂函数
    
    Args:
        grid_world: 网格世界
        collision_detector: 碰撞检测器
        config: 配置字典
        
    Returns:
        mask_generator: 掩码生成器
    """
    if config is None:
        config = {
            'enable_safety_mask': True,
            'enable_task_mask': True,
            'enable_physics_mask': True
        }
    
    return ActionMaskGenerator(
        grid_world=grid_world,
        collision_detector=collision_detector,
        **config
    )
