"""
课程学习策略实现

实现三阶段渐进式课程学习：2AGV→3AGV→4AGV，包括自适应难度调节
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from enum import Enum
import logging
from dataclasses import dataclass
from collections import deque


class CurriculumStage(Enum):
    """课程学习阶段"""
    STAGE_1 = "2_agv"      # 2个AGV
    STAGE_2 = "3_agv"      # 3个AGV
    STAGE_3 = "4_agv"      # 4个AGV
    COMPLETED = "completed" # 完成所有阶段


class DifficultyLevel(Enum):
    """难度级别"""
    EASY = "easy"           # 简单：少任务，大间距
    MEDIUM = "medium"       # 中等：中等任务，中等间距
    HARD = "hard"          # 困难：多任务，小间距
    EXPERT = "expert"      # 专家：最多任务，最小间距


@dataclass
class CurriculumConfig:
    """课程学习配置"""
    # 阶段配置
    stage_1_episodes: int = 50000      # 阶段1训练轮数
    stage_2_episodes: int = 75000      # 阶段2训练轮数
    stage_3_episodes: int = 100000     # 阶段3训练轮数
    
    # 难度调节配置
    performance_window: int = 1000     # 性能评估窗口
    promotion_threshold: float = 0.8   # 晋级阈值
    demotion_threshold: float = 0.4    # 降级阈值
    min_episodes_per_difficulty: int = 5000  # 每个难度最少训练轮数
    
    # 自适应配置
    use_adaptive_difficulty: bool = True    # 是否使用自适应难度
    use_performance_based_promotion: bool = True  # 是否基于性能晋级
    use_gradual_transition: bool = True     # 是否使用渐进过渡
    
    # 性能指标权重
    success_rate_weight: float = 0.4       # 成功率权重
    efficiency_weight: float = 0.3         # 效率权重
    collision_weight: float = 0.2          # 碰撞率权重（负向）
    cooperation_weight: float = 0.1        # 协作度权重


class CurriculumLearningManager:
    """课程学习管理器"""
    
    def __init__(self, 
                 config: CurriculumConfig = None,
                 logger: logging.Logger = None):
        """
        初始化课程学习管理器
        
        Args:
            config: 课程学习配置
            logger: 日志记录器
        """
        self.config = config or CurriculumConfig()
        self.logger = logger or logging.getLogger(__name__)
        
        # 当前状态
        self.current_stage = CurriculumStage.STAGE_1
        self.current_difficulty = DifficultyLevel.EASY
        self.current_episode = 0
        self.stage_episode = 0
        self.difficulty_episode = 0
        
        # 性能跟踪
        self.performance_history = deque(maxlen=self.config.performance_window)
        self.stage_performance = {}
        self.difficulty_performance = {}
        
        # 统计信息
        self.stage_transitions = []
        self.difficulty_transitions = []
        self.promotion_count = 0
        self.demotion_count = 0
        
        self.logger.info(f"课程学习管理器初始化完成，起始阶段: {self.current_stage.value}")
    
    def get_current_config(self) -> Dict[str, Union[int, float, bool]]:
        """获取当前阶段和难度的环境配置"""
        # 基础配置
        config = {
            'num_agvs': self._get_num_agvs(),
            'max_tasks': self._get_max_tasks(),
            'min_task_distance': self._get_min_task_distance(),
            'agv_spawn_distance': self._get_agv_spawn_distance(),
            'task_generation_rate': self._get_task_generation_rate(),
            'episode_length': self._get_episode_length()
        }
        
        # 添加阶段和难度信息
        config.update({
            'current_stage': self.current_stage.value,
            'current_difficulty': self.current_difficulty.value,
            'stage_progress': self._get_stage_progress(),
            'difficulty_progress': self._get_difficulty_progress()
        })
        
        return config
    
    def _get_num_agvs(self) -> int:
        """获取当前阶段的AGV数量"""
        if self.current_stage == CurriculumStage.STAGE_1:
            return 2
        elif self.current_stage == CurriculumStage.STAGE_2:
            return 3
        elif self.current_stage == CurriculumStage.STAGE_3:
            return 4
        else:
            return 4
    
    def _get_max_tasks(self) -> int:
        """获取当前难度的最大任务数"""
        base_tasks = {
            CurriculumStage.STAGE_1: 4,
            CurriculumStage.STAGE_2: 6,
            CurriculumStage.STAGE_3: 8
        }.get(self.current_stage, 8)
        
        difficulty_multiplier = {
            DifficultyLevel.EASY: 0.5,
            DifficultyLevel.MEDIUM: 0.75,
            DifficultyLevel.HARD: 1.0,
            DifficultyLevel.EXPERT: 1.25
        }.get(self.current_difficulty, 1.0)
        
        return max(2, int(base_tasks * difficulty_multiplier))
    
    def _get_min_task_distance(self) -> float:
        """获取任务间最小距离"""
        base_distance = {
            DifficultyLevel.EASY: 8.0,
            DifficultyLevel.MEDIUM: 6.0,
            DifficultyLevel.HARD: 4.0,
            DifficultyLevel.EXPERT: 3.0
        }.get(self.current_difficulty, 6.0)
        
        return base_distance
    
    def _get_agv_spawn_distance(self) -> float:
        """获取AGV生成间距"""
        base_distance = {
            DifficultyLevel.EASY: 6.0,
            DifficultyLevel.MEDIUM: 4.0,
            DifficultyLevel.HARD: 3.0,
            DifficultyLevel.EXPERT: 2.0
        }.get(self.current_difficulty, 4.0)
        
        return base_distance
    
    def _get_task_generation_rate(self) -> float:
        """获取任务生成率"""
        base_rate = {
            DifficultyLevel.EASY: 0.1,
            DifficultyLevel.MEDIUM: 0.15,
            DifficultyLevel.HARD: 0.2,
            DifficultyLevel.EXPERT: 0.25
        }.get(self.current_difficulty, 0.15)
        
        return base_rate
    
    def _get_episode_length(self) -> int:
        """获取回合长度"""
        base_length = {
            CurriculumStage.STAGE_1: 200,
            CurriculumStage.STAGE_2: 300,
            CurriculumStage.STAGE_3: 400
        }.get(self.current_stage, 400)
        
        difficulty_multiplier = {
            DifficultyLevel.EASY: 0.8,
            DifficultyLevel.MEDIUM: 1.0,
            DifficultyLevel.HARD: 1.2,
            DifficultyLevel.EXPERT: 1.5
        }.get(self.current_difficulty, 1.0)
        
        return int(base_length * difficulty_multiplier)
    
    def _get_stage_progress(self) -> float:
        """获取当前阶段进度"""
        max_episodes = {
            CurriculumStage.STAGE_1: self.config.stage_1_episodes,
            CurriculumStage.STAGE_2: self.config.stage_2_episodes,
            CurriculumStage.STAGE_3: self.config.stage_3_episodes
        }.get(self.current_stage, 1)
        
        return min(1.0, self.stage_episode / max_episodes)
    
    def _get_difficulty_progress(self) -> float:
        """获取当前难度进度"""
        return min(1.0, self.difficulty_episode / self.config.min_episodes_per_difficulty)
    
    def update_performance(self, 
                          success_rate: float,
                          efficiency: float,
                          collision_rate: float,
                          cooperation_score: float) -> bool:
        """
        更新性能指标并检查是否需要调整难度或阶段
        
        Args:
            success_rate: 成功率 [0, 1]
            efficiency: 效率指标 [0, 1]
            collision_rate: 碰撞率 [0, 1]
            cooperation_score: 协作得分 [0, 1]
            
        Returns:
            是否发生了阶段或难度变化
        """
        # 计算综合性能分数
        performance_score = (
            self.config.success_rate_weight * success_rate +
            self.config.efficiency_weight * efficiency +
            self.config.collision_weight * (1.0 - collision_rate) +  # 碰撞率越低越好
            self.config.cooperation_weight * cooperation_score
        )
        
        # 添加到历史记录
        self.performance_history.append({
            'episode': self.current_episode,
            'performance_score': performance_score,
            'success_rate': success_rate,
            'efficiency': efficiency,
            'collision_rate': collision_rate,
            'cooperation_score': cooperation_score,
            'stage': self.current_stage.value,
            'difficulty': self.current_difficulty.value
        })
        
        # 更新计数器
        self.current_episode += 1
        self.stage_episode += 1
        self.difficulty_episode += 1
        
        # 检查是否需要调整
        changed = False
        
        if self.config.use_adaptive_difficulty:
            changed |= self._check_difficulty_adjustment()
        
        if self.config.use_performance_based_promotion:
            changed |= self._check_stage_promotion()
        
        return changed

    def _check_difficulty_adjustment(self) -> bool:
        """检查是否需要调整难度"""
        if len(self.performance_history) < self.config.performance_window // 2:
            return False

        if self.difficulty_episode < self.config.min_episodes_per_difficulty:
            return False

        # 计算最近性能
        recent_performance = [p['performance_score'] for p in list(self.performance_history)[-self.config.performance_window//2:]]
        avg_performance = np.mean(recent_performance)

        old_difficulty = self.current_difficulty

        # 检查是否需要提升难度
        if avg_performance >= self.config.promotion_threshold:
            if self.current_difficulty == DifficultyLevel.EASY:
                self.current_difficulty = DifficultyLevel.MEDIUM
            elif self.current_difficulty == DifficultyLevel.MEDIUM:
                self.current_difficulty = DifficultyLevel.HARD
            elif self.current_difficulty == DifficultyLevel.HARD:
                self.current_difficulty = DifficultyLevel.EXPERT

        # 检查是否需要降低难度
        elif avg_performance <= self.config.demotion_threshold:
            if self.current_difficulty == DifficultyLevel.EXPERT:
                self.current_difficulty = DifficultyLevel.HARD
            elif self.current_difficulty == DifficultyLevel.HARD:
                self.current_difficulty = DifficultyLevel.MEDIUM
            elif self.current_difficulty == DifficultyLevel.MEDIUM:
                self.current_difficulty = DifficultyLevel.EASY

        # 如果难度发生变化
        if old_difficulty != self.current_difficulty:
            self.difficulty_episode = 0
            self.difficulty_transitions.append({
                'episode': self.current_episode,
                'from': old_difficulty.value,
                'to': self.current_difficulty.value,
                'performance': avg_performance
            })

            if old_difficulty.value < self.current_difficulty.value:
                self.promotion_count += 1
                self.logger.info(f"难度提升: {old_difficulty.value} -> {self.current_difficulty.value} (性能: {avg_performance:.3f})")
            else:
                self.demotion_count += 1
                self.logger.info(f"难度降低: {old_difficulty.value} -> {self.current_difficulty.value} (性能: {avg_performance:.3f})")

            return True

        return False

    def _check_stage_promotion(self) -> bool:
        """检查是否需要晋级到下一阶段"""
        # 检查是否达到最低训练轮数
        min_episodes = {
            CurriculumStage.STAGE_1: self.config.stage_1_episodes,
            CurriculumStage.STAGE_2: self.config.stage_2_episodes,
            CurriculumStage.STAGE_3: self.config.stage_3_episodes
        }.get(self.current_stage, float('inf'))

        if self.stage_episode < min_episodes:
            return False

        # 检查性能是否达标
        if len(self.performance_history) < self.config.performance_window:
            return False

        recent_performance = [p['performance_score'] for p in list(self.performance_history)[-self.config.performance_window:]]
        avg_performance = np.mean(recent_performance)

        # 只有在困难或专家难度下表现良好才能晋级
        if self.current_difficulty not in [DifficultyLevel.HARD, DifficultyLevel.EXPERT]:
            return False

        if avg_performance < self.config.promotion_threshold:
            return False

        old_stage = self.current_stage

        # 晋级到下一阶段
        if self.current_stage == CurriculumStage.STAGE_1:
            self.current_stage = CurriculumStage.STAGE_2
        elif self.current_stage == CurriculumStage.STAGE_2:
            self.current_stage = CurriculumStage.STAGE_3
        elif self.current_stage == CurriculumStage.STAGE_3:
            self.current_stage = CurriculumStage.COMPLETED

        if old_stage != self.current_stage:
            self.stage_episode = 0
            self.difficulty_episode = 0
            # 新阶段从中等难度开始
            self.current_difficulty = DifficultyLevel.MEDIUM

            self.stage_transitions.append({
                'episode': self.current_episode,
                'from': old_stage.value,
                'to': self.current_stage.value,
                'performance': avg_performance
            })

            self.logger.info(f"阶段晋级: {old_stage.value} -> {self.current_stage.value} (性能: {avg_performance:.3f})")
            return True

        return False

    def is_completed(self) -> bool:
        """检查课程学习是否完成"""
        return self.current_stage == CurriculumStage.COMPLETED

    def get_statistics(self) -> Dict:
        """获取课程学习统计信息"""
        if not self.performance_history:
            return {}

        recent_performance = list(self.performance_history)[-100:] if len(self.performance_history) >= 100 else list(self.performance_history)

        stats = {
            'current_stage': self.current_stage.value,
            'current_difficulty': self.current_difficulty.value,
            'total_episodes': self.current_episode,
            'stage_episode': self.stage_episode,
            'difficulty_episode': self.difficulty_episode,
            'stage_progress': self._get_stage_progress(),
            'difficulty_progress': self._get_difficulty_progress(),
            'promotion_count': self.promotion_count,
            'demotion_count': self.demotion_count,
            'stage_transitions': len(self.stage_transitions),
            'difficulty_transitions': len(self.difficulty_transitions)
        }

        if recent_performance:
            stats.update({
                'recent_avg_performance': np.mean([p['performance_score'] for p in recent_performance]),
                'recent_avg_success_rate': np.mean([p['success_rate'] for p in recent_performance]),
                'recent_avg_efficiency': np.mean([p['efficiency'] for p in recent_performance]),
                'recent_avg_collision_rate': np.mean([p['collision_rate'] for p in recent_performance]),
                'recent_avg_cooperation': np.mean([p['cooperation_score'] for p in recent_performance])
            })

        return stats

    def save_state(self, filepath: str):
        """保存课程学习状态"""
        state = {
            'current_stage': self.current_stage.value,
            'current_difficulty': self.current_difficulty.value,
            'current_episode': self.current_episode,
            'stage_episode': self.stage_episode,
            'difficulty_episode': self.difficulty_episode,
            'performance_history': list(self.performance_history),
            'stage_transitions': self.stage_transitions,
            'difficulty_transitions': self.difficulty_transitions,
            'promotion_count': self.promotion_count,
            'demotion_count': self.demotion_count,
            'config': {
                'stage_1_episodes': self.config.stage_1_episodes,
                'stage_2_episodes': self.config.stage_2_episodes,
                'stage_3_episodes': self.config.stage_3_episodes,
                'performance_window': self.config.performance_window,
                'promotion_threshold': self.config.promotion_threshold,
                'demotion_threshold': self.config.demotion_threshold,
                'min_episodes_per_difficulty': self.config.min_episodes_per_difficulty
            }
        }

        import json
        with open(filepath, 'w') as f:
            json.dump(state, f, indent=2)

        self.logger.info(f"课程学习状态已保存到: {filepath}")

    def load_state(self, filepath: str):
        """加载课程学习状态"""
        import json
        with open(filepath, 'r') as f:
            state = json.load(f)

        self.current_stage = CurriculumStage(state['current_stage'])
        self.current_difficulty = DifficultyLevel(state['current_difficulty'])
        self.current_episode = state['current_episode']
        self.stage_episode = state['stage_episode']
        self.difficulty_episode = state['difficulty_episode']
        self.performance_history = deque(state['performance_history'], maxlen=self.config.performance_window)
        self.stage_transitions = state['stage_transitions']
        self.difficulty_transitions = state['difficulty_transitions']
        self.promotion_count = state['promotion_count']
        self.demotion_count = state['demotion_count']

        self.logger.info(f"课程学习状态已从 {filepath} 加载")
        self.logger.info(f"当前阶段: {self.current_stage.value}, 难度: {self.current_difficulty.value}")


class AdaptiveCurriculumScheduler:
    """自适应课程调度器"""

    def __init__(self,
                 curriculum_manager: CurriculumLearningManager,
                 adaptation_rate: float = 0.1,
                 stability_threshold: float = 0.05):
        """
        初始化自适应课程调度器

        Args:
            curriculum_manager: 课程学习管理器
            adaptation_rate: 适应率
            stability_threshold: 稳定性阈值
        """
        self.curriculum_manager = curriculum_manager
        self.adaptation_rate = adaptation_rate
        self.stability_threshold = stability_threshold

        # 性能趋势跟踪
        self.performance_trend = deque(maxlen=50)
        self.stability_counter = 0

    def should_accelerate_curriculum(self) -> bool:
        """判断是否应该加速课程进度"""
        if len(self.performance_trend) < 20:
            return False

        # 计算性能趋势
        recent_trend = np.polyfit(range(len(self.performance_trend)), list(self.performance_trend), 1)[0]

        # 如果性能持续提升且稳定，可以加速
        if recent_trend > 0 and self._is_performance_stable():
            return True

        return False

    def should_decelerate_curriculum(self) -> bool:
        """判断是否应该减慢课程进度"""
        if len(self.performance_trend) < 20:
            return False

        # 计算性能趋势
        recent_trend = np.polyfit(range(len(self.performance_trend)), list(self.performance_trend), 1)[0]

        # 如果性能下降或不稳定，应该减慢
        if recent_trend < -0.01 or not self._is_performance_stable():
            return True

        return False

    def _is_performance_stable(self) -> bool:
        """检查性能是否稳定"""
        if len(self.performance_trend) < 10:
            return False

        recent_std = np.std(list(self.performance_trend)[-10:])
        return recent_std < self.stability_threshold

    def update(self, performance_score: float):
        """更新调度器状态"""
        self.performance_trend.append(performance_score)

        if self._is_performance_stable():
            self.stability_counter += 1
        else:
            self.stability_counter = 0

    def get_recommended_adjustment(self) -> Dict[str, float]:
        """获取推荐的调整参数"""
        adjustments = {}

        if self.should_accelerate_curriculum():
            adjustments['difficulty_multiplier'] = 1.1
            adjustments['stage_acceleration'] = 1.2
        elif self.should_decelerate_curriculum():
            adjustments['difficulty_multiplier'] = 0.9
            adjustments['stage_acceleration'] = 0.8
        else:
            adjustments['difficulty_multiplier'] = 1.0
            adjustments['stage_acceleration'] = 1.0

        return adjustments
