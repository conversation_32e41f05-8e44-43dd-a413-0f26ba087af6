"""
注意力增强训练损失函数

设计结合注意力机制的训练损失函数，包括注意力正则化和时序一致性损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List, Union


class AttentionEnhancedLoss(nn.Module):
    """注意力增强损失函数"""
    
    def __init__(self, 
                 policy_loss_coef: float = 1.0,
                 value_loss_coef: float = 0.5,
                 entropy_coef: float = 0.01,
                 attention_reg_coef: float = 0.1,
                 temporal_consistency_coef: float = 0.05,
                 fusion_quality_coef: float = 0.02,
                 collaboration_reward_coef: float = 0.1,
                 max_grad_norm: float = 0.5,
                 use_clipped_value_loss: bool = True,
                 clip_param: float = 0.2):
        """
        初始化注意力增强损失函数
        
        Args:
            policy_loss_coef: 策略损失系数
            value_loss_coef: 价值损失系数
            entropy_coef: 熵损失系数
            attention_reg_coef: 注意力正则化系数
            temporal_consistency_coef: 时序一致性损失系数
            fusion_quality_coef: 融合质量损失系数
            collaboration_reward_coef: 协作奖励系数
            max_grad_norm: 最大梯度范数
            use_clipped_value_loss: 是否使用裁剪价值损失
            clip_param: 裁剪参数
        """
        super(AttentionEnhancedLoss, self).__init__()
        
        self.policy_loss_coef = policy_loss_coef
        self.value_loss_coef = value_loss_coef
        self.entropy_coef = entropy_coef
        self.attention_reg_coef = attention_reg_coef
        self.temporal_consistency_coef = temporal_consistency_coef
        self.fusion_quality_coef = fusion_quality_coef
        self.collaboration_reward_coef = collaboration_reward_coef
        self.max_grad_norm = max_grad_norm
        self.use_clipped_value_loss = use_clipped_value_loss
        self.clip_param = clip_param
        
        # 注意力正则化器
        self.attention_regularizer = AttentionRegularizer()
        
        # 时序一致性损失
        self.temporal_consistency_loss = TemporalConsistencyLoss()
        
        # 融合质量损失
        self.fusion_quality_loss = FusionQualityLoss()
        
        # 协作奖励计算器
        self.collaboration_reward_calculator = CollaborationRewardCalculator()
    
    def forward(self, 
                policy_outputs: Dict[str, torch.Tensor],
                value_outputs: Dict[str, torch.Tensor],
                actions: torch.Tensor,
                old_action_logprobs: torch.Tensor,
                advantages: torch.Tensor,
                returns: torch.Tensor,
                old_values: torch.Tensor,
                attention_history: Optional[List[Dict[str, torch.Tensor]]] = None) -> Dict[str, torch.Tensor]:
        """
        计算注意力增强损失
        
        Args:
            policy_outputs: 策略网络输出
            value_outputs: 价值网络输出
            actions: 动作
            old_action_logprobs: 旧动作对数概率
            advantages: 优势函数
            returns: 回报
            old_values: 旧价值估计
            attention_history: 注意力历史
            
        Returns:
            loss_dict: 损失字典
        """
        # 基础PPO损失
        ppo_losses = self._compute_ppo_losses(
            policy_outputs, value_outputs, actions, old_action_logprobs,
            advantages, returns, old_values
        )
        
        # 注意力正则化损失
        attention_reg_loss = self._compute_attention_regularization(policy_outputs, value_outputs)
        
        # 时序一致性损失
        temporal_loss = self._compute_temporal_consistency_loss(attention_history)
        
        # 融合质量损失
        fusion_loss = self._compute_fusion_quality_loss(policy_outputs, value_outputs)
        
        # 协作奖励
        collaboration_reward = self._compute_collaboration_reward(policy_outputs, value_outputs)
        
        # 总损失
        total_loss = (
            self.policy_loss_coef * ppo_losses['policy_loss'] +
            self.value_loss_coef * ppo_losses['value_loss'] -
            self.entropy_coef * ppo_losses['entropy_loss'] +
            self.attention_reg_coef * attention_reg_loss +
            self.temporal_consistency_coef * temporal_loss +
            self.fusion_quality_coef * fusion_loss -
            self.collaboration_reward_coef * collaboration_reward
        )
        
        return {
            'total_loss': total_loss,
            'policy_loss': ppo_losses['policy_loss'],
            'value_loss': ppo_losses['value_loss'],
            'entropy_loss': ppo_losses['entropy_loss'],
            'attention_reg_loss': attention_reg_loss,
            'temporal_consistency_loss': temporal_loss,
            'fusion_quality_loss': fusion_loss,
            'collaboration_reward': collaboration_reward,
            'approx_kl': ppo_losses['approx_kl'],
            'clipfrac': ppo_losses['clipfrac']
        }
    
    def _compute_ppo_losses(self, 
                           policy_outputs: Dict[str, torch.Tensor],
                           value_outputs: Dict[str, torch.Tensor],
                           actions: torch.Tensor,
                           old_action_logprobs: torch.Tensor,
                           advantages: torch.Tensor,
                           returns: torch.Tensor,
                           old_values: torch.Tensor) -> Dict[str, torch.Tensor]:
        """计算基础PPO损失"""
        # 策略损失
        action_logprobs = policy_outputs['action_logprobs']
        ratio = torch.exp(action_logprobs - old_action_logprobs)
        
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1.0 - self.clip_param, 1.0 + self.clip_param) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # 价值损失
        values = value_outputs['values'].squeeze(-1)
        
        if self.use_clipped_value_loss:
            value_pred_clipped = old_values + torch.clamp(
                values - old_values, -self.clip_param, self.clip_param
            )
            value_losses = (values - returns).pow(2)
            value_losses_clipped = (value_pred_clipped - returns).pow(2)
            value_loss = 0.5 * torch.max(value_losses, value_losses_clipped).mean()
        else:
            value_loss = 0.5 * (values - returns).pow(2).mean()
        
        # 熵损失
        entropy_loss = policy_outputs.get('entropy', torch.tensor(0.0))
        if isinstance(entropy_loss, torch.Tensor) and entropy_loss.numel() > 1:
            entropy_loss = entropy_loss.mean()
        
        # KL散度和裁剪比例
        with torch.no_grad():
            approx_kl = ((old_action_logprobs - action_logprobs).exp() - 1) - (old_action_logprobs - action_logprobs)
            approx_kl = approx_kl.mean()
            
            clipfrac = ((ratio - 1.0).abs() > self.clip_param).float().mean()
        
        return {
            'policy_loss': policy_loss,
            'value_loss': value_loss,
            'entropy_loss': entropy_loss,
            'approx_kl': approx_kl,
            'clipfrac': clipfrac
        }
    
    def _compute_attention_regularization(self, 
                                        policy_outputs: Dict[str, torch.Tensor],
                                        value_outputs: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算注意力正则化损失"""
        reg_loss = torch.tensor(0.0, device=next(iter(policy_outputs.values())).device)
        
        # 策略网络注意力正则化
        if 'attention_info' in policy_outputs:
            policy_attention_info = policy_outputs['attention_info']
            reg_loss += self.attention_regularizer(policy_attention_info)
        
        # 价值网络注意力正则化
        if 'attention_info' in value_outputs:
            value_attention_info = value_outputs['attention_info']
            reg_loss += self.attention_regularizer(value_attention_info)
        
        return reg_loss
    
    def _compute_temporal_consistency_loss(self, 
                                         attention_history: Optional[List[Dict[str, torch.Tensor]]]) -> torch.Tensor:
        """计算时序一致性损失"""
        if attention_history is None or len(attention_history) < 2:
            return torch.tensor(0.0)
        
        return self.temporal_consistency_loss(attention_history)
    
    def _compute_fusion_quality_loss(self, 
                                   policy_outputs: Dict[str, torch.Tensor],
                                   value_outputs: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算融合质量损失"""
        fusion_loss = torch.tensor(0.0, device=next(iter(policy_outputs.values())).device)
        
        # 策略网络融合质量
        if 'attention_info' in policy_outputs:
            policy_attention_info = policy_outputs['attention_info']
            if 'fusion_result' in policy_attention_info:
                fusion_loss += self.fusion_quality_loss(policy_attention_info['fusion_result'])
        
        # 价值网络融合质量
        if 'attention_info' in value_outputs:
            value_attention_info = value_outputs['attention_info']
            if 'fusion_result' in value_attention_info:
                fusion_loss += self.fusion_quality_loss(value_attention_info['fusion_result'])
        
        return fusion_loss
    
    def _compute_collaboration_reward(self, 
                                    policy_outputs: Dict[str, torch.Tensor],
                                    value_outputs: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算协作奖励"""
        collaboration_reward = torch.tensor(0.0, device=next(iter(policy_outputs.values())).device)
        
        # 从注意力信息中提取协作指标
        if 'attention_info' in policy_outputs:
            attention_info = policy_outputs['attention_info']
            collaboration_reward += self.collaboration_reward_calculator(attention_info)
        
        return collaboration_reward


class AttentionRegularizer(nn.Module):
    """注意力正则化器"""
    
    def __init__(self, 
                 sparsity_weight: float = 0.1,
                 diversity_weight: float = 0.1,
                 stability_weight: float = 0.1):
        super(AttentionRegularizer, self).__init__()
        
        self.sparsity_weight = sparsity_weight
        self.diversity_weight = diversity_weight
        self.stability_weight = stability_weight
    
    def forward(self, attention_info: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算注意力正则化损失"""
        reg_loss = torch.tensor(0.0)
        
        # 稀疏性正则化
        sparsity_loss = self._compute_sparsity_loss(attention_info)
        
        # 多样性正则化
        diversity_loss = self._compute_diversity_loss(attention_info)
        
        # 稳定性正则化
        stability_loss = self._compute_stability_loss(attention_info)
        
        reg_loss = (
            self.sparsity_weight * sparsity_loss +
            self.diversity_weight * diversity_loss +
            self.stability_weight * stability_loss
        )
        
        return reg_loss
    
    def _compute_sparsity_loss(self, attention_info: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算稀疏性损失"""
        # 获取设备信息
        device = None
        for value in attention_info.values():
            if isinstance(value, torch.Tensor):
                device = value.device
                break
            elif isinstance(value, dict):
                for sub_value in value.values():
                    if isinstance(sub_value, torch.Tensor):
                        device = sub_value.device
                        break
                if device is not None:
                    break

        sparsity_loss = torch.tensor(0.0, device=device)

        # 遍历所有注意力权重
        for key, value in attention_info.items():
            if 'attention' in key and isinstance(value, dict):
                if 'attention_weights' in value:
                    attention_weights = value['attention_weights']
                    # L1正则化促进稀疏性
                    sparsity_loss = sparsity_loss + torch.mean(torch.abs(attention_weights))

        return sparsity_loss
    
    def _compute_diversity_loss(self, attention_info: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算多样性损失"""
        # 获取设备信息
        device = None
        for value in attention_info.values():
            if isinstance(value, torch.Tensor):
                device = value.device
                break
            elif isinstance(value, dict):
                for sub_value in value.values():
                    if isinstance(sub_value, torch.Tensor):
                        device = sub_value.device
                        break
                if device is not None:
                    break

        diversity_loss = torch.tensor(0.0, device=device)

        # 鼓励不同注意力头的多样性
        for key, value in attention_info.items():
            if 'attention' in key and isinstance(value, dict):
                if 'attention_weights' in value:
                    attention_weights = value['attention_weights']
                    if attention_weights.dim() >= 3:  # [batch, heads, seq, seq] or [batch, seq, seq]
                        if attention_weights.dim() == 4:  # [batch, heads, seq, seq]
                            # 计算注意力头之间的相似性
                            num_heads = attention_weights.size(1)
                            similarity_loss = torch.tensor(0.0, device=attention_weights.device)

                            for i in range(num_heads):
                                for j in range(i + 1, num_heads):
                                    head_i = attention_weights[:, i, :, :].flatten(1)
                                    head_j = attention_weights[:, j, :, :].flatten(1)
                                    similarity = F.cosine_similarity(head_i, head_j, dim=1)
                                    similarity_loss = similarity_loss + torch.mean(similarity.abs())

                            diversity_loss = diversity_loss + similarity_loss / (num_heads * (num_heads - 1) / 2)
                        else:  # [batch, seq, seq] - 单头注意力，跳过多样性计算
                            pass

        return diversity_loss
    
    def _compute_stability_loss(self, attention_info: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算稳定性损失"""
        # 获取设备信息
        device = None
        for value in attention_info.values():
            if isinstance(value, torch.Tensor):
                device = value.device
                break
            elif isinstance(value, dict):
                for sub_value in value.values():
                    if isinstance(sub_value, torch.Tensor):
                        device = sub_value.device
                        break
                if device is not None:
                    break

        stability_loss = torch.tensor(0.0, device=device)

        # 检查注意力权重的方差
        for key, value in attention_info.items():
            if 'attention' in key and isinstance(value, dict):
                if 'attention_weights' in value:
                    attention_weights = value['attention_weights']
                    # 惩罚过大的方差
                    variance = torch.var(attention_weights, dim=-1)
                    stability_loss = stability_loss + torch.mean(variance)

        return stability_loss


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失"""
    
    def __init__(self, consistency_weight: float = 1.0):
        super(TemporalConsistencyLoss, self).__init__()
        self.consistency_weight = consistency_weight
    
    def forward(self, attention_history: List[Dict[str, torch.Tensor]]) -> torch.Tensor:
        """计算时序一致性损失"""
        if len(attention_history) < 2:
            return torch.tensor(0.0)

        # 获取设备信息
        device = None
        for history_item in attention_history:
            for value in history_item.values():
                if isinstance(value, torch.Tensor):
                    device = value.device
                    break
            if device is not None:
                break

        consistency_loss = torch.tensor(0.0, device=device)

        # 计算相邻时间步的注意力一致性
        for t in range(1, len(attention_history)):
            prev_attention = attention_history[t-1]
            curr_attention = attention_history[t]

            # 比较注意力权重的一致性
            for key in prev_attention.keys():
                if key in curr_attention and 'attention_weights' in key:
                    prev_weights = prev_attention[key]
                    curr_weights = curr_attention[key]

                    if isinstance(prev_weights, torch.Tensor) and isinstance(curr_weights, torch.Tensor):
                        if prev_weights.shape == curr_weights.shape:
                            # 计算L2距离
                            consistency_loss = consistency_loss + F.mse_loss(curr_weights, prev_weights)

        return self.consistency_weight * consistency_loss


class FusionQualityLoss(nn.Module):
    """融合质量损失"""
    
    def __init__(self, quality_weight: float = 1.0):
        super(FusionQualityLoss, self).__init__()
        self.quality_weight = quality_weight
    
    def forward(self, fusion_result: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算融合质量损失"""
        # 获取设备信息
        device = None
        for value in fusion_result.values():
            if isinstance(value, torch.Tensor):
                device = value.device
                break
            elif isinstance(value, dict):
                for sub_value in value.values():
                    if isinstance(sub_value, torch.Tensor):
                        device = sub_value.device
                        break
                if device is not None:
                    break

        quality_loss = torch.tensor(0.0, device=device)

        if 'quality_metrics' in fusion_result:
            quality_metrics = fusion_result['quality_metrics']

            # 鼓励高质量融合
            if 'overall_quality' in quality_metrics:
                quality_loss = quality_loss - torch.mean(quality_metrics['overall_quality'])

            # 惩罚低一致性
            if 'coherence_score' in quality_metrics:
                quality_loss = quality_loss - torch.mean(quality_metrics['coherence_score'])

            # 鼓励稳定性
            if 'stability_score' in quality_metrics:
                quality_loss = quality_loss - torch.mean(quality_metrics['stability_score'])

        return self.quality_weight * quality_loss


class CollaborationRewardCalculator(nn.Module):
    """协作奖励计算器"""
    
    def __init__(self, reward_weight: float = 1.0):
        super(CollaborationRewardCalculator, self).__init__()
        self.reward_weight = reward_weight
    
    def forward(self, attention_info: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算协作奖励"""
        # 获取设备信息
        device = None
        for value in attention_info.values():
            if isinstance(value, torch.Tensor):
                device = value.device
                break
            elif isinstance(value, dict):
                for sub_value in value.values():
                    if isinstance(sub_value, torch.Tensor):
                        device = sub_value.device
                        break
                if device is not None:
                    break

        collaboration_reward = torch.tensor(0.0, device=device)

        # 从协作注意力中提取协作指标
        if 'collaboration_attention' in attention_info:
            collab_info = attention_info['collaboration_attention']

            # 协作质量奖励
            if 'collaboration_quality' in collab_info:
                quality_metrics = collab_info['collaboration_quality']
                if 'overall_quality' in quality_metrics:
                    collaboration_reward = collaboration_reward + torch.mean(quality_metrics['overall_quality'])

        # 从融合结果中提取协作指标
        if 'fusion_result' in attention_info:
            fusion_info = attention_info['fusion_result']

            # 融合权重平衡奖励
            if 'adaptive_weights' in fusion_info:
                weights = fusion_info['adaptive_weights']
                # 鼓励权重平衡
                weight_entropy = -torch.sum(weights * torch.log(weights + 1e-8), dim=-1)
                collaboration_reward = collaboration_reward + torch.mean(weight_entropy)

        return self.reward_weight * collaboration_reward
