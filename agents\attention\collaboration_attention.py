"""
协作感知注意力机制

实现AGV之间的协作感知注意力，支持距离感知、速度预测和冲突避免
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, Tuple, Optional, List, Union
from enum import Enum

from .multi_head_attention import EnhancedMultiHeadAttention, AttentionType


class CollaborationType(Enum):
    """协作类型"""
    CLOSE_RANGE = "close"      # 近距离协作
    MEDIUM_RANGE = "medium"    # 中距离协作
    LONG_RANGE = "long"        # 远距离协作
    CONFLICT_AVOIDANCE = "conflict"  # 冲突避免


class DistanceLevel(Enum):
    """距离级别"""
    VERY_CLOSE = 0  # < 3 units
    CLOSE = 1       # 3-8 units
    MEDIUM = 2      # 8-15 units
    FAR = 3         # > 15 units


class CollaborationAwareAttention(nn.Module):
    """第二层协作感知注意力机制"""
    
    def __init__(self,
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 distance_thresholds: List[float] = [5.0, 15.0, 30.0],
                 collaboration_types: List[str] = ["close", "medium", "far"],
                 constraint_types: List[str] = None,
                 use_adaptive_temperature: bool = True,
                 use_adaptive_hierarchy: bool = True,
                 use_relative_position: bool = True,
                 use_enhanced_state_representation: bool = True,
                 use_multimodal_representation: bool = False,
                 use_information_aggregation: bool = True,
                 aggregation_method: str = "adaptive_fusion",
                 position_encoding_type: str = "directional",
                 safety_distance: float = 2.0,
                 prediction_horizon: float = 3.0,
                 history_length: int = 10,
                 dropout: float = 0.1):
        """
        初始化协作感知注意力机制

        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            distance_thresholds: 距离阈值列表
            collaboration_types: 协作类型列表
            constraint_types: 约束类型列表
            use_adaptive_temperature: 是否使用自适应温度
            use_adaptive_hierarchy: 是否使用自适应层次
            use_relative_position: 是否使用相对位置编码
            use_enhanced_state_representation: 是否使用增强状态表示
            use_multimodal_representation: 是否使用多模态表示
            use_information_aggregation: 是否使用信息聚合
            aggregation_method: 聚合方法
            position_encoding_type: 位置编码类型
            safety_distance: 安全距离
            prediction_horizon: 预测时间范围
            history_length: 历史长度
            dropout: Dropout概率
        """
        super(CollaborationAwareAttention, self).__init__()

        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.distance_thresholds = distance_thresholds
        self.collaboration_types = collaboration_types
        self.use_adaptive_temperature = use_adaptive_temperature
        self.use_adaptive_hierarchy = use_adaptive_hierarchy
        self.use_relative_position = use_relative_position
        self.use_enhanced_state_representation = use_enhanced_state_representation
        self.use_multimodal_representation = use_multimodal_representation
        self.use_information_aggregation = use_information_aggregation
        self.aggregation_method = aggregation_method
        self.safety_distance = safety_distance
        self.prediction_horizon = prediction_horizon
        self.history_length = history_length
        
        # 相对位置编码
        if use_relative_position:
            self.position_encoding = create_relative_position_encoding(
                encoding_type=position_encoding_type,
                embed_dim=embed_dim,
                max_distance=max(distance_thresholds),
                include_motion_prediction=True
            )
        
        # 层次化协作注意力
        if use_adaptive_hierarchy:
            self.hierarchical_collaboration = AdaptiveHierarchicalCollaboration(
                embed_dim=embed_dim,
                num_heads=num_heads,
                distance_thresholds=distance_thresholds,
                collaboration_types=collaboration_types,
                dropout=dropout
            )
        else:
            self.hierarchical_collaboration = HierarchicalCollaborationAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                distance_thresholds=distance_thresholds,
                collaboration_types=collaboration_types,
                dropout=dropout
            )
        
        # 自适应温度控制器
        if use_adaptive_temperature:
            self.temperature_controller = AdaptiveTemperatureController(
                embed_dim=embed_dim,
                base_temperature=1.0,
                temperature_range=(0.1, 5.0),
                adaptation_method="hybrid"
            )
        
        # 协作约束集成器
        if constraint_types is None:
            constraint_types = [
                "collision_risk", "path_conflict", "load_balance", 
                "collab_history", "safety_distance", "deadlock_prevention"
            ]
        
        self.constraint_integrator = CollaborationConstraintIntegrator(
            embed_dim=embed_dim,
            constraint_types=constraint_types,
            history_length=history_length,
            safety_distance=safety_distance,
            prediction_horizon=prediction_horizon
        )

        # 协作状态表示
        if use_enhanced_state_representation:
            if use_multimodal_representation:
                self.state_representation = MultiModalStateRepresentation(
                    embed_dim=embed_dim,
                    state_dim=10,  # 默认AGV状态维度
                    position_dim=2,
                    velocity_dim=2,
                    use_temporal_encoding=True,
                    use_intention_prediction=True,
                    use_relative_encoding=True,
                    max_sequence_length=history_length,
                    dropout=dropout
                )
            else:
                self.state_representation = CollaborationStateRepresentation(
                    embed_dim=embed_dim,
                    state_dim=10,  # 默认AGV状态维度
                    position_dim=2,
                    velocity_dim=2,
                    use_temporal_encoding=True,
                    use_intention_prediction=True,
                    use_relative_encoding=True,
                    max_sequence_length=history_length,
                    dropout=dropout
                )

        # 协作信息聚合
        if use_information_aggregation:
            # 将聚合方法字符串转换为枚举
            aggregation_method_enum = getattr(AggregationMethod, aggregation_method.upper(), AggregationMethod.ADAPTIVE_FUSION)

            self.information_aggregator = CollaborationInformationAggregator(
                embed_dim=embed_dim,
                num_layers=len(collaboration_types),  # 使用协作类型数量作为层数
                layer_names=collaboration_types,
                aggregation_method=aggregation_method_enum,
                use_layer_norm=True,
                use_residual=True,
                use_dynamic_weights=True,
                temperature=1.0,
                dropout=dropout
            )
        
        # 协作感知融合网络 - 动态计算输入维度
        self.collaboration_fusion = None  # 延迟初始化
        
        # 最终注意力计算
        self.final_attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 输出投影
        self.output_projection = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 协作质量评估器
        self.collaboration_quality_assessor = CollaborationQualityAssessor(embed_dim)
        
        # 动态权重调整器
        self.dynamic_weight_adjuster = nn.Sequential(
            nn.Linear(embed_dim + 6, embed_dim // 2),  # AGV特征 + 环境复杂度
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 3),  # 三个组件的权重
            nn.Softmax(dim=-1)
        )
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: torch.Tensor,
                agv_states: torch.Tensor,
                task_assignments: Optional[torch.Tensor] = None,
                global_context: Optional[torch.Tensor] = None,
                batch_id: str = "default",
                return_attention: bool = False) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            agv_states: AGV状态 [batch_size, num_agvs, state_dim]
            task_assignments: 任务分配 [batch_size, num_agvs, num_tasks]
            global_context: 全局上下文 [batch_size, embed_dim]
            batch_id: 批次ID
            return_attention: 是否返回注意力权重
            
        Returns:
            output_dict: 输出字典
        """
        batch_size, num_agvs, embed_dim = agv_embeddings.shape

        # 0. 增强状态表示（如果启用）
        enhanced_embeddings = agv_embeddings
        state_representation_result = {}

        if self.use_enhanced_state_representation:
            state_representation_result = self.state_representation(
                agv_states=agv_states,
                agv_positions=agv_positions,
                agv_velocities=agv_velocities,
                batch_id=batch_id
            )
            enhanced_embeddings = state_representation_result['enhanced_state_representation']

        # 1. 计算相对位置编码
        if self.use_relative_position:
            position_encodings = self.position_encoding(
                agv_positions, agv_velocities, agv_states
            )
        else:
            position_encodings = torch.zeros(
                batch_size, num_agvs, num_agvs, embed_dim,
                device=agv_embeddings.device
            )
        
        # 2. 层次化协作注意力
        hierarchical_result = self.hierarchical_collaboration(
            agv_embeddings=enhanced_embeddings,
            agv_positions=agv_positions,
            agv_velocities=agv_velocities,
            agv_states=agv_states,
            global_context=global_context
        )
        
        hierarchical_output = hierarchical_result['collaboration_output']
        level_attention_weights = hierarchical_result['level_attention_weights']
        
        # 3. 计算基础注意力分数（用于约束计算）
        base_attention_scores = self._compute_base_attention_scores(
            agv_embeddings, position_encodings
        )
        
        # 4. 协作约束集成
        constraint_result = self.constraint_integrator(
            agv_embeddings=agv_embeddings,
            agv_positions=agv_positions,
            agv_velocities=agv_velocities,
            agv_states=agv_states,
            base_attention_scores=base_attention_scores,
            task_assignments=task_assignments,
            batch_id=batch_id
        )
        
        constrained_attention_scores = constraint_result['constrained_attention_scores']
        constraint_details = constraint_result['constraint_details']
        
        # 5. 自适应温度调整
        if self.use_adaptive_temperature:
            temperature_result = self.temperature_controller(
                agv_embeddings=agv_embeddings,
                agv_positions=agv_positions,
                agv_velocities=agv_velocities,
                attention_scores=constrained_attention_scores
            )
            current_temperature = temperature_result['temperature']
            temperature_info = temperature_result['adaptation_info']
        else:
            current_temperature = torch.tensor(1.0, device=agv_embeddings.device)
            temperature_info = {'updated': False}
        
        # 6. 应用温度缩放
        scaled_attention_scores = constrained_attention_scores / current_temperature
        
        # 7. 计算最终注意力权重
        final_attention_weights = F.softmax(scaled_attention_scores, dim=-1)
        
        # 8. 应用注意力权重
        attended_features = torch.bmm(final_attention_weights, enhanced_embeddings)

        # 8.5. 协作信息聚合（如果启用）
        aggregation_result = {}
        if self.use_information_aggregation:
            # 准备层输出字典
            layer_outputs = {}
            level_outputs = hierarchical_result.get('level_outputs', {})

            for i, collab_type in enumerate(self.collaboration_types):
                if isinstance(level_outputs, dict) and collab_type in level_outputs:
                    layer_outputs[collab_type] = level_outputs[collab_type]
                elif isinstance(level_outputs, list) and i < len(level_outputs):
                    layer_outputs[collab_type] = level_outputs[i]
                else:
                    # 如果层输出不足，使用层次化输出
                    layer_outputs[collab_type] = hierarchical_output

            # 准备层注意力权重字典
            layer_attention_weights = {}
            level_attention_weights = hierarchical_result.get('level_attention_weights', {})

            if level_attention_weights:
                for i, collab_type in enumerate(self.collaboration_types):
                    if isinstance(level_attention_weights, dict) and collab_type in level_attention_weights:
                        layer_attention_weights[collab_type] = level_attention_weights[collab_type]
                    elif isinstance(level_attention_weights, list) and i < len(level_attention_weights):
                        layer_attention_weights[collab_type] = level_attention_weights[i]

            # 执行信息聚合
            aggregation_result = self.information_aggregator(
                layer_outputs=layer_outputs,
                layer_attention_weights=layer_attention_weights if layer_attention_weights else None,
                global_context=global_context,
                batch_id=batch_id
            )

            # 使用聚合后的输出
            aggregated_output = aggregation_result['aggregated_output']
        else:
            aggregated_output = hierarchical_output

        # 9. 协作感知融合
        # 组合聚合输出、约束调整和位置信息
        position_summary = torch.mean(position_encodings, dim=2)  # [batch_size, num_agvs, embed_dim]

        fusion_input = torch.cat([
            aggregated_output,
            attended_features,
            position_summary
        ], dim=-1)

        # 动态初始化协作感知融合网络
        if self.collaboration_fusion is None:
            fusion_input_dim = fusion_input.shape[-1]
            self.collaboration_fusion = nn.Sequential(
                nn.Linear(fusion_input_dim, embed_dim * 2),
                nn.LayerNorm(embed_dim * 2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(embed_dim * 2, embed_dim),
                nn.LayerNorm(embed_dim)
            ).to(fusion_input.device)

        fused_features = self.collaboration_fusion(fusion_input)
        
        # 10. 最终注意力计算
        final_output, final_attention = self.final_attention(
            fused_features, fused_features, fused_features
        )
        
        # 11. 输出投影
        collaboration_output = self.output_projection(final_output)
        
        # 12. 协作质量评估
        collaboration_quality = self.collaboration_quality_assessor.assess_quality(
            agv_embeddings, collaboration_output, final_attention_weights
        )
        
        # 13. 计算动态权重
        env_complexity = self._compute_environment_complexity(
            agv_positions, agv_velocities, agv_states
        )
        
        weight_input = torch.cat([
            torch.mean(agv_embeddings, dim=1),  # [batch_size, embed_dim]
            env_complexity  # [batch_size, 6]
        ], dim=-1)
        
        dynamic_weights = self.dynamic_weight_adjuster(weight_input)  # [batch_size, 3]
        
        # 组装输出字典
        output_dict = {
            'collaboration_output': collaboration_output,
            'final_attention_weights': final_attention_weights,
            'hierarchical_result': hierarchical_result,
            'constraint_result': constraint_result,
            'temperature_info': temperature_info,
            'current_temperature': current_temperature,
            'collaboration_quality': collaboration_quality,
            'dynamic_weights': dynamic_weights,
            'environment_complexity': env_complexity,
            'position_encodings': position_encodings,
            'state_representation_result': state_representation_result,
            'aggregation_result': aggregation_result,
            'enhanced_embeddings': enhanced_embeddings,
            'aggregated_output': aggregated_output if self.use_information_aggregation else hierarchical_output
        }
        
        if return_attention:
            output_dict.update({
                'level_attention_weights': level_attention_weights,
                'base_attention_scores': base_attention_scores,
                'constrained_attention_scores': constrained_attention_scores,
                'scaled_attention_scores': scaled_attention_scores,
                'final_attention': final_attention
            })
        
        return output_dict
    
    def _compute_base_attention_scores(self,
                                     agv_embeddings: torch.Tensor,
                                     position_encodings: torch.Tensor) -> torch.Tensor:
        """计算基础注意力分数"""
        batch_size, num_agvs, embed_dim = agv_embeddings.shape

        # 简单的点积注意力
        Q = agv_embeddings
        K = agv_embeddings + torch.mean(position_encodings, dim=2)

        attention_scores = torch.bmm(Q, K.transpose(1, 2)) / math.sqrt(embed_dim)

        return attention_scores
    
    def _compute_environment_complexity(self, 
                                      agv_positions: torch.Tensor,
                                      agv_velocities: torch.Tensor,
                                      agv_states: torch.Tensor) -> torch.Tensor:
        """计算环境复杂度"""
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 1. 空间密度
        pos_i = agv_positions.unsqueeze(2)
        pos_j = agv_positions.unsqueeze(1)
        distances = torch.norm(pos_j - pos_i, dim=-1)
        avg_distance = torch.sum(distances) / (num_agvs * (num_agvs - 1))
        spatial_density = 1.0 / (avg_distance + 1e-8)
        
        # 2. 速度方差
        speed_variance = torch.var(torch.norm(agv_velocities, dim=-1), dim=-1)
        
        # 3. 负载方差
        loads = agv_states[:, :, 4]  # 假设第4维是载重
        load_variance = torch.var(loads, dim=-1)
        
        # 4. 任务分布
        task_counts = agv_states[:, :, 6]  # 假设第6维是任务数
        task_variance = torch.var(task_counts, dim=-1)
        
        # 5. 协作需求
        close_pairs = torch.sum((distances < 5.0) & (distances > 0), dim=(1, 2)).float()
        collaboration_need = close_pairs / (num_agvs * (num_agvs - 1))
        
        # 6. 运动复杂度
        vel_i = agv_velocities.unsqueeze(2)
        vel_j = agv_velocities.unsqueeze(1)
        relative_velocities = vel_j - vel_i
        motion_complexity = torch.var(torch.norm(relative_velocities, dim=-1), dim=(1, 2))
        
        complexity_features = torch.stack([
            spatial_density.unsqueeze(0).expand(batch_size),
            speed_variance,
            load_variance,
            task_variance,
            collaboration_need,
            motion_complexity
        ], dim=-1)
        
        return complexity_features
    
    def get_collaboration_statistics(self, batch_id: str = "default") -> Dict[str, float]:
        """获取协作统计信息"""
        stats = {}
        
        # 温度统计
        if self.use_adaptive_temperature:
            temp_stats = self.temperature_controller.get_temperature_statistics()
            stats.update({f'temperature_{k}': v for k, v in temp_stats.items()})
        
        # 约束权重统计
        if hasattr(self.constraint_integrator, 'constraint_weights'):
            if isinstance(self.constraint_integrator.constraint_weights, nn.ParameterDict):
                for constraint_type, weight in self.constraint_integrator.constraint_weights.items():
                    stats[f'constraint_weight_{constraint_type}'] = weight.item()
        
        # 协作历史统计
        if hasattr(self.constraint_integrator.collaboration_history_tracker, 'collaboration_history'):
            history = self.constraint_integrator.collaboration_history_tracker.collaboration_history
            if batch_id in history:
                stats['collaboration_history_length'] = len(history[batch_id]['attention_history'])
        
        return stats
    
    def reset_collaboration_history(self, batch_id: str = None):
        """重置协作历史"""
        if hasattr(self.constraint_integrator.collaboration_history_tracker, 'collaboration_history'):
            history = self.constraint_integrator.collaboration_history_tracker.collaboration_history
            if batch_id is None:
                history.clear()
            elif batch_id in history:
                del history[batch_id]


class CollaborationQualityAssessor(nn.Module):
    """协作质量评估器"""
    
    def __init__(self, embed_dim: int):
        """
        初始化协作质量评估器
        
        Args:
            embed_dim: 嵌入维度
        """
        super(CollaborationQualityAssessor, self).__init__()
        
        self.embed_dim = embed_dim
        
        # 质量评估网络
        self.quality_assessor = nn.Sequential(
            nn.Linear(embed_dim * 3, embed_dim),  # 原始特征 + 协作特征 + 注意力特征
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, 4),  # 4个质量指标
            nn.Sigmoid()
        )
    
    def assess_quality(self, 
                      original_embeddings: torch.Tensor,
                      collaboration_output: torch.Tensor,
                      attention_weights: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        评估协作质量
        
        Args:
            original_embeddings: 原始AGV嵌入
            collaboration_output: 协作输出
            attention_weights: 注意力权重
            
        Returns:
            quality_metrics: 质量指标字典
        """
        batch_size, num_agvs, embed_dim = original_embeddings.shape
        
        # 计算注意力特征
        attention_entropy = -torch.sum(
            attention_weights * torch.log(attention_weights + 1e-8), dim=-1
        )  # [batch_size, num_agvs]

        # 扩展注意力特征到嵌入维度
        attention_features = attention_entropy.unsqueeze(-1).expand(batch_size, num_agvs, embed_dim)
        
        # 组合特征
        quality_input = torch.cat([
            original_embeddings,
            collaboration_output,
            attention_features
        ], dim=-1)
        
        # 评估质量
        quality_scores = self.quality_assessor(quality_input)  # [batch_size, num_agvs, 4]
        
        # 分解质量指标
        coordination_quality = quality_scores[:, :, 0]      # 协调质量
        efficiency_quality = quality_scores[:, :, 1]        # 效率质量
        safety_quality = quality_scores[:, :, 2]            # 安全质量
        adaptability_quality = quality_scores[:, :, 3]      # 适应性质量
        
        # 计算总体质量
        overall_quality = torch.mean(quality_scores, dim=-1)
        
        return {
            'coordination_quality': coordination_quality,
            'efficiency_quality': efficiency_quality,
            'safety_quality': safety_quality,
            'adaptability_quality': adaptability_quality,
            'overall_quality': overall_quality,
            'quality_scores': quality_scores
        }


class MultiModalCollaborationAttention(CollaborationAwareAttention):
    """多模态协作感知注意力"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 modalities: List[str] = ["spatial", "temporal", "semantic"],
                 **kwargs):
        """
        初始化多模态协作感知注意力
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            modalities: 模态列表
            **kwargs: 其他参数
        """
        super().__init__(embed_dim, num_heads, **kwargs)
        
        self.modalities = modalities
        self.num_modalities = len(modalities)
        
        # 为每个模态创建专门的注意力
        self.modality_attentions = nn.ModuleDict({
            modality: CollaborationAwareAttention(embed_dim, num_heads, **kwargs)
            for modality in modalities
        })
        
        # 模态融合网络
        self.modality_fusion = nn.Sequential(
            nn.Linear(embed_dim * self.num_modalities, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Dropout(kwargs.get('dropout', 0.1)),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 模态权重预测器
        self.modality_weight_predictor = nn.Sequential(
            nn.Linear(embed_dim + 6, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, self.num_modalities),
            nn.Softmax(dim=-1)
        )
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: torch.Tensor,
                agv_states: torch.Tensor,
                **kwargs) -> Dict[str, torch.Tensor]:
        """
        前向传播（多模态版本）
        """
        modality_outputs = {}
        modality_results = {}
        
        # 计算每个模态的协作注意力
        for modality in self.modalities:
            result = self.modality_attentions[modality](
                agv_embeddings, agv_positions, agv_velocities, agv_states, **kwargs
            )
            modality_outputs[modality] = result['collaboration_output']
            modality_results[modality] = result
        
        # 计算模态权重
        env_complexity = self._compute_environment_complexity(
            agv_positions, agv_velocities, agv_states
        )
        
        weight_input = torch.cat([
            torch.mean(agv_embeddings, dim=1),
            env_complexity
        ], dim=-1)
        
        modality_weights = self.modality_weight_predictor(weight_input)
        
        # 加权融合模态输出
        weighted_outputs = []
        for i, modality in enumerate(self.modalities):
            weight = modality_weights[:, i:i+1].unsqueeze(-1)  # [batch_size, 1, 1]
            weighted_outputs.append(weight * modality_outputs[modality])
        
        weighted_fusion = sum(weighted_outputs)
        
        # 拼接融合
        concatenated_outputs = torch.cat(list(modality_outputs.values()), dim=-1)
        concat_fusion = self.modality_fusion(concatenated_outputs)
        
        # 最终输出
        final_output = weighted_fusion + concat_fusion
        
        return {
            'collaboration_output': final_output,
            'modality_outputs': modality_outputs,
            'modality_results': modality_results,
            'modality_weights': modality_weights,
            'weighted_fusion': weighted_fusion,
            'concat_fusion': concat_fusion
        }
