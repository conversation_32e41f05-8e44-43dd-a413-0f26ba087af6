"""
碰撞检测与防止系统

实现AGV间碰撞检测、边界检查、障碍物检测和动作有效性验证
"""

import numpy as np
from typing import List, Dict, Tuple, Set, Optional
from enum import Enum
from envs.grid_world import GridWorld, CellType
from envs.agv_model import AGVModel, AGVAction


class CollisionType(Enum):
    """碰撞类型"""
    NO_COLLISION = 0
    AGV_COLLISION = 1      # AGV间碰撞
    WALL_COLLISION = 2     # 墙壁碰撞
    SHELF_COLLISION = 3    # 货架碰撞
    BOUNDARY_COLLISION = 4 # 边界碰撞
    DEADLOCK = 5          # 死锁


class CollisionDetector:
    """碰撞检测器"""
    
    def __init__(self, grid_world: GridWorld):
        """
        初始化碰撞检测器
        
        Args:
            grid_world: 网格世界环境
        """
        self.grid_world = grid_world
        self.collision_history = []  # 碰撞历史记录
        self.deadlock_threshold = 10  # 死锁检测阈值
        
        # 碰撞统计
        self.total_collisions = 0
        self.collision_counts = {
            CollisionType.AGV_COLLISION: 0,
            CollisionType.WALL_COLLISION: 0,
            CollisionType.SHELF_COLLISION: 0,
            CollisionType.BOUNDARY_COLLISION: 0,
            CollisionType.DEADLOCK: 0
        }
    
    def check_action_validity(self, 
                            agv: AGVModel, 
                            action: AGVAction,
                            other_agvs: List[AGVModel]) -> Tuple[bool, CollisionType]:
        """
        检查动作的有效性
        
        Args:
            agv: 执行动作的AGV
            action: 要执行的动作
            other_agvs: 其他AGV列表
            
        Returns:
            (是否有效, 碰撞类型)
        """
        # 计算动作后的新位置
        new_x, new_y = self._get_new_position(agv, action)
        
        # 检查边界碰撞
        if not self._is_within_bounds(new_x, new_y):
            return False, CollisionType.BOUNDARY_COLLISION
        
        # 检查障碍物碰撞
        collision_type = self._check_obstacle_collision(new_x, new_y)
        if collision_type != CollisionType.NO_COLLISION:
            return False, collision_type
        
        # 检查AGV间碰撞
        if self._check_agv_collision(agv, new_x, new_y, other_agvs):
            return False, CollisionType.AGV_COLLISION
        
        return True, CollisionType.NO_COLLISION
    
    def _get_new_position(self, agv: AGVModel, action: AGVAction) -> Tuple[int, int]:
        """根据动作计算新位置"""
        x, y = agv.position
        
        if action == AGVAction.MOVE_UP:
            return x, y - 1
        elif action == AGVAction.MOVE_DOWN:
            return x, y + 1
        elif action == AGVAction.MOVE_LEFT:
            return x - 1, y
        elif action == AGVAction.MOVE_RIGHT:
            return x + 1, y
        else:
            return x, y  # 非移动动作保持原位置
    
    def _is_within_bounds(self, x: int, y: int) -> bool:
        """检查位置是否在边界内"""
        return (0 <= x < self.grid_world.width and 
                0 <= y < self.grid_world.height)
    
    def _check_obstacle_collision(self, x: int, y: int) -> CollisionType:
        """检查障碍物碰撞"""
        if not self._is_within_bounds(x, y):
            return CollisionType.BOUNDARY_COLLISION
        
        cell_type = self.grid_world.get_cell_type(x, y)
        
        if cell_type == CellType.SHELF:
            return CollisionType.SHELF_COLLISION
        elif cell_type == CellType.WALL:
            return CollisionType.WALL_COLLISION
        
        return CollisionType.NO_COLLISION
    
    def _check_agv_collision(self, 
                           agv: AGVModel, 
                           new_x: int, 
                           new_y: int, 
                           other_agvs: List[AGVModel]) -> bool:
        """检查AGV间碰撞"""
        for other_agv in other_agvs:
            if other_agv.id == agv.id:
                continue
            
            # 检查位置重叠
            if (new_x, new_y) == other_agv.position:
                return True
            
            # 检查交换位置（两个AGV互相移动到对方位置）
            if ((new_x, new_y) == other_agv.position and 
                agv.position == (new_x, new_y)):
                return True
        
        return False
    
    def check_deadlock(self, agvs: List[AGVModel]) -> bool:
        """
        检查死锁情况
        
        Args:
            agvs: AGV列表
            
        Returns:
            是否存在死锁
        """
        # 简单的死锁检测：检查是否有AGV长时间无法移动
        stationary_agvs = 0
        
        for agv in agvs:
            if agv.total_wait_time > self.deadlock_threshold:
                stationary_agvs += 1
        
        # 如果超过一半的AGV长时间静止，认为可能存在死锁
        if stationary_agvs > len(agvs) // 2:
            self.collision_counts[CollisionType.DEADLOCK] += 1
            return True
        
        return False
    
    def resolve_collision(self, 
                         agv: AGVModel, 
                         collision_type: CollisionType,
                         other_agvs: List[AGVModel]) -> AGVAction:
        """
        解决碰撞的策略
        
        Args:
            agv: 发生碰撞的AGV
            collision_type: 碰撞类型
            other_agvs: 其他AGV列表
            
        Returns:
            建议的替代动作
        """
        if collision_type == CollisionType.AGV_COLLISION:
            return self._resolve_agv_collision(agv, other_agvs)
        elif collision_type in [CollisionType.WALL_COLLISION, 
                               CollisionType.SHELF_COLLISION,
                               CollisionType.BOUNDARY_COLLISION]:
            return self._resolve_obstacle_collision(agv)
        elif collision_type == CollisionType.DEADLOCK:
            return self._resolve_deadlock(agv, other_agvs)
        
        return AGVAction.WAIT
    
    def _resolve_agv_collision(self, agv: AGVModel, other_agvs: List[AGVModel]) -> AGVAction:
        """解决AGV间碰撞"""
        # 策略1：等待
        if np.random.random() < 0.5:
            return AGVAction.WAIT
        
        # 策略2：寻找替代路径
        alternative_actions = [
            AGVAction.MOVE_UP, AGVAction.MOVE_DOWN,
            AGVAction.MOVE_LEFT, AGVAction.MOVE_RIGHT
        ]
        
        for action in alternative_actions:
            is_valid, _ = self.check_action_validity(agv, action, other_agvs)
            if is_valid:
                return action
        
        return AGVAction.WAIT
    
    def _resolve_obstacle_collision(self, agv: AGVModel) -> AGVAction:
        """解决障碍物碰撞"""
        # 寻找可行的移动方向
        possible_actions = [
            AGVAction.MOVE_UP, AGVAction.MOVE_DOWN,
            AGVAction.MOVE_LEFT, AGVAction.MOVE_RIGHT
        ]
        
        valid_actions = []
        for action in possible_actions:
            new_x, new_y = self._get_new_position(agv, action)
            if (self._is_within_bounds(new_x, new_y) and 
                self._check_obstacle_collision(new_x, new_y) == CollisionType.NO_COLLISION):
                valid_actions.append(action)
        
        if valid_actions:
            return np.random.choice(valid_actions)
        
        return AGVAction.WAIT
    
    def _resolve_deadlock(self, agv: AGVModel, other_agvs: List[AGVModel]) -> AGVAction:
        """解决死锁"""
        # 死锁解决策略：随机移动到空闲位置
        free_positions = list(self.grid_world.free_spaces)
        occupied_positions = {other_agv.position for other_agv in other_agvs}
        
        available_positions = [pos for pos in free_positions 
                             if pos not in occupied_positions]
        
        if available_positions:
            target_pos = np.random.choice(len(available_positions))
            target_x, target_y = available_positions[target_pos]
            
            # 计算移动方向
            dx = target_x - agv.x
            dy = target_y - agv.y
            
            if abs(dx) > abs(dy):
                return AGVAction.MOVE_RIGHT if dx > 0 else AGVAction.MOVE_LEFT
            else:
                return AGVAction.MOVE_DOWN if dy > 0 else AGVAction.MOVE_UP
        
        return AGVAction.WAIT
    
    def generate_action_mask(self, 
                           agv: AGVModel, 
                           other_agvs: List[AGVModel]) -> np.ndarray:
        """
        生成动作掩码
        
        Args:
            agv: AGV模型
            other_agvs: 其他AGV列表
            
        Returns:
            动作掩码数组 (1表示可行，0表示不可行)
        """
        actions = [
            AGVAction.MOVE_UP, AGVAction.MOVE_DOWN,
            AGVAction.MOVE_LEFT, AGVAction.MOVE_RIGHT,
            AGVAction.WAIT, AGVAction.PICKUP, AGVAction.DROPOFF
        ]
        
        mask = np.zeros(len(actions), dtype=np.float32)
        
        for i, action in enumerate(actions):
            if action in [AGVAction.PICKUP, AGVAction.DROPOFF]:
                # 拾取和放置动作的有效性需要额外检查
                mask[i] = 1.0  # 暂时设为有效，具体检查由环境处理
            else:
                is_valid, _ = self.check_action_validity(agv, action, other_agvs)
                mask[i] = 1.0 if is_valid else 0.0
        
        return mask
    
    def record_collision(self, collision_type: CollisionType, agv_ids: List[int]):
        """记录碰撞事件"""
        collision_event = {
            'type': collision_type,
            'agv_ids': agv_ids,
            'timestamp': len(self.collision_history)
        }
        
        self.collision_history.append(collision_event)
        self.collision_counts[collision_type] += 1
        self.total_collisions += 1
    
    def get_collision_statistics(self) -> Dict[str, float]:
        """获取碰撞统计信息"""
        if self.total_collisions == 0:
            return {collision_type.name: 0.0 for collision_type in CollisionType}
        
        stats = {}
        for collision_type, count in self.collision_counts.items():
            stats[collision_type.name] = count / self.total_collisions
        
        stats['total_collisions'] = self.total_collisions
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.collision_history.clear()
        self.total_collisions = 0
        for collision_type in self.collision_counts:
            self.collision_counts[collision_type] = 0
    
    def get_safety_score(self, agvs: List[AGVModel]) -> float:
        """
        计算安全分数
        
        Args:
            agvs: AGV列表
            
        Returns:
            安全分数 (0-1, 1表示最安全)
        """
        if not agvs:
            return 1.0
        
        # 计算AGV间的最小距离
        min_distances = []
        for i, agv1 in enumerate(agvs):
            for j, agv2 in enumerate(agvs[i+1:], i+1):
                dist = self.grid_world.get_distance(agv1.position, agv2.position)
                min_distances.append(dist)
        
        if not min_distances:
            return 1.0
        
        avg_min_distance = np.mean(min_distances)
        safety_score = min(1.0, avg_min_distance / 3.0)  # 距离3格以上认为安全
        
        return safety_score
    
    def predict_collision_risk(self, 
                             agv: AGVModel, 
                             action: AGVAction,
                             other_agvs: List[AGVModel],
                             lookahead_steps: int = 3) -> float:
        """
        预测碰撞风险
        
        Args:
            agv: AGV模型
            action: 计划动作
            other_agvs: 其他AGV列表
            lookahead_steps: 前瞻步数
            
        Returns:
            碰撞风险 (0-1, 1表示高风险)
        """
        # 简单的风险评估：基于距离和相对速度
        new_x, new_y = self._get_new_position(agv, action)
        risk_score = 0.0
        
        for other_agv in other_agvs:
            if other_agv.id == agv.id:
                continue
            
            # 计算距离
            distance = self.grid_world.get_distance((new_x, new_y), other_agv.position)
            
            # 距离越近风险越高
            if distance <= 1:
                risk_score += 0.8
            elif distance <= 2:
                risk_score += 0.4
            elif distance <= 3:
                risk_score += 0.2
            
            # 如果两个AGV朝向相同方向，风险增加
            if (other_agv.is_moving and 
                abs(agv.theta - other_agv.theta) < np.pi / 4):
                risk_score += 0.3
        
        return min(1.0, risk_score)
    
    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_collision_statistics()
        return (f"CollisionDetector: {self.total_collisions} total collisions, "
                f"AGV: {stats.get('AGV_COLLISION', 0):.2f}, "
                f"Obstacle: {stats.get('WALL_COLLISION', 0) + stats.get('SHELF_COLLISION', 0):.2f}")
    
    def __repr__(self) -> str:
        return self.__str__()
