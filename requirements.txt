# 基于融合双层注意力机制的MAPPO多AGV协同调度系统
# 依赖包列表

# 深度学习框架
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# 强化学习
ray[rllib]>=2.0.0
gymnasium>=1.0.0

# 数据处理
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# 日志和监控
tensorboard>=2.8.0
wandb>=0.12.0

# 配置管理
pyyaml>=6.0
hydra-core>=1.1.0

# 工具库
tqdm>=4.62.0
click>=8.0.0
rich>=10.0.0

# 测试
pytest>=6.2.0
pytest-cov>=2.12.0

# 代码质量
black>=21.0.0
flake8>=3.9.0
isort>=5.9.0

# 其他
psutil>=5.8.0
opencv-python>=4.5.0
pillow>=8.3.0
scikit-learn>=1.0.0
