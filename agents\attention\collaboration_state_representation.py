"""
协作状态表示

实现增强的AGV状态表示，融合意图信息和相对位置
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, Tuple, Optional, List, Union
from enum import Enum


class IntentionType(Enum):
    """意图类型枚举"""
    PICKUP = "pickup"           # 取货意图
    DELIVERY = "delivery"       # 送货意图
    WAITING = "waiting"         # 等待意图
    CHARGING = "charging"       # 充电意图
    MAINTENANCE = "maintenance" # 维护意图
    IDLE = "idle"              # 空闲意图
    COLLABORATION = "collaboration"  # 协作意图


class CollaborationStateRepresentation(nn.Module):
    """协作状态表示模块"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 state_dim: int = 10,
                 position_dim: int = 2,
                 velocity_dim: int = 2,
                 intention_types: List[str] = None,
                 use_temporal_encoding: bool = True,
                 use_intention_prediction: bool = True,
                 use_relative_encoding: bool = True,
                 max_sequence_length: int = 50,
                 dropout: float = 0.1):
        """
        初始化协作状态表示模块
        
        Args:
            embed_dim: 嵌入维度
            state_dim: 原始状态维度
            position_dim: 位置维度
            velocity_dim: 速度维度
            intention_types: 意图类型列表
            use_temporal_encoding: 是否使用时序编码
            use_intention_prediction: 是否使用意图预测
            use_relative_encoding: 是否使用相对编码
            max_sequence_length: 最大序列长度
            dropout: Dropout概率
        """
        super(CollaborationStateRepresentation, self).__init__()
        
        self.embed_dim = embed_dim
        self.state_dim = state_dim
        self.position_dim = position_dim
        self.velocity_dim = velocity_dim
        self.use_temporal_encoding = use_temporal_encoding
        self.use_intention_prediction = use_intention_prediction
        self.use_relative_encoding = use_relative_encoding
        self.max_sequence_length = max_sequence_length
        
        if intention_types is None:
            intention_types = ["pickup", "delivery", "waiting", "charging", "maintenance", "idle", "collaboration"]
        self.intention_types = intention_types
        self.num_intentions = len(intention_types)
        
        # 基础状态编码器
        self.state_encoder = nn.Sequential(
            nn.Linear(state_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, embed_dim // 2)
        )
        
        # 位置编码器
        self.position_encoder = nn.Sequential(
            nn.Linear(position_dim, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, embed_dim // 4)
        )
        
        # 速度编码器
        self.velocity_encoder = nn.Sequential(
            nn.Linear(velocity_dim, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, embed_dim // 4)
        )
        
        # 意图编码器
        self.intention_embedding = nn.Embedding(self.num_intentions, embed_dim // 4)
        
        # 意图预测器
        if use_intention_prediction:
            self.intention_predictor = IntentionPredictor(
                embed_dim, self.num_intentions, dropout
            )
        
        # 时序编码器
        if use_temporal_encoding:
            self.temporal_encoder = TemporalEncoder(
                embed_dim, max_sequence_length, dropout
            )
        
        # 相对位置编码器
        if use_relative_encoding:
            self.relative_encoder = RelativeStateEncoder(
                embed_dim, dropout
            )
        
        # 协作上下文编码器
        self.collaboration_context_encoder = CollaborationContextEncoder(
            embed_dim, dropout
        )
        
        # 状态融合网络 - 动态计算输入维度
        self.state_fusion = None  # 将在第一次前向传播时初始化
        
        # 协作增强网络
        self.collaboration_enhancement = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),  # 个体状态 + 协作上下文
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 输出投影
        self.output_projection = nn.Linear(embed_dim, embed_dim)
        
        # 状态历史缓存
        self.state_history = {}
        self.intention_history = {}
    
    def forward(self, 
                agv_states: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: torch.Tensor,
                current_intentions: Optional[torch.Tensor] = None,
                time_step: Optional[int] = None,
                batch_id: str = "default") -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_states: AGV状态 [batch_size, num_agvs, state_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, position_dim]
            agv_velocities: AGV速度 [batch_size, num_agvs, velocity_dim]
            current_intentions: 当前意图 [batch_size, num_agvs] (可选)
            time_step: 当前时间步 (可选)
            batch_id: 批次ID
            
        Returns:
            result_dict: 结果字典
        """
        batch_size, num_agvs, _ = agv_states.shape
        
        # 1. 基础状态编码
        state_features = self.state_encoder(agv_states)  # [batch_size, num_agvs, embed_dim//2]
        
        # 2. 位置编码
        position_features = self.position_encoder(agv_positions)  # [batch_size, num_agvs, embed_dim//4]
        
        # 3. 速度编码
        velocity_features = self.velocity_encoder(agv_velocities)  # [batch_size, num_agvs, embed_dim//4]
        
        # 4. 意图处理
        if current_intentions is None:
            # 如果没有提供意图，则预测意图
            if self.use_intention_prediction:
                combined_features = torch.cat([state_features, position_features, velocity_features], dim=-1)
                intention_result = self.intention_predictor(combined_features, batch_id)
                predicted_intentions = intention_result['predicted_intentions']
                intention_features = self.intention_embedding(predicted_intentions)
                intention_probs = intention_result['intention_probabilities']
            else:
                # 默认为空闲意图
                default_intentions = torch.full((batch_size, num_agvs), 
                                              self.intention_types.index("idle"), 
                                              device=agv_states.device, dtype=torch.long)
                intention_features = self.intention_embedding(default_intentions)
                intention_probs = None
        else:
            intention_features = self.intention_embedding(current_intentions)
            intention_probs = None
        
        # 5. 时序编码
        temporal_features = None
        if self.use_temporal_encoding and time_step is not None:
            temporal_features = self.temporal_encoder(
                state_features, time_step, batch_id
            )
        
        # 6. 相对编码
        relative_features = None
        if self.use_relative_encoding:
            relative_features = self.relative_encoder(
                agv_positions, agv_velocities, state_features
            )
        
        # 7. 组合特征
        feature_list = [state_features, position_features, velocity_features, intention_features]
        
        if temporal_features is not None:
            feature_list.append(temporal_features)
        
        if relative_features is not None:
            feature_list.append(relative_features)
        
        combined_features = torch.cat(feature_list, dim=-1)

        # 8. 状态融合 - 动态初始化融合网络
        if self.state_fusion is None:
            fusion_input_dim = combined_features.shape[-1]
            self.state_fusion = nn.Sequential(
                nn.Linear(fusion_input_dim, self.embed_dim),
                nn.LayerNorm(self.embed_dim),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(self.embed_dim, self.embed_dim),
                nn.LayerNorm(self.embed_dim)
            ).to(combined_features.device)

        fused_state = self.state_fusion(combined_features)
        
        # 9. 协作上下文编码
        collaboration_context = self.collaboration_context_encoder(
            fused_state, agv_positions, agv_velocities
        )
        
        # 10. 协作增强
        enhanced_input = torch.cat([fused_state, collaboration_context], dim=-1)
        enhanced_state = self.collaboration_enhancement(enhanced_input)
        
        # 11. 输出投影
        final_representation = self.output_projection(enhanced_state)
        
        # 12. 更新历史
        self._update_history(final_representation, current_intentions, batch_id)
        
        # 组装结果
        result = {
            'enhanced_state_representation': final_representation,
            'base_state_features': state_features,
            'position_features': position_features,
            'velocity_features': velocity_features,
            'intention_features': intention_features,
            'collaboration_context': collaboration_context,
            'fused_state': fused_state
        }
        
        if intention_probs is not None:
            result['intention_probabilities'] = intention_probs
            result['predicted_intentions'] = predicted_intentions
        
        if temporal_features is not None:
            result['temporal_features'] = temporal_features
        
        if relative_features is not None:
            result['relative_features'] = relative_features
        
        return result
    
    def _update_history(self, 
                       state_representation: torch.Tensor,
                       intentions: Optional[torch.Tensor],
                       batch_id: str):
        """更新状态历史"""
        if batch_id not in self.state_history:
            self.state_history[batch_id] = []
            self.intention_history[batch_id] = []
        
        # 添加当前状态
        self.state_history[batch_id].append(state_representation.detach().cpu())
        
        # 添加意图历史
        if intentions is not None:
            self.intention_history[batch_id].append(intentions.detach().cpu())
        
        # 限制历史长度
        if len(self.state_history[batch_id]) > self.max_sequence_length:
            self.state_history[batch_id].pop(0)
            self.intention_history[batch_id].pop(0)
    
    def get_state_history(self, batch_id: str) -> Dict[str, List[torch.Tensor]]:
        """获取状态历史"""
        return {
            'state_history': self.state_history.get(batch_id, []),
            'intention_history': self.intention_history.get(batch_id, [])
        }
    
    def reset_history(self, batch_id: str = None):
        """重置历史"""
        if batch_id is None:
            self.state_history.clear()
            self.intention_history.clear()
        else:
            if batch_id in self.state_history:
                del self.state_history[batch_id]
            if batch_id in self.intention_history:
                del self.intention_history[batch_id]


class IntentionPredictor(nn.Module):
    """意图预测器"""
    
    def __init__(self, embed_dim: int, num_intentions: int, dropout: float = 0.1):
        super(IntentionPredictor, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_intentions = num_intentions
        
        # 意图预测网络
        self.intention_predictor = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, num_intentions)
        )
        
        # 意图历史
        self.intention_history = {}
    
    def forward(self, features: torch.Tensor, batch_id: str) -> Dict[str, torch.Tensor]:
        """预测意图"""
        # 预测意图概率
        intention_logits = self.intention_predictor(features)
        intention_probs = F.softmax(intention_logits, dim=-1)
        
        # 选择最可能的意图
        predicted_intentions = torch.argmax(intention_probs, dim=-1)
        
        # 更新历史
        if batch_id not in self.intention_history:
            self.intention_history[batch_id] = []
        
        self.intention_history[batch_id].append(predicted_intentions.detach().cpu())
        
        # 限制历史长度
        if len(self.intention_history[batch_id]) > 10:
            self.intention_history[batch_id].pop(0)
        
        return {
            'intention_probabilities': intention_probs,
            'predicted_intentions': predicted_intentions,
            'intention_logits': intention_logits
        }


class TemporalEncoder(nn.Module):
    """时序编码器"""
    
    def __init__(self, embed_dim: int, max_sequence_length: int, dropout: float = 0.1):
        super(TemporalEncoder, self).__init__()
        
        self.embed_dim = embed_dim
        self.max_sequence_length = max_sequence_length
        
        # 位置编码
        self.positional_encoding = nn.Parameter(
            torch.randn(max_sequence_length, embed_dim // 4)
        )
        
        # 时序变换网络
        self.temporal_transform = nn.Sequential(
            nn.Linear(embed_dim // 2, embed_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 4, embed_dim // 4)
        )
        
        # 状态历史
        self.state_history = {}
    
    def forward(self, state_features: torch.Tensor, time_step: int, batch_id: str) -> torch.Tensor:
        """时序编码"""
        batch_size, num_agvs, _ = state_features.shape
        
        # 获取位置编码
        pos_idx = min(time_step, self.max_sequence_length - 1)
        pos_encoding = self.positional_encoding[pos_idx].unsqueeze(0).unsqueeze(0)
        pos_encoding = pos_encoding.expand(batch_size, num_agvs, -1)
        
        # 时序变换
        temporal_features = self.temporal_transform(state_features)
        
        # 添加位置编码
        temporal_features = temporal_features + pos_encoding
        
        return temporal_features


class RelativeStateEncoder(nn.Module):
    """相对状态编码器"""
    
    def __init__(self, embed_dim: int, dropout: float = 0.1):
        super(RelativeStateEncoder, self).__init__()
        
        self.embed_dim = embed_dim
        
        # 相对位置编码器
        self.relative_position_encoder = nn.Sequential(
            nn.Linear(2, embed_dim // 4),  # 相对位置
            nn.ReLU(),
            nn.Linear(embed_dim // 4, embed_dim // 4)
        )
        
        # 相对速度编码器
        self.relative_velocity_encoder = nn.Sequential(
            nn.Linear(2, embed_dim // 4),  # 相对速度
            nn.ReLU(),
            nn.Linear(embed_dim // 4, embed_dim // 4)
        )
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(embed_dim // 2, embed_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, embed_dim // 2)
        )
    
    def forward(self, 
                agv_positions: torch.Tensor,
                agv_velocities: torch.Tensor,
                state_features: torch.Tensor) -> torch.Tensor:
        """相对状态编码"""
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算相对位置
        pos_i = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        pos_j = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        relative_positions = pos_j - pos_i  # [batch_size, num_agvs, num_agvs, 2]
        
        # 计算相对速度
        vel_i = agv_velocities.unsqueeze(2)
        vel_j = agv_velocities.unsqueeze(1)
        relative_velocities = vel_j - vel_i  # [batch_size, num_agvs, num_agvs, 2]
        
        # 编码相对位置和速度
        rel_pos_features = self.relative_position_encoder(relative_positions)
        rel_vel_features = self.relative_velocity_encoder(relative_velocities)
        
        # 融合相对特征
        relative_features = torch.cat([rel_pos_features, rel_vel_features], dim=-1)
        relative_features = self.fusion_network(relative_features)
        
        # 聚合到每个AGV
        aggregated_features = torch.mean(relative_features, dim=2)  # [batch_size, num_agvs, embed_dim//2]
        
        return aggregated_features


class CollaborationContextEncoder(nn.Module):
    """协作上下文编码器"""
    
    def __init__(self, embed_dim: int, dropout: float = 0.1):
        super(CollaborationContextEncoder, self).__init__()
        
        self.embed_dim = embed_dim
        
        # 局部上下文编码器
        self.local_context_encoder = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, embed_dim // 2)
        )
        
        # 全局上下文编码器
        self.global_context_encoder = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, embed_dim // 2)
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        # 上下文融合
        self.context_fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim)
        )
    
    def forward(self, 
                state_representations: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: torch.Tensor) -> torch.Tensor:
        """协作上下文编码"""
        batch_size, num_agvs, embed_dim = state_representations.shape
        
        # 计算距离掩码（只关注附近的AGV）
        pos_i = agv_positions.unsqueeze(2)
        pos_j = agv_positions.unsqueeze(1)
        distances = torch.norm(pos_j - pos_i, dim=-1)
        
        # 创建注意力掩码（距离超过10米的AGV不参与协作上下文）
        attention_mask = distances > 10.0
        
        # 自注意力计算协作上下文
        context_output, attention_weights = self.attention(
            state_representations, state_representations, state_representations,
            key_padding_mask=None,
            attn_mask=attention_mask.repeat(4, 1, 1) if attention_mask.any() else None
        )
        
        # 上下文融合
        collaboration_context = self.context_fusion(context_output)
        
        return collaboration_context


class MultiModalStateRepresentation(CollaborationStateRepresentation):
    """多模态状态表示"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 modalities: List[str] = None,
                 **kwargs):
        """
        初始化多模态状态表示
        
        Args:
            embed_dim: 嵌入维度
            modalities: 模态列表
            **kwargs: 其他参数
        """
        super().__init__(embed_dim, **kwargs)
        
        if modalities is None:
            modalities = ["kinematic", "semantic", "temporal", "social"]
        self.modalities = modalities
        self.num_modalities = len(modalities)
        
        # 为每个模态创建编码器
        self.modality_encoders = nn.ModuleDict({
            modality: nn.Sequential(
                nn.Linear(embed_dim, embed_dim),
                nn.ReLU(),
                nn.Linear(embed_dim, embed_dim)
            ) for modality in modalities
        })
        
        # 模态融合网络
        self.modality_fusion = nn.Sequential(
            nn.Linear(embed_dim * self.num_modalities, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 模态权重预测器
        self.modality_weight_predictor = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, self.num_modalities),
            nn.Softmax(dim=-1)
        )
    
    def forward(self, *args, **kwargs) -> Dict[str, torch.Tensor]:
        """多模态前向传播"""
        # 获取基础表示
        base_result = super().forward(*args, **kwargs)
        base_representation = base_result['enhanced_state_representation']
        
        # 为每个模态编码
        modality_representations = {}
        for modality in self.modalities:
            modality_representations[modality] = self.modality_encoders[modality](base_representation)
        
        # 计算模态权重
        modality_weights = self.modality_weight_predictor(base_representation)
        
        # 加权融合
        weighted_representations = []
        for i, modality in enumerate(self.modalities):
            weight = modality_weights[:, :, i:i+1]
            weighted_representations.append(weight * modality_representations[modality])
        
        weighted_fusion = sum(weighted_representations)
        
        # 拼接融合
        concatenated = torch.cat(list(modality_representations.values()), dim=-1)
        concat_fusion = self.modality_fusion(concatenated)
        
        # 最终表示
        final_representation = weighted_fusion + concat_fusion
        
        # 更新结果
        base_result.update({
            'multimodal_representation': final_representation,
            'modality_representations': modality_representations,
            'modality_weights': modality_weights,
            'weighted_fusion': weighted_fusion,
            'concat_fusion': concat_fusion
        })
        
        return base_result
