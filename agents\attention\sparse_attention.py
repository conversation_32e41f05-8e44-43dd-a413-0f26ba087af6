"""
Top-K稀疏化注意力机制

实现Top-K稀疏注意力机制，将计算复杂度从O(n²)降低到O(nk)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List
import math


class TopKSparseAttention(nn.Module):
    """Top-K稀疏注意力机制"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 k_ratio: float = 0.5,
                 temperature: float = 1.0,
                 dropout: float = 0.1,
                 use_relative_position: bool = True):
        """
        初始化Top-K稀疏注意力
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            k_ratio: Top-K比例（相对于序列长度）
            temperature: 温度参数
            dropout: Dropout概率
            use_relative_position: 是否使用相对位置编码
        """
        super(TopKSparseAttention, self).__init__()
        
        assert embed_dim % num_heads == 0, "embed_dim必须能被num_heads整除"
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.k_ratio = k_ratio
        self.temperature = temperature
        self.use_relative_position = use_relative_position
        
        # 查询、键、值投影层
        self.q_proj = nn.Linear(embed_dim, embed_dim, bias=False)
        self.k_proj = nn.Linear(embed_dim, embed_dim, bias=False)
        self.v_proj = nn.Linear(embed_dim, embed_dim, bias=False)
        
        # 输出投影层
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 相对位置编码
        if use_relative_position:
            self.relative_position_embedding = nn.Parameter(
                torch.randn(2 * 64 - 1, self.head_dim)  # 假设最大相对距离为64
            )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        nn.init.xavier_uniform_(self.q_proj.weight)
        nn.init.xavier_uniform_(self.k_proj.weight)
        nn.init.xavier_uniform_(self.v_proj.weight)
        nn.init.xavier_uniform_(self.out_proj.weight)
        nn.init.constant_(self.out_proj.bias, 0)
        
        if self.use_relative_position:
            nn.init.normal_(self.relative_position_embedding, 0, 0.1)
    
    def _get_relative_positions(self, seq_len_q: int, seq_len_k: int) -> torch.Tensor:
        """
        获取相对位置矩阵
        
        Args:
            seq_len_q: 查询序列长度
            seq_len_k: 键序列长度
            
        Returns:
            relative_positions: 相对位置矩阵 [seq_len_q, seq_len_k]
        """
        range_q = torch.arange(seq_len_q)
        range_k = torch.arange(seq_len_k)
        
        # 计算相对位置
        relative_positions = range_q.unsqueeze(1) - range_k.unsqueeze(0)
        
        # 将相对位置映射到正数范围
        relative_positions = relative_positions + (seq_len_k - 1)
        
        return relative_positions
    
    def _compute_top_k_mask(self, 
                           attention_scores: torch.Tensor,
                           k: int) -> torch.Tensor:
        """
        计算Top-K掩码
        
        Args:
            attention_scores: 注意力分数 [batch_size, num_heads, seq_len_q, seq_len_k]
            k: Top-K值
            
        Returns:
            mask: Top-K掩码 [batch_size, num_heads, seq_len_q, seq_len_k]
        """
        batch_size, num_heads, seq_len_q, seq_len_k = attention_scores.shape
        
        # 获取Top-K索引
        _, top_k_indices = torch.topk(attention_scores, k, dim=-1)  # [batch_size, num_heads, seq_len_q, k]
        
        # 创建掩码
        mask = torch.zeros_like(attention_scores, dtype=torch.bool)
        
        # 使用scatter_填充Top-K位置
        mask.scatter_(-1, top_k_indices, True)
        
        return mask
    
    def forward(self, 
                query: torch.Tensor,
                key: torch.Tensor,
                value: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                return_attention: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播
        
        Args:
            query: 查询张量 [batch_size, seq_len_q, embed_dim]
            key: 键张量 [batch_size, seq_len_k, embed_dim]
            value: 值张量 [batch_size, seq_len_v, embed_dim]
            attention_mask: 注意力掩码 [batch_size, seq_len_q, seq_len_k]
            return_attention: 是否返回注意力权重
            
        Returns:
            output: 输出张量 [batch_size, seq_len_q, embed_dim]
            attention_weights: 注意力权重（可选）
        """
        batch_size, seq_len_q, _ = query.shape
        seq_len_k = key.shape[1]
        seq_len_v = value.shape[1]
        
        assert seq_len_k == seq_len_v, "键和值的序列长度必须相同"
        
        # 计算Top-K值
        k = max(1, int(self.k_ratio * seq_len_k))
        
        # 投影到查询、键、值
        Q = self.q_proj(query)  # [batch_size, seq_len_q, embed_dim]
        K = self.k_proj(key)    # [batch_size, seq_len_k, embed_dim]
        V = self.v_proj(value)  # [batch_size, seq_len_v, embed_dim]
        
        # 重塑为多头格式
        Q = Q.view(batch_size, seq_len_q, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, seq_len_v, self.num_heads, self.head_dim).transpose(1, 2)
        # 形状: [batch_size, num_heads, seq_len, head_dim]
        
        # 计算注意力分数
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)
        # 形状: [batch_size, num_heads, seq_len_q, seq_len_k]
        
        # 添加相对位置编码
        if self.use_relative_position:
            relative_positions = self._get_relative_positions(seq_len_q, seq_len_k)
            relative_positions = relative_positions.to(query.device)
            
            # 限制相对位置范围
            max_relative_position = self.relative_position_embedding.shape[0] // 2
            relative_positions = torch.clamp(
                relative_positions, 
                -max_relative_position, 
                max_relative_position
            ) + max_relative_position
            
            # 获取相对位置嵌入
            relative_embeddings = self.relative_position_embedding[relative_positions]
            # 形状: [seq_len_q, seq_len_k, head_dim]
            
            # 计算相对位置注意力
            relative_attention = torch.einsum('bhqd,qkd->bhqk', Q, relative_embeddings)
            attention_scores = attention_scores + relative_attention
        
        # 应用温度缩放
        attention_scores = attention_scores / self.temperature
        
        # 应用注意力掩码
        if attention_mask is not None:
            # 扩展掩码维度以匹配多头注意力
            if attention_mask.dim() == 3:
                attention_mask = attention_mask.unsqueeze(1)  # [batch_size, 1, seq_len_q, seq_len_k]
            
            attention_scores = attention_scores.masked_fill(
                attention_mask == 0, float('-inf')
            )
        
        # 计算Top-K掩码
        top_k_mask = self._compute_top_k_mask(attention_scores, k)
        
        # 应用Top-K稀疏化
        sparse_attention_scores = attention_scores.masked_fill(~top_k_mask, float('-inf'))
        
        # 计算注意力权重
        attention_weights = F.softmax(sparse_attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重到值
        output = torch.matmul(attention_weights, V)
        # 形状: [batch_size, num_heads, seq_len_q, head_dim]
        
        # 重塑输出
        output = output.transpose(1, 2).contiguous().view(
            batch_size, seq_len_q, self.embed_dim
        )
        
        # 输出投影
        output = self.out_proj(output)
        
        if return_attention:
            # 平均多头注意力权重用于返回
            avg_attention_weights = attention_weights.mean(dim=1)
            return output, avg_attention_weights
        else:
            return output, None


class AdaptiveTopKSparseAttention(TopKSparseAttention):
    """自适应Top-K稀疏注意力"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 k_ratio_range: Tuple[float, float] = (0.3, 0.8),
                 temperature: float = 1.0,
                 dropout: float = 0.1,
                 use_relative_position: bool = True,
                 adaptation_method: str = "entropy"):
        """
        初始化自适应Top-K稀疏注意力
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            k_ratio_range: Top-K比例范围
            temperature: 温度参数
            dropout: Dropout概率
            use_relative_position: 是否使用相对位置编码
            adaptation_method: 自适应方法 ("entropy", "variance", "learned")
        """
        super().__init__(embed_dim, num_heads, k_ratio_range[0], temperature, dropout, use_relative_position)
        
        self.k_ratio_range = k_ratio_range
        self.adaptation_method = adaptation_method
        
        if adaptation_method == "learned":
            # 学习的自适应机制
            self.k_predictor = nn.Sequential(
                nn.Linear(embed_dim, embed_dim // 4),
                nn.ReLU(),
                nn.Linear(embed_dim // 4, 1),
                nn.Sigmoid()
            )
    
    def _compute_adaptive_k(self, 
                          query: torch.Tensor,
                          key: torch.Tensor,
                          seq_len_k: int) -> int:
        """
        计算自适应K值
        
        Args:
            query: 查询张量
            key: 键张量
            seq_len_k: 键序列长度
            
        Returns:
            k: 自适应K值
        """
        if self.adaptation_method == "entropy":
            # 基于注意力熵的自适应
            with torch.no_grad():
                # 计算简化的注意力分数
                Q = self.q_proj(query.mean(dim=1, keepdim=True))  # [batch_size, 1, embed_dim]
                K = self.k_proj(key)  # [batch_size, seq_len_k, embed_dim]
                
                scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.embed_dim)
                probs = F.softmax(scores, dim=-1)
                
                # 计算熵
                entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
                normalized_entropy = entropy / math.log(seq_len_k)
                
                # 根据熵调整k_ratio
                k_ratio = self.k_ratio_range[0] + (self.k_ratio_range[1] - self.k_ratio_range[0]) * normalized_entropy.mean()
                
        elif self.adaptation_method == "variance":
            # 基于特征方差的自适应
            with torch.no_grad():
                key_variance = torch.var(key, dim=1).mean()
                normalized_variance = torch.sigmoid(key_variance)
                
                k_ratio = self.k_ratio_range[0] + (self.k_ratio_range[1] - self.k_ratio_range[0]) * normalized_variance
                
        elif self.adaptation_method == "learned":
            # 学习的自适应机制
            query_mean = query.mean(dim=1)  # [batch_size, embed_dim]
            k_ratio_pred = self.k_predictor(query_mean).mean()  # 标量
            
            k_ratio = self.k_ratio_range[0] + (self.k_ratio_range[1] - self.k_ratio_range[0]) * k_ratio_pred
        
        else:
            k_ratio = self.k_ratio
        
        k = max(1, int(k_ratio * seq_len_k))
        return k
    
    def forward(self, 
                query: torch.Tensor,
                key: torch.Tensor,
                value: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                return_attention: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播（自适应版本）
        """
        seq_len_k = key.shape[1]
        
        # 计算自适应K值
        k = self._compute_adaptive_k(query, key, seq_len_k)
        
        # 临时设置k_ratio
        original_k_ratio = self.k_ratio
        self.k_ratio = k / seq_len_k
        
        # 调用父类方法
        output, attention_weights = super().forward(
            query, key, value, attention_mask, return_attention
        )
        
        # 恢复原始k_ratio
        self.k_ratio = original_k_ratio
        
        return output, attention_weights


class MultiScaleTopKAttention(nn.Module):
    """多尺度Top-K注意力"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 scales: List[float] = [0.3, 0.5, 0.7],
                 temperature: float = 1.0,
                 dropout: float = 0.1):
        """
        初始化多尺度Top-K注意力
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            scales: 不同尺度的K比例
            temperature: 温度参数
            dropout: Dropout概率
        """
        super(MultiScaleTopKAttention, self).__init__()
        
        self.scales = scales
        self.num_scales = len(scales)
        
        # 为每个尺度创建注意力层
        self.attention_layers = nn.ModuleList([
            TopKSparseAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                k_ratio=scale,
                temperature=temperature,
                dropout=dropout
            ) for scale in scales
        ])
        
        # 尺度融合层
        self.scale_fusion = nn.Sequential(
            nn.Linear(embed_dim * self.num_scales, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 尺度权重
        self.scale_weights = nn.Parameter(torch.ones(self.num_scales) / self.num_scales)
    
    def forward(self, 
                query: torch.Tensor,
                key: torch.Tensor,
                value: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                return_attention: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播
        """
        scale_outputs = []
        scale_attentions = []
        
        # 计算每个尺度的注意力
        for i, attention_layer in enumerate(self.attention_layers):
            output, attention = attention_layer(
                query, key, value, attention_mask, return_attention
            )
            scale_outputs.append(output)
            if return_attention:
                scale_attentions.append(attention)
        
        # 加权融合不同尺度的输出
        weighted_outputs = []
        scale_weights_normalized = F.softmax(self.scale_weights, dim=0)
        
        for i, output in enumerate(scale_outputs):
            weighted_outputs.append(output * scale_weights_normalized[i])
        
        # 拼接所有尺度的输出
        concatenated_output = torch.cat(scale_outputs, dim=-1)
        
        # 通过融合层
        fused_output = self.scale_fusion(concatenated_output)
        
        # 加上加权平均作为残差连接
        final_output = fused_output + sum(weighted_outputs)
        
        if return_attention:
            # 加权平均注意力权重
            avg_attention = sum(
                attention * weight for attention, weight in 
                zip(scale_attentions, scale_weights_normalized)
            )
            return final_output, avg_attention
        else:
            return final_output, None
