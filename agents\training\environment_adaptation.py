"""
环境变化检测与适应

实现环境变化检测机制和快速适应策略
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
from collections import deque
import logging
from dataclasses import dataclass
from enum import Enum
import time


class ChangeType(Enum):
    """变化类型"""
    NO_CHANGE = "no_change"         # 无变化
    GRADUAL = "gradual"             # 渐进变化
    SUDDEN = "sudden"               # 突然变化
    PERIODIC = "periodic"           # 周期性变化
    DRIFT = "drift"                 # 概念漂移


class AdaptationStrategy(Enum):
    """适应策略"""
    NONE = "none"                   # 无适应
    FINE_TUNE = "fine_tune"         # 微调
    RESET_PARTIAL = "reset_partial" # 部分重置
    RESET_FULL = "reset_full"       # 完全重置
    META_ADAPT = "meta_adapt"       # 元学习适应


@dataclass
class AdaptationConfig:
    """适应配置"""
    # 检测参数
    detection_window: int = 100         # 检测窗口大小
    change_threshold: float = 0.2       # 变化阈值
    sudden_change_threshold: float = 0.5 # 突然变化阈值
    drift_threshold: float = 0.1        # 漂移阈值
    
    # 适应参数
    adaptation_lr_multiplier: float = 2.0    # 适应学习率倍数
    fine_tune_epochs: int = 10              # 微调轮数
    reset_probability: float = 0.1          # 重置概率
    
    # 性能监控
    performance_degradation_threshold: float = 0.3  # 性能下降阈值
    adaptation_patience: int = 20           # 适应耐心
    min_adaptation_interval: int = 50       # 最小适应间隔


class EnvironmentChangeDetector:
    """环境变化检测器"""
    
    def __init__(self, 
                 config: AdaptationConfig,
                 logger: logging.Logger = None):
        """
        初始化环境变化检测器
        
        Args:
            config: 适应配置
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        
        # 性能历史
        self.performance_history = deque(maxlen=config.detection_window * 2)
        self.reward_history = deque(maxlen=config.detection_window * 2)
        self.success_rate_history = deque(maxlen=config.detection_window * 2)
        self.efficiency_history = deque(maxlen=config.detection_window * 2)
        
        # 环境特征历史
        self.env_features_history = deque(maxlen=config.detection_window)
        self.task_distribution_history = deque(maxlen=config.detection_window)
        self.collision_rate_history = deque(maxlen=config.detection_window)
        
        # 变化检测状态
        self.last_change_detection = 0
        self.change_count = 0
        self.false_positive_count = 0
        
        # 基线性能
        self.baseline_performance = None
        self.baseline_window_size = 50
    
    def update(self, 
               performance_metrics: Dict[str, float],
               env_features: Optional[Dict[str, float]] = None,
               episode: int = 0) -> Dict[str, Any]:
        """
        更新检测器并检测变化
        
        Args:
            performance_metrics: 性能指标
            env_features: 环境特征
            episode: 当前回合数
            
        Returns:
            检测结果
        """
        # 更新历史记录
        self.performance_history.append(performance_metrics.get('overall_score', 0.0))
        self.reward_history.append(performance_metrics.get('reward', 0.0))
        self.success_rate_history.append(performance_metrics.get('success_rate', 0.0))
        self.efficiency_history.append(performance_metrics.get('efficiency', 0.0))
        self.collision_rate_history.append(performance_metrics.get('collision_rate', 0.0))
        
        if env_features:
            self.env_features_history.append(env_features)
            if 'task_distribution' in env_features:
                self.task_distribution_history.append(env_features['task_distribution'])
        
        # 建立基线
        if self.baseline_performance is None and len(self.performance_history) >= self.baseline_window_size:
            self._establish_baseline()
        
        # 检测变化
        change_result = self._detect_changes(episode)
        
        return change_result
    
    def _establish_baseline(self):
        """建立性能基线"""
        recent_performance = list(self.performance_history)[-self.baseline_window_size:]
        
        self.baseline_performance = {
            'mean_performance': np.mean(recent_performance),
            'std_performance': np.std(recent_performance),
            'mean_success_rate': np.mean(list(self.success_rate_history)[-self.baseline_window_size:]),
            'mean_efficiency': np.mean(list(self.efficiency_history)[-self.baseline_window_size:]),
            'mean_collision_rate': np.mean(list(self.collision_rate_history)[-self.baseline_window_size:])
        }
        
        self.logger.info(f"建立性能基线: {self.baseline_performance}")
    
    def _detect_changes(self, episode: int) -> Dict[str, Any]:
        """检测环境变化"""
        if len(self.performance_history) < self.config.detection_window:
            return {'change_detected': False, 'change_type': ChangeType.NO_CHANGE}
        
        # 检测不同类型的变化
        sudden_change = self._detect_sudden_change()
        gradual_change = self._detect_gradual_change()
        drift_change = self._detect_concept_drift()
        periodic_change = self._detect_periodic_change()
        
        # 确定主要变化类型
        change_type = ChangeType.NO_CHANGE
        change_magnitude = 0.0
        change_detected = False
        
        if sudden_change['detected']:
            change_type = ChangeType.SUDDEN
            change_magnitude = sudden_change['magnitude']
            change_detected = True
        elif drift_change['detected']:
            change_type = ChangeType.DRIFT
            change_magnitude = drift_change['magnitude']
            change_detected = True
        elif gradual_change['detected']:
            change_type = ChangeType.GRADUAL
            change_magnitude = gradual_change['magnitude']
            change_detected = True
        elif periodic_change['detected']:
            change_type = ChangeType.PERIODIC
            change_magnitude = periodic_change['magnitude']
            change_detected = True
        
        # 更新统计
        if change_detected:
            self.change_count += 1
            self.last_change_detection = episode
            self.logger.info(f"检测到环境变化: {change_type.value}, 强度: {change_magnitude:.3f}")
        
        return {
            'change_detected': change_detected,
            'change_type': change_type,
            'change_magnitude': change_magnitude,
            'sudden_change': sudden_change,
            'gradual_change': gradual_change,
            'drift_change': drift_change,
            'periodic_change': periodic_change,
            'episode': episode
        }
    
    def _detect_sudden_change(self) -> Dict[str, Any]:
        """检测突然变化"""
        if len(self.performance_history) < 20:
            return {'detected': False, 'magnitude': 0.0}
        
        # 比较最近10个和之前10个的性能
        recent_performance = list(self.performance_history)[-10:]
        previous_performance = list(self.performance_history)[-20:-10]
        
        recent_mean = np.mean(recent_performance)
        previous_mean = np.mean(previous_performance)
        
        # 计算变化幅度
        if previous_mean != 0:
            change_ratio = abs(recent_mean - previous_mean) / abs(previous_mean)
        else:
            change_ratio = abs(recent_mean - previous_mean)
        
        detected = change_ratio > self.config.sudden_change_threshold
        
        return {
            'detected': detected,
            'magnitude': change_ratio,
            'recent_mean': recent_mean,
            'previous_mean': previous_mean
        }
    
    def _detect_gradual_change(self) -> Dict[str, Any]:
        """检测渐进变化"""
        if len(self.performance_history) < self.config.detection_window:
            return {'detected': False, 'magnitude': 0.0}
        
        # 使用线性回归检测趋势
        performance_data = list(self.performance_history)[-self.config.detection_window:]
        x = np.arange(len(performance_data))
        
        # 计算趋势斜率
        slope, _ = np.polyfit(x, performance_data, 1)
        
        # 计算变化幅度
        magnitude = abs(slope) * len(performance_data)
        
        detected = magnitude > self.config.change_threshold
        
        return {
            'detected': detected,
            'magnitude': magnitude,
            'slope': slope,
            'trend': 'increasing' if slope > 0 else 'decreasing'
        }
    
    def _detect_concept_drift(self) -> Dict[str, Any]:
        """检测概念漂移"""
        if len(self.performance_history) < self.config.detection_window * 2:
            return {'detected': False, 'magnitude': 0.0}
        
        # 比较两个窗口的分布
        window_size = self.config.detection_window
        recent_window = list(self.performance_history)[-window_size:]
        old_window = list(self.performance_history)[-window_size*2:-window_size]
        
        # 使用简化的分布比较（避免scipy依赖）
        # 计算两个窗口的均值和标准差差异
        old_mean, old_std = np.mean(old_window), np.std(old_window)
        recent_mean, recent_std = np.mean(recent_window), np.std(recent_window)

        # 计算标准化差异
        mean_diff = abs(recent_mean - old_mean) / max(old_std, 1e-6)
        std_diff = abs(recent_std - old_std) / max(old_std, 1e-6)
        ks_statistic = (mean_diff + std_diff) / 2.0
        p_value = 1.0 - ks_statistic  # 简化的p值估计
        
        detected = ks_statistic > self.config.drift_threshold
        
        return {
            'detected': detected,
            'magnitude': ks_statistic,
            'p_value': p_value,
            'recent_mean': np.mean(recent_window),
            'old_mean': np.mean(old_window)
        }
    
    def _detect_periodic_change(self) -> Dict[str, Any]:
        """检测周期性变化"""
        if len(self.performance_history) < self.config.detection_window:
            return {'detected': False, 'magnitude': 0.0}
        
        # 使用FFT检测周期性
        performance_data = list(self.performance_history)[-self.config.detection_window:]
        fft = np.fft.fft(performance_data)
        frequencies = np.fft.fftfreq(len(performance_data))
        
        # 找到主要频率
        magnitude_spectrum = np.abs(fft)
        dominant_freq_idx = np.argmax(magnitude_spectrum[1:len(magnitude_spectrum)//2]) + 1
        dominant_magnitude = magnitude_spectrum[dominant_freq_idx]
        
        # 检测是否有显著的周期性
        threshold = np.mean(magnitude_spectrum) + 2 * np.std(magnitude_spectrum)
        detected = dominant_magnitude > threshold
        
        return {
            'detected': detected,
            'magnitude': dominant_magnitude / np.mean(magnitude_spectrum),
            'dominant_frequency': frequencies[dominant_freq_idx],
            'period': 1.0 / abs(frequencies[dominant_freq_idx]) if frequencies[dominant_freq_idx] != 0 else float('inf')
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取检测器统计信息"""
        stats = {
            'change_count': self.change_count,
            'false_positive_count': self.false_positive_count,
            'last_change_detection': self.last_change_detection,
            'baseline_performance': self.baseline_performance
        }
        
        if self.performance_history:
            recent_performance = list(self.performance_history)[-20:]
            stats.update({
                'recent_avg_performance': np.mean(recent_performance),
                'recent_performance_std': np.std(recent_performance),
                'performance_trend': np.polyfit(range(len(recent_performance)), recent_performance, 1)[0]
            })
        
        return stats


class AdaptationStrategyManager:
    """适应策略管理器"""

    def __init__(self,
                 config: AdaptationConfig,
                 policy_network,
                 value_network,
                 optimizer,
                 logger: logging.Logger = None):
        """
        初始化适应策略管理器

        Args:
            config: 适应配置
            policy_network: 策略网络
            value_network: 价值网络
            optimizer: 优化器
            logger: 日志记录器
        """
        self.config = config
        self.policy_network = policy_network
        self.value_network = value_network
        self.optimizer = optimizer
        self.logger = logger or logging.getLogger(__name__)

        # 保存原始网络状态
        self.original_policy_state = None
        self.original_value_state = None
        self.original_optimizer_state = None

        # 适应历史
        self.adaptation_history = []
        self.adaptation_count = 0
        self.last_adaptation_episode = 0

        # 性能跟踪
        self.pre_adaptation_performance = None
        self.post_adaptation_performance = deque(maxlen=20)

    def save_checkpoint(self):
        """保存检查点"""
        self.original_policy_state = self.policy_network.state_dict().copy()
        self.original_value_state = self.value_network.state_dict().copy()
        self.original_optimizer_state = self.optimizer.state_dict().copy()

        self.logger.info("已保存网络检查点")

    def adapt_to_change(self,
                       change_result: Dict[str, Any],
                       current_performance: Dict[str, float],
                       episode: int) -> Dict[str, Any]:
        """
        根据检测到的变化执行适应策略

        Args:
            change_result: 变化检测结果
            current_performance: 当前性能
            episode: 当前回合数

        Returns:
            适应结果
        """
        if not change_result['change_detected']:
            return {'adapted': False, 'strategy': AdaptationStrategy.NONE}

        # 检查适应间隔
        if episode - self.last_adaptation_episode < self.config.min_adaptation_interval:
            return {'adapted': False, 'strategy': AdaptationStrategy.NONE, 'reason': 'too_soon'}

        # 选择适应策略
        strategy = self._select_adaptation_strategy(change_result, current_performance)

        # 执行适应
        adaptation_result = self._execute_adaptation(strategy, change_result, episode)

        # 记录适应
        self.adaptation_history.append({
            'episode': episode,
            'change_type': change_result['change_type'],
            'change_magnitude': change_result['change_magnitude'],
            'strategy': strategy,
            'pre_performance': current_performance.copy(),
            'adaptation_result': adaptation_result
        })

        self.adaptation_count += 1
        self.last_adaptation_episode = episode
        self.pre_adaptation_performance = current_performance.copy()

        self.logger.info(f"执行适应策略: {strategy.value} (回合 {episode})")

        return adaptation_result

    def _select_adaptation_strategy(self,
                                  change_result: Dict[str, Any],
                                  current_performance: Dict[str, float]) -> AdaptationStrategy:
        """选择适应策略"""
        change_type = change_result['change_type']
        change_magnitude = change_result['change_magnitude']

        # 基于变化类型和强度选择策略
        if change_type == ChangeType.SUDDEN:
            if change_magnitude > 0.8:
                return AdaptationStrategy.RESET_FULL
            elif change_magnitude > 0.5:
                return AdaptationStrategy.RESET_PARTIAL
            else:
                return AdaptationStrategy.FINE_TUNE

        elif change_type == ChangeType.DRIFT:
            if change_magnitude > 0.3:
                return AdaptationStrategy.RESET_PARTIAL
            else:
                return AdaptationStrategy.FINE_TUNE

        elif change_type == ChangeType.GRADUAL:
            return AdaptationStrategy.FINE_TUNE

        elif change_type == ChangeType.PERIODIC:
            return AdaptationStrategy.META_ADAPT

        else:
            return AdaptationStrategy.NONE

    def _execute_adaptation(self,
                          strategy: AdaptationStrategy,
                          change_result: Dict[str, Any],
                          episode: int) -> Dict[str, Any]:
        """执行适应策略"""
        result = {
            'adapted': True,
            'strategy': strategy,
            'episode': episode,
            'changes_made': []
        }

        if strategy == AdaptationStrategy.FINE_TUNE:
            result.update(self._fine_tune_adaptation())

        elif strategy == AdaptationStrategy.RESET_PARTIAL:
            result.update(self._partial_reset_adaptation())

        elif strategy == AdaptationStrategy.RESET_FULL:
            result.update(self._full_reset_adaptation())

        elif strategy == AdaptationStrategy.META_ADAPT:
            result.update(self._meta_adaptation())

        else:
            result['adapted'] = False

        return result

    def _fine_tune_adaptation(self) -> Dict[str, Any]:
        """微调适应"""
        changes = []

        # 提高学习率
        original_lr = self.optimizer.param_groups[0]['lr']
        new_lr = original_lr * self.config.adaptation_lr_multiplier

        for param_group in self.optimizer.param_groups:
            param_group['lr'] = new_lr

        changes.append(f"学习率从 {original_lr:.6f} 调整到 {new_lr:.6f}")

        # 重置优化器动量
        self.optimizer.state = {}
        changes.append("重置优化器状态")

        return {
            'changes_made': changes,
            'new_lr': new_lr,
            'adaptation_type': 'fine_tune'
        }

    def _partial_reset_adaptation(self) -> Dict[str, Any]:
        """部分重置适应"""
        changes = []

        # 重置策略网络的最后几层
        if hasattr(self.policy_network, 'action_head'):
            self.policy_network.action_head.apply(self._weight_reset)
            changes.append("重置策略网络动作头")

        # 重置价值网络的最后几层
        if hasattr(self.value_network, 'value_head'):
            self.value_network.value_head.apply(self._weight_reset)
            changes.append("重置价值网络价值头")

        # 调整学习率
        original_lr = self.optimizer.param_groups[0]['lr']
        new_lr = original_lr * self.config.adaptation_lr_multiplier

        for param_group in self.optimizer.param_groups:
            param_group['lr'] = new_lr

        changes.append(f"学习率调整到 {new_lr:.6f}")

        # 重置优化器
        self.optimizer.state = {}
        changes.append("重置优化器状态")

        return {
            'changes_made': changes,
            'new_lr': new_lr,
            'adaptation_type': 'partial_reset'
        }

    def _full_reset_adaptation(self) -> Dict[str, Any]:
        """完全重置适应"""
        changes = []

        # 重置整个网络
        self.policy_network.apply(self._weight_reset)
        self.value_network.apply(self._weight_reset)
        changes.append("重置所有网络权重")

        # 重置优化器
        self.optimizer.state = {}
        changes.append("重置优化器状态")

        # 恢复原始学习率
        if self.original_optimizer_state:
            original_lr = self.original_optimizer_state['param_groups'][0]['lr']
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = original_lr
            changes.append(f"恢复原始学习率 {original_lr:.6f}")

        return {
            'changes_made': changes,
            'adaptation_type': 'full_reset'
        }

    def _meta_adaptation(self) -> Dict[str, Any]:
        """元学习适应"""
        changes = []

        # 实现简单的元学习适应
        # 这里可以集成更复杂的元学习算法

        # 调整学习率和动量
        for param_group in self.optimizer.param_groups:
            param_group['lr'] *= 1.5
            if 'momentum' in param_group:
                param_group['momentum'] = 0.9

        changes.append("调整学习率和动量参数")

        # 添加噪声到网络权重以增加探索
        with torch.no_grad():
            for param in self.policy_network.parameters():
                noise = torch.randn_like(param) * 0.01
                param.add_(noise)

        changes.append("添加探索噪声到策略网络")

        return {
            'changes_made': changes,
            'adaptation_type': 'meta_adapt'
        }

    def _weight_reset(self, m):
        """重置权重"""
        if isinstance(m, (nn.Linear, nn.Conv2d)):
            m.reset_parameters()

    def evaluate_adaptation_success(self,
                                  post_adaptation_performance: Dict[str, float]) -> Dict[str, Any]:
        """评估适应成功性"""
        if self.pre_adaptation_performance is None:
            return {'success': False, 'reason': 'no_baseline'}

        self.post_adaptation_performance.append(post_adaptation_performance)

        # 比较适应前后性能
        pre_score = self.pre_adaptation_performance.get('overall_score', 0.0)
        post_score = post_adaptation_performance.get('overall_score', 0.0)

        improvement = post_score - pre_score
        improvement_ratio = improvement / max(abs(pre_score), 1e-6)

        # 判断成功标准
        success = improvement_ratio > -self.config.performance_degradation_threshold

        result = {
            'success': success,
            'improvement': improvement,
            'improvement_ratio': improvement_ratio,
            'pre_performance': pre_score,
            'post_performance': post_score
        }

        if success:
            self.logger.info(f"适应成功: 性能改善 {improvement:.3f} ({improvement_ratio:.1%})")
        else:
            self.logger.warning(f"适应失败: 性能下降 {improvement:.3f} ({improvement_ratio:.1%})")

        return result

    def get_adaptation_statistics(self) -> Dict[str, Any]:
        """获取适应统计信息"""
        stats = {
            'adaptation_count': self.adaptation_count,
            'last_adaptation_episode': self.last_adaptation_episode,
            'adaptation_history_length': len(self.adaptation_history)
        }

        if self.adaptation_history:
            # 策略使用统计
            strategies = [a['strategy'] for a in self.adaptation_history]
            strategy_counts = {s.value: strategies.count(s) for s in AdaptationStrategy}
            stats['strategy_usage'] = strategy_counts

            # 最近适应性能
            if self.post_adaptation_performance:
                recent_performance = list(self.post_adaptation_performance)
                stats['recent_post_adaptation_performance'] = {
                    'mean': np.mean([p.get('overall_score', 0.0) for p in recent_performance]),
                    'std': np.std([p.get('overall_score', 0.0) for p in recent_performance])
                }

        return stats
