"""
任务管理系统

实现任务生成、状态管理、分配机制和完成检测
"""

import numpy as np
import random
from typing import List, Dict, Optional, Tuple, Set
from enum import Enum
from dataclasses import dataclass, field
from utils.data_structures import TaskState
from utils.math_utils import manhattan_distance


class TaskType(Enum):
    """任务类型"""
    PICKUP = "pickup"      # 拾取任务
    DELIVERY = "delivery"  # 配送任务
    TRANSPORT = "transport"  # 运输任务（拾取+配送）


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class Task:
    """任务数据结构"""
    id: int
    task_type: TaskType
    pickup_position: Tuple[int, int]
    dropoff_position: Tuple[int, int]
    weight: int
    priority: TaskPriority = TaskPriority.NORMAL
    deadline: Optional[int] = None
    creation_time: int = 0
    assigned_agv_id: Optional[int] = None
    status: str = "unassigned"  # unassigned, assigned, in_progress, completed, failed
    pickup_completed: bool = False
    dropoff_completed: bool = False
    completion_time: Optional[int] = None
    
    @property
    def is_available(self) -> bool:
        """任务是否可分配"""
        return self.status == "unassigned"
    
    @property
    def is_assigned(self) -> bool:
        """任务是否已分配"""
        return self.status == "assigned"
    
    @property
    def is_in_progress(self) -> bool:
        """任务是否进行中"""
        return self.status == "in_progress"
    
    @property
    def is_completed(self) -> bool:
        """任务是否已完成"""
        return self.status == "completed"
    
    @property
    def is_failed(self) -> bool:
        """任务是否失败"""
        return self.status == "failed"
    
    def assign_to_agv(self, agv_id: int):
        """分配给AGV"""
        self.assigned_agv_id = agv_id
        self.status = "assigned"
    
    def start_execution(self):
        """开始执行"""
        self.status = "in_progress"
    
    def complete_pickup(self):
        """完成拾取"""
        self.pickup_completed = True
    
    def complete_dropoff(self, completion_time: int):
        """完成放置"""
        self.dropoff_completed = True
        self.completion_time = completion_time
        self.status = "completed"
    
    def fail_task(self, failure_time: int):
        """任务失败"""
        self.completion_time = failure_time
        self.status = "failed"
    
    def get_distance(self) -> int:
        """获取任务距离（拾取到放置）"""
        return manhattan_distance(self.pickup_position, self.dropoff_position)
    
    def get_priority_value(self) -> float:
        """获取优先级数值"""
        return self.priority.value
    
    def to_state_vector(self) -> np.ndarray:
        """转换为状态向量"""
        status_encoding = {
            "unassigned": 0, "assigned": 1, 
            "in_progress": 2, "completed": 3, "failed": 4
        }
        
        return np.array([
            self.pickup_position[0], self.pickup_position[1],  # 拾取位置
            self.dropoff_position[0], self.dropoff_position[1],  # 放置位置
            self.weight,  # 重量
            self.priority.value,  # 优先级
            self.deadline if self.deadline is not None else -1,  # 截止时间
            status_encoding[self.status],  # 状态编码
            self.assigned_agv_id if self.assigned_agv_id is not None else -1,  # 分配的AGV
            1 if self.pickup_completed else 0,  # 拾取完成
            1 if self.dropoff_completed else 0,  # 放置完成
        ], dtype=np.float32)


class TaskManager:
    """任务管理器"""
    
    def __init__(self, 
                 max_tasks: int = 16,
                 task_weights: List[int] = None,
                 priority_distribution: Dict[TaskPriority, float] = None):
        """
        初始化任务管理器
        
        Args:
            max_tasks: 最大任务数量
            task_weights: 可能的任务重量列表
            priority_distribution: 优先级分布
        """
        self.max_tasks = max_tasks
        self.task_weights = task_weights or [5, 10]
        self.priority_distribution = priority_distribution or {
            TaskPriority.LOW: 0.2,
            TaskPriority.NORMAL: 0.5,
            TaskPriority.HIGH: 0.2,
            TaskPriority.URGENT: 0.1
        }
        
        # 任务存储
        self.tasks: Dict[int, Task] = {}
        self.next_task_id = 0
        self.current_time = 0
        
        # 任务队列
        self.unassigned_tasks: Set[int] = set()
        self.assigned_tasks: Set[int] = set()
        self.in_progress_tasks: Set[int] = set()
        self.completed_tasks: Set[int] = set()
        self.failed_tasks: Set[int] = set()
        
        # 统计信息
        self.total_tasks_created = 0
        self.total_tasks_completed = 0
        self.total_tasks_failed = 0
        self.total_completion_time = 0
        self.total_wait_time = 0
    
    def create_task(self, 
                   pickup_pos: Tuple[int, int],
                   dropoff_pos: Tuple[int, int],
                   weight: Optional[int] = None,
                   priority: Optional[TaskPriority] = None,
                   deadline: Optional[int] = None) -> int:
        """
        创建新任务
        
        Args:
            pickup_pos: 拾取位置
            dropoff_pos: 放置位置
            weight: 任务重量
            priority: 任务优先级
            deadline: 截止时间
            
        Returns:
            任务ID
        """
        if len(self.tasks) >= self.max_tasks:
            raise ValueError(f"任务数量已达到最大值 {self.max_tasks}")
        
        task_id = self.next_task_id
        self.next_task_id += 1
        
        # 随机生成重量和优先级（如果未指定）
        if weight is None:
            weight = random.choice(self.task_weights)
        
        if priority is None:
            priority = self._random_priority()
        
        # 创建任务
        task = Task(
            id=task_id,
            task_type=TaskType.TRANSPORT,
            pickup_position=pickup_pos,
            dropoff_position=dropoff_pos,
            weight=weight,
            priority=priority,
            deadline=deadline,
            creation_time=self.current_time
        )
        
        self.tasks[task_id] = task
        self.unassigned_tasks.add(task_id)
        self.total_tasks_created += 1
        
        return task_id
    
    def _random_priority(self) -> TaskPriority:
        """随机生成优先级"""
        priorities = list(self.priority_distribution.keys())
        weights = list(self.priority_distribution.values())
        return random.choices(priorities, weights=weights)[0]
    
    def generate_random_tasks(self, 
                            num_tasks: int,
                            pickup_positions: List[Tuple[int, int]],
                            dropoff_positions: List[Tuple[int, int]]) -> List[int]:
        """
        生成随机任务
        
        Args:
            num_tasks: 任务数量
            pickup_positions: 可选拾取位置列表
            dropoff_positions: 可选放置位置列表
            
        Returns:
            生成的任务ID列表
        """
        task_ids = []
        
        for _ in range(num_tasks):
            if len(self.tasks) >= self.max_tasks:
                break
            
            pickup_pos = random.choice(pickup_positions)
            dropoff_pos = random.choice(dropoff_positions)
            
            # 确保拾取和放置位置不同
            while dropoff_pos == pickup_pos:
                dropoff_pos = random.choice(dropoff_positions)
            
            task_id = self.create_task(pickup_pos, dropoff_pos)
            task_ids.append(task_id)
        
        return task_ids
    
    def assign_task(self, task_id: int, agv_id: int) -> bool:
        """
        分配任务给AGV
        
        Args:
            task_id: 任务ID
            agv_id: AGV ID
            
        Returns:
            是否分配成功
        """
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        if not task.is_available:
            return False
        
        task.assign_to_agv(agv_id)
        self.unassigned_tasks.discard(task_id)
        self.assigned_tasks.add(task_id)
        
        return True
    
    def start_task(self, task_id: int) -> bool:
        """
        开始执行任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否开始成功
        """
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        if not task.is_assigned:
            return False
        
        task.start_execution()
        self.assigned_tasks.discard(task_id)
        self.in_progress_tasks.add(task_id)
        
        return True
    
    def complete_pickup(self, task_id: int) -> bool:
        """
        完成任务拾取
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否完成成功
        """
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        if not task.is_in_progress:
            return False
        
        task.complete_pickup()
        return True
    
    def complete_task(self, task_id: int) -> bool:
        """
        完成任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否完成成功
        """
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        if not task.is_in_progress:
            return False
        
        task.complete_dropoff(self.current_time)
        self.in_progress_tasks.discard(task_id)
        self.completed_tasks.add(task_id)
        
        # 更新统计信息
        self.total_tasks_completed += 1
        completion_time = self.current_time - task.creation_time
        self.total_completion_time += completion_time
        
        return True
    
    def fail_task(self, task_id: int, reason: str = "timeout") -> bool:
        """
        任务失败
        
        Args:
            task_id: 任务ID
            reason: 失败原因
            
        Returns:
            是否处理成功
        """
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        task.fail_task(self.current_time)
        
        # 从相应队列中移除
        self.unassigned_tasks.discard(task_id)
        self.assigned_tasks.discard(task_id)
        self.in_progress_tasks.discard(task_id)
        self.failed_tasks.add(task_id)
        
        self.total_tasks_failed += 1
        
        return True
    
    def get_task(self, task_id: int) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def get_unassigned_tasks(self) -> List[Task]:
        """获取未分配任务列表"""
        return [self.tasks[tid] for tid in self.unassigned_tasks]
    
    def get_assigned_tasks(self) -> List[Task]:
        """获取已分配任务列表"""
        return [self.tasks[tid] for tid in self.assigned_tasks]
    
    def get_tasks_by_agv(self, agv_id: int) -> List[Task]:
        """获取指定AGV的任务"""
        return [task for task in self.tasks.values() 
                if task.assigned_agv_id == agv_id and not task.is_completed]
    
    def get_tasks_by_priority(self, priority: TaskPriority) -> List[Task]:
        """获取指定优先级的任务"""
        return [task for task in self.tasks.values() 
                if task.priority == priority and task.is_available]
    
    def get_urgent_tasks(self) -> List[Task]:
        """获取紧急任务"""
        current_time = self.current_time
        urgent_tasks = []
        
        for task in self.tasks.values():
            if task.is_available and task.deadline is not None:
                time_remaining = task.deadline - current_time
                if time_remaining <= 10:  # 剩余时间少于10步视为紧急
                    urgent_tasks.append(task)
        
        return sorted(urgent_tasks, key=lambda t: t.deadline)
    
    def check_deadlines(self) -> List[int]:
        """检查超时任务"""
        expired_tasks = []
        
        for task_id, task in self.tasks.items():
            if (task.deadline is not None and 
                self.current_time > task.deadline and 
                not task.is_completed):
                expired_tasks.append(task_id)
        
        # 自动标记超时任务为失败
        for task_id in expired_tasks:
            self.fail_task(task_id, "deadline_exceeded")
        
        return expired_tasks
    
    def get_task_statistics(self) -> Dict[str, float]:
        """获取任务统计信息"""
        total_tasks = len(self.tasks)
        if total_tasks == 0:
            return {}
        
        completion_rate = self.total_tasks_completed / total_tasks
        failure_rate = self.total_tasks_failed / total_tasks
        avg_completion_time = (self.total_completion_time / self.total_tasks_completed 
                              if self.total_tasks_completed > 0 else 0)
        
        return {
            'total_tasks': total_tasks,
            'completed_tasks': self.total_tasks_completed,
            'failed_tasks': self.total_tasks_failed,
            'unassigned_tasks': len(self.unassigned_tasks),
            'assigned_tasks': len(self.assigned_tasks),
            'in_progress_tasks': len(self.in_progress_tasks),
            'completion_rate': completion_rate,
            'failure_rate': failure_rate,
            'average_completion_time': avg_completion_time
        }
    
    def step(self):
        """时间步进"""
        self.current_time += 1
        
        # 检查超时任务
        self.check_deadlines()
    
    def reset(self):
        """重置任务管理器"""
        self.tasks.clear()
        self.next_task_id = 0
        self.current_time = 0
        
        self.unassigned_tasks.clear()
        self.assigned_tasks.clear()
        self.in_progress_tasks.clear()
        self.completed_tasks.clear()
        self.failed_tasks.clear()
        
        self.total_tasks_created = 0
        self.total_tasks_completed = 0
        self.total_tasks_failed = 0
        self.total_completion_time = 0
        self.total_wait_time = 0
    
    def get_all_tasks_state(self) -> np.ndarray:
        """获取所有任务的状态矩阵"""
        if not self.tasks:
            return np.zeros((self.max_tasks, 11), dtype=np.float32)
        
        task_states = []
        task_ids = sorted(self.tasks.keys())
        
        for task_id in task_ids:
            task_states.append(self.tasks[task_id].to_state_vector())
        
        # 填充到最大任务数量
        while len(task_states) < self.max_tasks:
            task_states.append(np.zeros(11, dtype=np.float32))
        
        return np.array(task_states[:self.max_tasks])
    
    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_task_statistics()
        return (f"TaskManager: {stats.get('total_tasks', 0)} tasks, "
                f"Completed: {stats.get('completed_tasks', 0)}, "
                f"Failed: {stats.get('failed_tasks', 0)}, "
                f"Rate: {stats.get('completion_rate', 0):.2f}")
    
    def __repr__(self) -> str:
        return self.__str__()
