"""
日志和监控系统

提供统一的日志记录和TensorBoard集成
"""

import os
import logging
import time
from typing import Dict, Any, Optional, Union
from datetime import datetime
import numpy as np
import torch
from torch.utils.tensorboard import SummaryWriter


class Logger:
    """统一的日志管理器"""
    
    def __init__(self, 
                 log_dir: str = "logs",
                 experiment_name: str = "experiment",
                 level: int = logging.INFO,
                 console_output: bool = True):
        """
        初始化日志器
        
        Args:
            log_dir: 日志目录
            experiment_name: 实验名称
            level: 日志级别
            console_output: 是否输出到控制台
        """
        self.log_dir = log_dir
        self.experiment_name = experiment_name
        self.start_time = time.time()
        
        # 创建日志目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = os.path.join(log_dir, f"{experiment_name}_{timestamp}")
        os.makedirs(self.run_dir, exist_ok=True)
        
        # 设置文件日志
        self.logger = logging.getLogger(experiment_name)
        self.logger.setLevel(level)
        
        # 清除已有的处理器
        self.logger.handlers.clear()
        
        # 文件处理器
        file_handler = logging.FileHandler(
            os.path.join(self.run_dir, "experiment.log"),
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        
        # 控制台处理器
        if console_output:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(level)
            
            # 设置格式
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # TensorBoard写入器
        self.tb_writer = SummaryWriter(
            log_dir=os.path.join(self.run_dir, "tensorboard")
        )
        
        # 性能指标缓存
        self.metrics_buffer = {}
        self.step_count = 0
        
        self.info(f"日志系统初始化完成，日志目录: {self.run_dir}")
    
    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)
    
    def log_scalar(self, tag: str, value: Union[float, int], step: Optional[int] = None):
        """记录标量值到TensorBoard"""
        if step is None:
            step = self.step_count
        
        self.tb_writer.add_scalar(tag, value, step)
        
        # 缓存到指标缓冲区
        if tag not in self.metrics_buffer:
            self.metrics_buffer[tag] = []
        self.metrics_buffer[tag].append((step, value))
    
    def log_scalars(self, tag_scalar_dict: Dict[str, Union[float, int]], step: Optional[int] = None):
        """批量记录标量值"""
        if step is None:
            step = self.step_count
        
        for tag, value in tag_scalar_dict.items():
            self.log_scalar(tag, value, step)
    
    def log_histogram(self, tag: str, values: Union[np.ndarray, torch.Tensor], step: Optional[int] = None):
        """记录直方图到TensorBoard"""
        if step is None:
            step = self.step_count
        
        if isinstance(values, torch.Tensor):
            values = values.detach().cpu().numpy()
        
        self.tb_writer.add_histogram(tag, values, step)
    
    def log_image(self, tag: str, img_tensor: torch.Tensor, step: Optional[int] = None):
        """记录图像到TensorBoard"""
        if step is None:
            step = self.step_count
        
        self.tb_writer.add_image(tag, img_tensor, step)
    
    def log_attention_weights(self, attention_weights: torch.Tensor, 
                            layer_name: str, step: Optional[int] = None):
        """记录注意力权重"""
        if step is None:
            step = self.step_count
        
        # 记录注意力权重的统计信息
        weights_np = attention_weights.detach().cpu().numpy()
        
        self.log_scalar(f"attention/{layer_name}/mean", np.mean(weights_np), step)
        self.log_scalar(f"attention/{layer_name}/std", np.std(weights_np), step)
        self.log_scalar(f"attention/{layer_name}/max", np.max(weights_np), step)
        self.log_scalar(f"attention/{layer_name}/min", np.min(weights_np), step)
        
        # 记录注意力权重分布
        self.log_histogram(f"attention/{layer_name}/weights", weights_np, step)
        
        # 计算注意力熵（多样性指标）
        entropy = -np.sum(weights_np * np.log(weights_np + 1e-8), axis=-1)
        self.log_scalar(f"attention/{layer_name}/entropy", np.mean(entropy), step)
    
    def log_training_metrics(self, metrics: Dict[str, float], step: Optional[int] = None):
        """记录训练指标"""
        if step is None:
            step = self.step_count
        
        for metric_name, value in metrics.items():
            self.log_scalar(f"training/{metric_name}", value, step)
        
        # 记录到日志文件
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.info(f"Step {step} - Training metrics: {metrics_str}")
    
    def log_evaluation_metrics(self, metrics: Dict[str, float], step: Optional[int] = None):
        """记录评估指标"""
        if step is None:
            step = self.step_count
        
        for metric_name, value in metrics.items():
            self.log_scalar(f"evaluation/{metric_name}", value, step)
        
        # 记录到日志文件
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.info(f"Step {step} - Evaluation metrics: {metrics_str}")
    
    def log_environment_metrics(self, metrics: Dict[str, float], step: Optional[int] = None):
        """记录环境指标"""
        if step is None:
            step = self.step_count
        
        for metric_name, value in metrics.items():
            self.log_scalar(f"environment/{metric_name}", value, step)
    
    def log_hyperparameters(self, hparams: Dict[str, Any], metrics: Dict[str, float]):
        """记录超参数和对应的性能指标"""
        self.tb_writer.add_hparams(hparams, metrics)
    
    def log_model_graph(self, model: torch.nn.Module, input_tensor: torch.Tensor):
        """记录模型计算图"""
        self.tb_writer.add_graph(model, input_tensor)
    
    def save_checkpoint_info(self, checkpoint_path: str, metrics: Dict[str, float]):
        """保存检查点信息"""
        checkpoint_info = {
            'path': checkpoint_path,
            'timestamp': datetime.now().isoformat(),
            'step': self.step_count,
            'metrics': metrics
        }
        
        info_str = f"检查点保存: {checkpoint_path}, 指标: {metrics}"
        self.info(info_str)
        
        return checkpoint_info
    
    def get_elapsed_time(self) -> float:
        """获取运行时间"""
        return time.time() - self.start_time
    
    def log_system_info(self):
        """记录系统信息"""
        import psutil
        import platform
        
        system_info = {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total / (1024**3),  # GB
            "torch_version": torch.__version__,
            "cuda_available": torch.cuda.is_available(),
        }
        
        if torch.cuda.is_available():
            system_info["cuda_version"] = torch.version.cuda
            system_info["gpu_count"] = torch.cuda.device_count()
            system_info["gpu_name"] = torch.cuda.get_device_name(0)
        
        self.info("系统信息:")
        for key, value in system_info.items():
            self.info(f"  {key}: {value}")
    
    def step(self):
        """增加步数计数器"""
        self.step_count += 1
    
    def close(self):
        """关闭日志器"""
        self.tb_writer.close()
        
        # 记录总运行时间
        total_time = self.get_elapsed_time()
        self.info(f"实验结束，总运行时间: {total_time:.2f}秒")
        
        # 关闭日志处理器
        for handler in self.logger.handlers:
            handler.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self, window_size: int = 100):
        """
        初始化指标跟踪器
        
        Args:
            window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.metrics = {}
    
    def update(self, **kwargs):
        """更新指标"""
        for key, value in kwargs.items():
            if key not in self.metrics:
                self.metrics[key] = []
            
            self.metrics[key].append(value)
            
            # 保持窗口大小
            if len(self.metrics[key]) > self.window_size:
                self.metrics[key].pop(0)
    
    def get_mean(self, key: str) -> float:
        """获取指标均值"""
        if key in self.metrics and self.metrics[key]:
            return np.mean(self.metrics[key])
        return 0.0
    
    def get_std(self, key: str) -> float:
        """获取指标标准差"""
        if key in self.metrics and len(self.metrics[key]) > 1:
            return np.std(self.metrics[key])
        return 0.0
    
    def get_latest(self, key: str) -> float:
        """获取最新值"""
        if key in self.metrics and self.metrics[key]:
            return self.metrics[key][-1]
        return 0.0
    
    def get_summary(self) -> Dict[str, Dict[str, float]]:
        """获取所有指标的摘要"""
        summary = {}
        for key in self.metrics:
            summary[key] = {
                'mean': self.get_mean(key),
                'std': self.get_std(key),
                'latest': self.get_latest(key),
                'count': len(self.metrics[key])
            }
        return summary
