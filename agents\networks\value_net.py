"""
价值网络架构

实现中心化价值网络，支持全局状态输入和价值估计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List


class ValueNetwork(nn.Module):
    """MAPPO中心化价值网络"""
    
    def __init__(self, 
                 agv_state_dim: int = 10,
                 task_state_dim: int = 11,
                 global_state_shape: Tuple[int, int] = (10, 26),
                 num_agents: int = 4,
                 hidden_dims: List[int] = None,
                 activation: str = "relu",
                 layer_norm: bool = True,
                 dropout: float = 0.1):
        """
        初始化价值网络
        
        Args:
            agv_state_dim: AGV状态维度
            task_state_dim: 任务状态维度
            global_state_shape: 全局状态形状 (height, width)
            num_agents: 智能体数量
            hidden_dims: 隐藏层维度列表
            activation: 激活函数类型
            layer_norm: 是否使用层归一化
            dropout: Dropout概率
        """
        super(ValueNetwork, self).__init__()
        
        self.agv_state_dim = agv_state_dim
        self.task_state_dim = task_state_dim
        self.global_state_shape = global_state_shape
        self.num_agents = num_agents
        self.hidden_dims = hidden_dims or [512, 256]
        self.layer_norm = layer_norm
        self.dropout = dropout
        
        # 激活函数
        if activation == "relu":
            self.activation = nn.ReLU()
        elif activation == "tanh":
            self.activation = nn.Tanh()
        elif activation == "gelu":
            self.activation = nn.GELU()
        else:
            self.activation = nn.ReLU()
        
        # 单个AGV状态编码器
        self.agv_encoder = nn.Sequential(
            nn.Linear(agv_state_dim, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(64, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 任务状态编码器
        self.task_encoder = nn.Sequential(
            nn.Linear(task_state_dim, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(64, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 全局状态编码器（CNN）
        self.global_encoder = nn.Sequential(
            nn.Conv2d(1, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32) if layer_norm else nn.Identity(),
            self.activation,
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.AdaptiveAvgPool2d((4, 4)),
            nn.Flatten(),
            nn.Linear(64 * 4 * 4, 128),
            nn.LayerNorm(128) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 多智能体状态聚合器
        self.multi_agv_aggregator = nn.Sequential(
            nn.Linear(64, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout)
        )
        
        # 任务状态聚合器
        self.task_aggregator = nn.Sequential(
            nn.Linear(64, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout)
        )
        
        # 全局注意力机制（用于聚合多智能体信息）
        self.global_attention = nn.MultiheadAttention(
            embed_dim=64,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 特征融合层
        # 聚合后的AGV特征(64) + 聚合后的任务特征(64) + 全局特征(128) = 256
        fusion_input_dim = 64 + 64 + 128
        
        self.feature_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, self.hidden_dims[0]),
            nn.LayerNorm(self.hidden_dims[0]) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout)
        )
        
        # 价值头部网络
        value_layers = []
        input_dim = self.hidden_dims[0]
        
        for hidden_dim in self.hidden_dims[1:]:
            value_layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.LayerNorm(hidden_dim) if layer_norm else nn.Identity(),
                self.activation,
                nn.Dropout(dropout)
            ])
            input_dim = hidden_dim
        
        self.value_head = nn.Sequential(*value_layers)
        
        # 价值输出层
        self.value_output = nn.Linear(input_dim, 1)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Conv2d):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
        
        # 价值输出层使用较小的初始化
        nn.init.orthogonal_(self.value_output.weight, gain=1.0)
        nn.init.constant_(self.value_output.bias, 0.0)
    
    def forward(self, 
                agv_states: torch.Tensor,
                task_states: torch.Tensor,
                global_state: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            agv_states: 所有AGV状态 [batch_size, num_agents, agv_state_dim]
            task_states: 任务状态 [batch_size, num_tasks, task_state_dim]
            global_state: 全局状态 [batch_size, height, width]
            
        Returns:
            values: 状态价值 [batch_size, 1]
        """
        batch_size = agv_states.size(0)
        num_agents = agv_states.size(1)
        num_tasks = task_states.size(1)
        
        # 编码所有AGV状态
        agv_states_flat = agv_states.view(batch_size * num_agents, -1)
        agv_features = self.agv_encoder(agv_states_flat)  # [batch_size * num_agents, 64]
        agv_features = agv_features.view(batch_size, num_agents, 64)  # [batch_size, num_agents, 64]
        
        # 使用注意力机制聚合AGV特征
        agv_features_agg, _ = self.global_attention(
            agv_features, agv_features, agv_features
        )  # [batch_size, num_agents, 64]
        
        # 平均池化得到全局AGV特征
        agv_features_global = torch.mean(agv_features_agg, dim=1)  # [batch_size, 64]
        agv_features_global = self.multi_agv_aggregator(agv_features_global)
        
        # 编码任务状态
        task_states_flat = task_states.view(batch_size * num_tasks, -1)
        task_features = self.task_encoder(task_states_flat)  # [batch_size * num_tasks, 64]
        task_features = task_features.view(batch_size, num_tasks, 64)  # [batch_size, num_tasks, 64]
        
        # 聚合任务特征
        task_features_global = torch.mean(task_features, dim=1)  # [batch_size, 64]
        task_features_global = self.task_aggregator(task_features_global)
        
        # 编码全局状态
        global_state = global_state.unsqueeze(1)  # [batch_size, 1, height, width]
        global_features = self.global_encoder(global_state)  # [batch_size, 128]
        
        # 特征融合
        fused_features = torch.cat([
            agv_features_global, 
            task_features_global, 
            global_features
        ], dim=1)  # [batch_size, 256]
        
        fused_features = self.feature_fusion(fused_features)  # [batch_size, hidden_dims[0]]
        
        # 价值头部
        value_features = self.value_head(fused_features)  # [batch_size, hidden_dims[-1]]
        
        # 价值输出
        values = self.value_output(value_features)  # [batch_size, 1]
        
        return values
    
    def get_feature_dim(self) -> int:
        """获取特征维度"""
        return self.hidden_dims[-1]


class IndividualValueNetwork(nn.Module):
    """个体价值网络（用于对比实验）"""
    
    def __init__(self, 
                 agv_state_dim: int = 10,
                 task_state_dim: int = 11,
                 global_state_shape: Tuple[int, int] = (10, 26),
                 hidden_dims: List[int] = None,
                 activation: str = "relu",
                 layer_norm: bool = True,
                 dropout: float = 0.1):
        """
        初始化个体价值网络
        
        Args:
            agv_state_dim: AGV状态维度
            task_state_dim: 任务状态维度
            global_state_shape: 全局状态形状
            hidden_dims: 隐藏层维度列表
            activation: 激活函数类型
            layer_norm: 是否使用层归一化
            dropout: Dropout概率
        """
        super(IndividualValueNetwork, self).__init__()
        
        self.agv_state_dim = agv_state_dim
        self.task_state_dim = task_state_dim
        self.global_state_shape = global_state_shape
        self.hidden_dims = hidden_dims or [256, 128]
        self.layer_norm = layer_norm
        self.dropout = dropout
        
        # 激活函数
        if activation == "relu":
            self.activation = nn.ReLU()
        elif activation == "tanh":
            self.activation = nn.Tanh()
        elif activation == "gelu":
            self.activation = nn.GELU()
        else:
            self.activation = nn.ReLU()
        
        # AGV状态编码器
        self.agv_encoder = nn.Sequential(
            nn.Linear(agv_state_dim, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(64, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 任务状态编码器
        self.task_encoder = nn.Sequential(
            nn.Linear(task_state_dim, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(64, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 特征融合层
        fusion_input_dim = 64 + 64  # AGV特征 + 任务特征
        
        self.feature_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, self.hidden_dims[0]),
            nn.LayerNorm(self.hidden_dims[0]) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout)
        )
        
        # 价值头部网络
        value_layers = []
        input_dim = self.hidden_dims[0]
        
        for hidden_dim in self.hidden_dims[1:]:
            value_layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.LayerNorm(hidden_dim) if layer_norm else nn.Identity(),
                self.activation,
                nn.Dropout(dropout)
            ])
            input_dim = hidden_dim
        
        self.value_head = nn.Sequential(*value_layers)
        
        # 价值输出层
        self.value_output = nn.Linear(input_dim, 1)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0.0)
        
        # 价值输出层使用较小的初始化
        nn.init.orthogonal_(self.value_output.weight, gain=1.0)
        nn.init.constant_(self.value_output.bias, 0.0)
    
    def forward(self, 
                agv_state: torch.Tensor,
                task_states: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            agv_state: 单个AGV状态 [batch_size, agv_state_dim]
            task_states: 任务状态 [batch_size, num_tasks, task_state_dim]
            
        Returns:
            values: 状态价值 [batch_size, 1]
        """
        batch_size = agv_state.size(0)
        num_tasks = task_states.size(1)
        
        # 编码AGV状态
        agv_features = self.agv_encoder(agv_state)  # [batch_size, 64]
        
        # 编码任务状态
        task_states_flat = task_states.view(batch_size * num_tasks, -1)
        task_features = self.task_encoder(task_states_flat)  # [batch_size * num_tasks, 64]
        task_features = task_features.view(batch_size, num_tasks, 64)  # [batch_size, num_tasks, 64]
        
        # 聚合任务特征
        task_features_global = torch.mean(task_features, dim=1)  # [batch_size, 64]
        
        # 特征融合
        fused_features = torch.cat([agv_features, task_features_global], dim=1)
        fused_features = self.feature_fusion(fused_features)
        
        # 价值头部
        value_features = self.value_head(fused_features)
        
        # 价值输出
        values = self.value_output(value_features)
        
        return values


class MultiAgentValueNetwork(nn.Module):
    """多智能体价值网络包装器"""
    
    def __init__(self, 
                 num_agents: int,
                 value_config: Dict,
                 centralized: bool = True):
        """
        初始化多智能体价值网络
        
        Args:
            num_agents: 智能体数量
            value_config: 价值网络配置
            centralized: 是否使用中心化价值网络
        """
        super(MultiAgentValueNetwork, self).__init__()
        
        self.num_agents = num_agents
        self.centralized = centralized
        
        if centralized:
            # 中心化价值网络
            self.value_net = ValueNetwork(num_agents=num_agents, **value_config)
        else:
            # 分布式价值网络
            self.value_nets = nn.ModuleList([
                IndividualValueNetwork(**value_config) for _ in range(num_agents)
            ])
    
    def forward(self, 
                agv_states: torch.Tensor,
                task_states: torch.Tensor,
                global_state: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        多智能体价值估计
        
        Args:
            agv_states: AGV状态 [batch_size, num_agents, agv_state_dim]
            task_states: 任务状态 [batch_size, num_tasks, task_state_dim]
            global_state: 全局状态 [batch_size, height, width]
            
        Returns:
            values: 价值估计 [batch_size, num_agents] 或 [batch_size, 1]
        """
        if self.centralized:
            # 中心化价值网络返回全局价值
            return self.value_net(agv_states, task_states, global_state)
        else:
            # 分布式价值网络返回每个智能体的价值
            batch_size = agv_states.size(0)
            all_values = []
            
            for i in range(self.num_agents):
                agv_state = agv_states[:, i, :]  # [batch_size, agv_state_dim]
                value = self.value_nets[i](agv_state, task_states)  # [batch_size, 1]
                all_values.append(value)
            
            return torch.cat(all_values, dim=1)  # [batch_size, num_agents]
