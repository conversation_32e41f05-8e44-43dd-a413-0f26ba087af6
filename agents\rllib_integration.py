"""
RLlib集成接口

实现与Ray RLlib的集成接口，支持分布式训练和标准化的多智能体强化学习
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional, Any, Union
from ray.rllib.models.torch.torch_modelv2 import TorchModelV2
from ray.rllib.models.modelv2 import ModelV2
from ray.rllib.utils.annotations import override
from ray.rllib.utils.typing import ModelConfigDict, TensorType
from ray.rllib.policy.policy import Policy
from ray.rllib.policy.torch_policy import TorchPolicy
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.env.multi_agent_env import MultiAgentEnv
import gymnasium as gym

from agents.networks.policy_net import PolicyNetwork
from agents.networks.value_net import ValueNetwork
from agents.action_masking import ActionMaskGenerator
from envs.warehouse_env import WarehouseEnv


class RLlibPolicyNetwork(TorchModelV2, nn.Module):
    """RLlib兼容的策略网络"""
    
    def __init__(self, 
                 obs_space: gym.Space,
                 action_space: gym.Space,
                 num_outputs: int,
                 model_config: ModelConfigDict,
                 name: str):
        """
        初始化RLlib策略网络
        
        Args:
            obs_space: 观察空间
            action_space: 动作空间
            num_outputs: 输出维度
            model_config: 模型配置
            name: 模型名称
        """
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)
        
        # 从观察空间提取维度信息
        self.agv_state_dim = obs_space['agv_states'].shape[-1]
        self.task_state_dim = obs_space['task_states'].shape[-1]
        self.global_state_shape = obs_space['global_state'].shape
        self.action_dim = num_outputs
        
        # 网络配置
        self.hidden_dims = model_config.get('custom_model_config', {}).get('hidden_dims', [512, 256])
        self.activation = model_config.get('custom_model_config', {}).get('activation', 'relu')
        self.layer_norm = model_config.get('custom_model_config', {}).get('layer_norm', True)
        self.dropout = model_config.get('custom_model_config', {}).get('dropout', 0.1)
        
        # 创建策略网络
        self.policy_net = PolicyNetwork(
            agv_state_dim=self.agv_state_dim,
            task_state_dim=self.task_state_dim,
            global_state_shape=self.global_state_shape,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            layer_norm=self.layer_norm,
            dropout=self.dropout
        )
        
        # 价值网络
        self.value_net = ValueNetwork(
            agv_state_dim=self.agv_state_dim,
            task_state_dim=self.task_state_dim,
            global_state_shape=self.global_state_shape,
            num_agents=1,  # 单智能体视角
            hidden_dims=self.hidden_dims,
            activation=self.activation,
            layer_norm=self.layer_norm,
            dropout=self.dropout
        )
        
        # 存储当前价值
        self._cur_value = None
    
    @override(TorchModelV2)
    def forward(self, 
                input_dict: Dict[str, TensorType],
                state: List[TensorType],
                seq_lens: TensorType) -> Tuple[TensorType, List[TensorType]]:
        """
        前向传播
        
        Args:
            input_dict: 输入字典
            state: RNN状态（未使用）
            seq_lens: 序列长度（未使用）
            
        Returns:
            logits: 动作logits
            state: 更新后的状态
        """
        obs = input_dict["obs"]
        
        # 提取观察组件
        agv_states = obs["agv_states"]
        task_states = obs["task_states"]
        global_state = obs["global_state"]
        action_masks = obs.get("action_masks", None)
        
        # 确保是单个智能体的观察
        if agv_states.dim() == 3:  # [batch, num_agents, features]
            agv_states = agv_states[:, 0, :]  # 取第一个智能体
        
        # 策略网络前向传播
        logits, _ = self.policy_net(agv_states, task_states, global_state, action_masks)
        
        # 价值网络前向传播（用于存储）
        if agv_states.dim() == 2:
            agv_states_expanded = agv_states.unsqueeze(1)  # [batch, 1, features]
        else:
            agv_states_expanded = agv_states
        
        self._cur_value = self.value_net(agv_states_expanded, task_states, global_state).squeeze(-1)
        
        return logits, state
    
    @override(TorchModelV2)
    def value_function(self) -> TensorType:
        """返回当前状态的价值估计"""
        assert self._cur_value is not None, "必须先调用forward()方法"
        return self._cur_value


class RLlibWarehouseEnv(MultiAgentEnv):
    """RLlib兼容的多智能体仓储环境"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化RLlib环境
        
        Args:
            config: 环境配置
        """
        super().__init__()
        
        # 创建基础环境
        self.base_env = WarehouseEnv(config.get('env_config'))
        self.num_agents = self.base_env.num_agvs
        
        # 智能体ID
        self.agent_ids = [f"agv_{i}" for i in range(self.num_agents)]
        
        # 观察和动作空间
        self.observation_space = self._create_agent_obs_space()
        self.action_space = gym.spaces.Discrete(7)  # 7个动作
        
        # 动作掩码生成器
        self.mask_generator = ActionMaskGenerator(
            self.base_env.grid_world,
            self.base_env.collision_detector
        )
    
    def _create_agent_obs_space(self) -> gym.Space:
        """创建单个智能体的观察空间"""
        return gym.spaces.Dict({
            'agv_states': gym.spaces.Box(
                low=-np.inf, high=np.inf,
                shape=(10,), dtype=np.float32
            ),
            'task_states': gym.spaces.Box(
                low=-np.inf, high=np.inf,
                shape=(self.base_env.num_tasks, 11), dtype=np.float32
            ),
            'global_state': gym.spaces.Box(
                low=0, high=10,
                shape=self.base_env.grid_world.to_array().shape, dtype=np.float32
            ),
            'action_masks': gym.spaces.Box(
                low=0, high=1,
                shape=(7,), dtype=np.float32
            )
        })
    
    def reset(self, *, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[Dict, Dict]:
        """重置环境"""
        obs, info = self.base_env.reset(seed=seed, options=options)
        
        # 转换为多智能体格式
        multi_agent_obs = {}
        multi_agent_info = {}
        
        for i, agent_id in enumerate(self.agent_ids):
            multi_agent_obs[agent_id] = {
                'agv_states': obs['agv_states'][i],
                'task_states': obs['task_states'],
                'global_state': obs['global_state'],
                'action_masks': obs['action_masks'][i]
            }
            multi_agent_info[agent_id] = info
        
        return multi_agent_obs, multi_agent_info
    
    def step(self, action_dict: Dict[str, int]) -> Tuple[Dict, Dict, Dict, Dict, Dict]:
        """执行动作"""
        # 转换动作格式
        actions = [action_dict[agent_id] for agent_id in self.agent_ids]
        
        # 执行动作
        obs, reward, terminated, truncated, info = self.base_env.step(actions)
        
        # 转换为多智能体格式
        multi_agent_obs = {}
        multi_agent_rewards = {}
        multi_agent_terminated = {}
        multi_agent_truncated = {}
        multi_agent_info = {}
        
        for i, agent_id in enumerate(self.agent_ids):
            multi_agent_obs[agent_id] = {
                'agv_states': obs['agv_states'][i],
                'task_states': obs['task_states'],
                'global_state': obs['global_state'],
                'action_masks': obs['action_masks'][i]
            }
            multi_agent_rewards[agent_id] = reward  # 共享奖励
            multi_agent_terminated[agent_id] = terminated
            multi_agent_truncated[agent_id] = truncated
            multi_agent_info[agent_id] = info
        
        # 添加特殊键
        multi_agent_terminated["__all__"] = terminated
        multi_agent_truncated["__all__"] = truncated
        
        return (multi_agent_obs, multi_agent_rewards, 
                multi_agent_terminated, multi_agent_truncated, multi_agent_info)
    
    def render(self, mode: str = "human"):
        """渲染环境"""
        return self.base_env.render(mode)
    
    def close(self):
        """关闭环境"""
        self.base_env.close()


def create_rllib_config(env_config: Dict[str, Any], 
                       training_config: Dict[str, Any]) -> PPOConfig:
    """
    创建RLlib配置
    
    Args:
        env_config: 环境配置
        training_config: 训练配置
        
    Returns:
        config: PPO配置对象
    """
    config = (
        PPOConfig()
        .environment(
            env=RLlibWarehouseEnv,
            env_config={"env_config": env_config}
        )
        .framework("torch")
        .training(
            lr=training_config.get('lr', 3e-4),
            gamma=training_config.get('gamma', 0.99),
            lambda_=training_config.get('gae_lambda', 0.95),
            clip_param=training_config.get('clip_param', 0.2),
            vf_loss_coeff=training_config.get('vf_loss_coeff', 0.5),
            entropy_coeff=training_config.get('entropy_coeff', 0.01),
            num_sgd_iter=training_config.get('num_sgd_iter', 10),
            sgd_minibatch_size=training_config.get('sgd_minibatch_size', 128),
            train_batch_size=training_config.get('train_batch_size', 4000),
            model={
                "custom_model": "rllib_policy_network",
                "custom_model_config": {
                    "hidden_dims": training_config.get('hidden_dims', [512, 256]),
                    "activation": training_config.get('activation', 'relu'),
                    "layer_norm": training_config.get('layer_norm', True),
                    "dropout": training_config.get('dropout', 0.1)
                }
            }
        )
        .multi_agent(
            policies={
                agent_id: (None, None, None, {})
                for agent_id in [f"agv_{i}" for i in range(env_config.get('num_agvs', 4))]
            },
            policy_mapping_fn=lambda agent_id, episode, worker, **kwargs: agent_id,
            policies_to_train="all"
        )
        .rollouts(
            num_rollout_workers=training_config.get('num_workers', 4),
            rollout_fragment_length=training_config.get('rollout_fragment_length', 200)
        )
        .resources(
            num_gpus=training_config.get('num_gpus', 1),
            num_cpus_per_worker=training_config.get('num_cpus_per_worker', 1)
        )
        .debugging(
            log_level="INFO"
        )
    )
    
    return config


def register_rllib_components():
    """注册RLlib组件"""
    from ray.rllib.models import ModelCatalog
    
    # 注册自定义模型
    ModelCatalog.register_custom_model("rllib_policy_network", RLlibPolicyNetwork)
    
    # 注册环境
    from ray.tune.registry import register_env
    register_env("warehouse_env", lambda config: RLlibWarehouseEnv(config))


class RLlibTrainer:
    """RLlib训练器包装器"""
    
    def __init__(self, 
                 env_config: Dict[str, Any],
                 training_config: Dict[str, Any],
                 checkpoint_dir: str = "checkpoints"):
        """
        初始化RLlib训练器
        
        Args:
            env_config: 环境配置
            training_config: 训练配置
            checkpoint_dir: 检查点目录
        """
        self.env_config = env_config
        self.training_config = training_config
        self.checkpoint_dir = checkpoint_dir
        
        # 注册组件
        register_rllib_components()
        
        # 创建配置
        self.config = create_rllib_config(env_config, training_config)
        
        # 创建算法
        self.algorithm = None
    
    def train(self, num_iterations: int = 1000):
        """
        开始训练
        
        Args:
            num_iterations: 训练迭代次数
        """
        import ray
        
        # 初始化Ray
        if not ray.is_initialized():
            ray.init()
        
        # 创建算法
        self.algorithm = self.config.build()
        
        # 训练循环
        for i in range(num_iterations):
            result = self.algorithm.train()
            
            # 打印进度
            if i % 10 == 0:
                print(f"Iteration {i}: "
                      f"Episode Reward Mean: {result['episode_reward_mean']:.2f}, "
                      f"Episode Length Mean: {result['episode_len_mean']:.2f}")
            
            # 保存检查点
            if i % 100 == 0:
                checkpoint_path = self.algorithm.save(self.checkpoint_dir)
                print(f"Checkpoint saved at: {checkpoint_path}")
        
        return self.algorithm
    
    def evaluate(self, num_episodes: int = 10):
        """
        评估训练好的模型
        
        Args:
            num_episodes: 评估episode数量
        """
        if self.algorithm is None:
            raise ValueError("必须先训练模型或加载检查点")
        
        # 创建评估环境
        eval_env = RLlibWarehouseEnv({"env_config": self.env_config})
        
        total_rewards = []
        total_lengths = []
        
        for episode in range(num_episodes):
            obs, _ = eval_env.reset()
            episode_reward = 0
            episode_length = 0
            terminated = {"__all__": False}
            
            while not terminated["__all__"]:
                # 获取动作
                actions = {}
                for agent_id in eval_env.agent_ids:
                    action = self.algorithm.compute_single_action(
                        obs[agent_id], policy_id=agent_id
                    )
                    actions[agent_id] = action
                
                # 执行动作
                obs, rewards, terminated, truncated, _ = eval_env.step(actions)
                
                episode_reward += sum(rewards.values()) / len(rewards)
                episode_length += 1
                
                if truncated["__all__"]:
                    break
            
            total_rewards.append(episode_reward)
            total_lengths.append(episode_length)
            
            print(f"Episode {episode + 1}: Reward = {episode_reward:.2f}, Length = {episode_length}")
        
        eval_env.close()
        
        print(f"\nEvaluation Results:")
        print(f"Average Reward: {np.mean(total_rewards):.2f} ± {np.std(total_rewards):.2f}")
        print(f"Average Length: {np.mean(total_lengths):.2f} ± {np.std(total_lengths):.2f}")
        
        return {
            'mean_reward': np.mean(total_rewards),
            'std_reward': np.std(total_rewards),
            'mean_length': np.mean(total_lengths),
            'std_length': np.std(total_lengths)
        }
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        self.algorithm = self.config.build()
        self.algorithm.restore(checkpoint_path)
    
    def close(self):
        """关闭训练器"""
        if self.algorithm:
            self.algorithm.stop()
        
        import ray
        if ray.is_initialized():
            ray.shutdown()
