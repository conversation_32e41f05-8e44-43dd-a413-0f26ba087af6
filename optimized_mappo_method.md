# 基于融合双层注意力机制的MAPPO多AGV调度优化方法论

## 1. 问题建模与环境设定

### 1.1 环境配置
- **地图规格**：26×10网格世界
- **货架布局**：15个货架，每个货架4×2网格，3行5列分布
- **通道设计**：货架间及边界处设置宽度为1的通行区域
- **AGV数量**：4个同构AGV，载重能力25单位
- **任务配置**：16个运输任务，重量为5或10单位

### 1.2 状态空间简化设计

#### 1.2.1 任务状态表示
每个任务$T_i$的特征向量采用简化设计：
$$\mathbf{t}_i = [x_i^{norm}, y_i^{norm}, w_i^{norm}, s_i, d_i^{min}] \in \mathbb{R}^5$$

其中：
- $(x_i^{norm}, y_i^{norm})$：任务位置归一化坐标 $\in [0,1]^2$
- $w_i^{norm} = \frac{w_i - 5}{5} \in \{0, 1\}$：任务重量归一化
- $s_i \in \{0,1,2\}$：任务状态（未分配/已分配/已完成）
- $d_i^{min}$：任务到所有AGV的最短曼哈顿距离归一化值

通过简单的全连接层将5维特征映射到64维：
$$\mathbf{t}_i^{embed} = \text{ReLU}(\mathbf{W}_t \mathbf{t}_i + \mathbf{b}_t) \in \mathbb{R}^{64}$$

#### 1.2.2 AGV状态表示
每个AGV $A_j$的特征向量设计为：
$$\mathbf{a}_j = [x_j^{norm}, y_j^{norm}, l_j^{norm}, q_j^{norm}, target_j, \text{idle}_j] \in \mathbb{R}^6$$

其中：
- $(x_j^{norm}, y_j^{norm})$：AGV位置归一化坐标
- $l_j^{norm} = \frac{l_j}{25}$：当前载重量归一化
- $q_j^{norm} = \frac{q_j}{4}$：任务队列长度归一化（假设最大队列长度为4）
- $target_j$：当前目标任务ID（归一化为$\frac{target\_id}{15}$，无目标时为-1）
- $\text{idle}_j \in \{0,1\}$：是否处于空闲状态

同样通过全连接层映射到64维：
$$\mathbf{a}_j^{embed} = \text{ReLU}(\mathbf{W}_a \mathbf{a}_j + \mathbf{b}_a) \in \mathbb{R}^{64}$$

### 1.3 全局状态构建
将所有AGV和任务的嵌入特征拼接构成全局状态：
$$\mathbf{S}_t = [\mathbf{a}_1^{embed}, \mathbf{a}_2^{embed}, \mathbf{a}_3^{embed}, \mathbf{a}_4^{embed}, \mathbf{t}_1^{embed}, ..., \mathbf{t}_{16}^{embed}] \in \mathbb{R}^{20 \times 64}$$

## 2. 双层注意力机制设计

### 2.1 第一层：任务分配注意力机制

#### 2.1.1 查询、键、值生成
对于每个AGV $j$，其查询向量基于自身状态：
$$\mathbf{Q}_j^{(1)} = \mathbf{a}_j^{embed} \mathbf{W}_Q^{(1)} \in \mathbb{R}^{64}$$

每个任务$i$的键和值向量基于任务状态：
$$\mathbf{K}_i^{(1)} = \mathbf{t}_i^{embed} \mathbf{W}_K^{(1)} \in \mathbb{R}^{64}$$
$$\mathbf{V}_i^{(1)} = \mathbf{t}_i^{embed} \mathbf{W}_V^{(1)} \in \mathbb{R}^{64}$$

其中$\mathbf{W}_Q^{(1)}, \mathbf{W}_K^{(1)}, \mathbf{W}_V^{(1)} \in \mathbb{R}^{64 \times 64}$是可学习的参数矩阵。

#### 2.1.2 距离与载重约束增强
引入距离和载重约束信息来增强注意力计算：

**距离约束项**：
$$d_{j,i} = \frac{|x_j - x_i| + |y_j - y_i|}{26 + 10}$$

**载重约束项**：
$$c_{j,i} = \begin{cases}
1 & \text{if } l_j + w_i \leq 25 \\
0 & \text{otherwise}
\end{cases}$$

**任务可用性约束**：
$$a_{j,i} = \begin{cases}
1 & \text{if } s_i = 0 \\
0 & \text{otherwise}
\end{cases}$$

#### 2.1.3 注意力权重计算
原始注意力分数：
$$e_{j,i}^{(1)} = \frac{\mathbf{Q}_j^{(1)} \cdot (\mathbf{K}_i^{(1)})^T}{\sqrt{64}}$$

约束增强的注意力分数：
$$\tilde{e}_{j,i}^{(1)} = e_{j,i}^{(1)} - \lambda_d \cdot d_{j,i} + \lambda_c \cdot c_{j,i}$$

其中$\lambda_d = 2.0, \lambda_c = 1.0$是超参数。

最终的注意力权重通过softmax计算，并应用约束掩码：
$$\alpha_{j,i}^{(1)} = \frac{\exp(\tilde{e}_{j,i}^{(1)}) \cdot a_{j,i}}{\sum_{k=1}^{16} \exp(\tilde{e}_{j,k}^{(1)}) \cdot a_{j,k}}$$

#### 2.1.4 任务分配输出
第一层注意力输出为：
$$\mathbf{z}_j^{(1)} = \sum_{i=1}^{16} \alpha_{j,i}^{(1)} \mathbf{V}_i^{(1)} \in \mathbb{R}^{64}$$

### 2.2 第二层：协作感知注意力机制

#### 2.2.1 AGV间交互建模
第二层注意力旨在建模AGV间的协作关系。首先将AGV状态与第一层输出结合：
$$\mathbf{h}_j = \mathbf{a}_j^{embed} + \mathbf{z}_j^{(1)} \in \mathbb{R}^{64}$$

#### 2.2.2 相对位置编码
为了捕捉AGV间的空间关系，引入相对位置编码：
$$\mathbf{r}_{j,k} = \text{ReLU}(\mathbf{W}_r [x_j - x_k, y_j - y_k] + \mathbf{b}_r) \in \mathbb{R}^{32}$$

其中$\mathbf{W}_r \in \mathbb{R}^{32 \times 2}$是可学习参数。

#### 2.2.3 协作注意力计算
查询、键、值向量计算：
$$\mathbf{Q}_j^{(2)} = [\mathbf{h}_j; \mathbf{r}_{j,avg}] \mathbf{W}_Q^{(2)} \in \mathbb{R}^{64}$$
$$\mathbf{K}_k^{(2)} = [\mathbf{h}_k; \mathbf{r}_{k,avg}] \mathbf{W}_K^{(2)} \in \mathbb{R}^{64}$$
$$\mathbf{V}_k^{(2)} = \mathbf{h}_k \mathbf{W}_V^{(2)} \in \mathbb{R}^{64}$$

其中$\mathbf{r}_{j,avg} = \frac{1}{3}\sum_{k \neq j} \mathbf{r}_{j,k}$是AGV $j$相对于其他AGV的平均相对位置编码。

注意力权重：
$$\beta_{j,k}^{(2)} = \frac{\exp(\frac{\mathbf{Q}_j^{(2)} \cdot (\mathbf{K}_k^{(2)})^T}{\sqrt{64}})}{\sum_{l=1}^{4} \exp(\frac{\mathbf{Q}_j^{(2)} \cdot (\mathbf{K}_l^{(2)})^T}{\sqrt{64}})}$$

协作感知输出：
$$\mathbf{z}_j^{(2)} = \sum_{k=1}^{4} \beta_{j,k}^{(2)} \mathbf{V}_k^{(2)} \in \mathbb{R}^{64}$$

### 2.3 注意力机制融合
将两层注意力输出进行融合：
$$\mathbf{z}_j^{final} = \text{LayerNorm}(\mathbf{z}_j^{(1)} + \mathbf{z}_j^{(2)}) \in \mathbb{R}^{64}$$

## 3. 基于RLlib的MAPPO实现

### 3.1 观察空间设计

#### 3.1.1 局部观察空间
每个AGV的局部观察包括：
- 自身状态：$\mathbf{a}_j^{embed} \in \mathbb{R}^{64}$
- 任务信息：$\mathbf{T}_{visible} \in \mathbb{R}^{N_{visible} \times 64}$（仅包含距离阈值内的任务）
- 其他AGV状态：$\mathbf{A}_{others} \in \mathbb{R}^{3 \times 64}$

局部观察空间总维度：$64 + N_{visible} \times 64 + 3 \times 64$

#### 3.1.2 全局观察空间
全局观察空间包含完整的环境状态：
$$\mathbf{obs}_{global} = \mathbf{S}_t \in \mathbb{R}^{20 \times 64}$$

### 3.2 动作空间设计

#### 3.2.1 离散动作空间
定义简化的动作空间：
$$\mathcal{A} = \{0: \text{上移}, 1: \text{下移}, 2: \text{左移}, 3: \text{右移}, 4: \text{等待}\}$$

#### 3.2.2 动作掩码机制
实现动作掩码以避免无效动作：
- 边界检查：防止AGV移动到地图边界外
- 障碍物检查：防止AGV移动到货架位置
- 碰撞检查：防止AGV移动到已被占用的位置

动作掩码向量：
$$\mathbf{mask}_j = [m_{j,0}, m_{j,1}, m_{j,2}, m_{j,3}, m_{j,4}] \in \{0,1\}^5$$

其中$m_{j,a} = 1$表示动作$a$对AGV $j$可行。

### 3.3 策略网络架构

#### 3.3.1 注意力增强策略网络
策略网络接收AGV的局部观察和注意力输出：
$$\mathbf{input}_{policy} = [\mathbf{obs}_{local,j}; \mathbf{z}_j^{final}] \in \mathbb{R}^{D_{input}}$$

策略网络结构：
```
输入层 → 全连接层(512) → ReLU → 全连接层(256) → ReLU → 输出层(5)
```

策略输出：
$$\mathbf{logits}_j = \text{PolicyNet}(\mathbf{input}_{policy})$$

应用动作掩码的策略分布：
$$\pi_j(a|\mathbf{s}_t) = \frac{\exp(\mathbf{logits}_j[a]) \cdot \mathbf{mask}_j[a]}{\sum_{a'=0}^{4} \exp(\mathbf{logits}_j[a']) \cdot \mathbf{mask}_j[a']}$$

#### 3.3.2 价值网络架构
价值网络使用全局观察：
$$V(\mathbf{s}_t) = \text{ValueNet}(\mathbf{obs}_{global})$$

价值网络结构：
```
输入层 → 全连接层(512) → ReLU → 全连接层(256) → ReLU → 输出层(1)
```

### 3.4 奖励函数设计

#### 3.4.1 任务完成奖励
$$R_{completion} = \begin{cases}
10 & \text{if 任务完成} \\
0 & \text{otherwise}
\end{cases}$$

#### 3.4.2 移动效率奖励
$$R_{movement} = \begin{cases}
-0.1 & \text{if 移动} \\
-0.05 & \text{if 等待} \\
0 & \text{if 拾取任务}
\end{cases}$$

#### 3.4.3 碰撞惩罚
$$R_{collision} = \begin{cases}
-5 & \text{if 发生碰撞} \\
0 & \text{otherwise}
\end{cases}$$

#### 3.4.4 距离引导奖励
$$R_{distance} = \begin{cases}
0.1 & \text{if 距离目标任务更近} \\
-0.1 & \text{if 距离目标任务更远} \\
0 & \text{otherwise}
\end{cases}$$

#### 3.4.5 系统效率奖励
$$R_{system} = \frac{1}{4} \sum_{j=1}^{4} \begin{cases}
0.2 & \text{if AGV}_j \text{有明确目标} \\
-0.1 & \text{if AGV}_j \text{空闲}
\end{cases}$$

#### 3.4.6 总奖励函数
$$R_{total} = R_{completion} + R_{movement} + R_{collision} + R_{distance} + R_{system}$$

## 4. 训练策略与优化

### 4.1 课程学习策略

#### 4.1.1 阶段一：基础移动训练
- 任务数量：4个
- AGV数量：2个
- 训练目标：学习基本的移动和任务拾取

#### 4.1.2 阶段二：协作训练
- 任务数量：8个
- AGV数量：3个
- 训练目标：学习避免碰撞和简单协作

#### 4.1.3 阶段三：完整场景训练
- 任务数量：16个
- AGV数量：4个
- 训练目标：学习复杂多任务调度和高效协作

### 4.2 注意力机制预训练

#### 4.2.1 任务分配预训练
使用监督学习预训练第一层注意力：
- 数据集：手工设计的最优任务分配样本
- 损失函数：交叉熵损失
- 目标：让注意力机制学习基础的任务分配逻辑

#### 4.2.2 协作感知预训练
使用模仿学习预训练第二层注意力：
- 数据集：专家演示的协作行为轨迹
- 损失函数：行为克隆损失
- 目标：让注意力机制学习协作模式

### 4.3 RLlib配置参数

#### 4.3.1 PPO超参数
```python
ppo_config = {
    "lr": 3e-4,
    "gamma": 0.99,
    "lambda": 0.95,
    "clip_param": 0.2,
    "vf_loss_coeff": 0.5,
    "entropy_coeff": 0.01,
    "num_sgd_iter": 10,
    "sgd_minibatch_size": 128,
    "train_batch_size": 4000,
}
```

#### 4.3.2 网络架构参数
```python
model_config = {
    "fcnet_hiddens": [512, 256],
    "fcnet_activation": "relu",
    "use_attention": True,
    "attention_dim": 64,
    "attention_num_heads": 4,
}
```

#### 4.3.3 多智能体配置
```python
multi_agent_config = {
    "policies": {
        "shared_policy": PolicySpec(
            policy_class=None,
            observation_space=observation_space,
            action_space=action_space,
            config={"gamma": 0.99}
        )
    },
    "policy_mapping_fn": lambda agent_id: "shared_policy",
    "policies_to_train": ["shared_policy"],
}
```

## 5. 实现细节与技术要点

### 5.1 环境实现

#### 5.1.1 状态更新机制
每个时间步的状态更新流程：
1. 收集所有AGV的动作
2. 检查动作有效性和碰撞
3. 更新AGV位置和任务状态
4. 重新计算距离和约束信息
5. 生成新的观察和奖励

#### 5.1.2 任务管理系统
- 任务队列：维护未完成任务的优先级队列
- 任务分配：基于第一层注意力权重进行任务分配
- 任务完成：检测AGV到达任务位置并更新任务状态

### 5.2 注意力机制实现

#### 5.2.1 计算优化
- 批量计算：同时计算所有AGV的注意力权重
- 掩码预计算：预先计算静态约束掩码
- 梯度裁剪：防止注意力权重梯度爆炸

#### 5.2.2 稳定性保证
- 温度调节：在注意力计算中引入温度参数$\tau$
- 正则化：对注意力权重施加L2正则化
- 归一化：使用Layer Normalization稳定训练

### 5.3 训练监控与调试

#### 5.3.1 关键指标监控
- 任务完成率：$\frac{\text{完成任务数}}{\text{总任务数}}$
- 平均完成时间：$\frac{\sum \text{任务完成时间}}{\text{完成任务数}}$
- 碰撞率：$\frac{\text{碰撞次数}}{\text{总时间步数}}$
- 注意力权重分布：监控注意力权重的熵值

#### 5.3.2 调试工具
- 可视化界面：实时显示AGV移动和任务状态
- 注意力热力图：可视化注意力权重分布
- 轨迹回放：记录和回放训练过程中的关键轨迹

## 6. 性能评估指标

### 6.1 任务执行效率
- **任务完成率**：$\eta = \frac{N_{completed}}{N_{total}}$
- **平均完成时间**：$\bar{T} = \frac{1}{N_{completed}} \sum_{i=1}^{N_{completed}} T_i$
- **系统吞吐量**：$\rho = \frac{N_{completed}}{T_{total}}$

### 6.2 协作效率
- **碰撞率**：$\gamma = \frac{N_{collisions}}{T_{total}}$
- **平均等待时间**：$\bar{W} = \frac{1}{N_{agents}} \sum_{j=1}^{N_{agents}} W_j$
- **路径效率**：$\epsilon = \frac{\text{最短路径长度}}{\text{实际路径长度}}$

### 6.3 注意力机制效果
- **注意力准确性**：与最优任务分配的匹配度
- **注意力稳定性**：连续时间步间注意力权重的变化幅度
- **注意力多样性**：注意力权重分布的熵值

