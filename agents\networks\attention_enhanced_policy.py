"""
注意力增强策略网络

将双层注意力机制集成到策略网络，实现层次化动作生成
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List, Union
from torch.distributions import Categorical, Normal

from ..attention.task_allocation_attention import TaskAllocationAttention
from ..attention.collaboration_attention import CollaborationAwareAttention
from ..attention.dual_layer_fusion import DualLayerAttentionFusion


class AttentionEnhancedPolicyNetwork(nn.Module):
    """注意力增强策略网络"""
    
    def __init__(self, 
                 agv_state_dim: int = 10,
                 task_state_dim: int = 11,
                 global_state_shape: Tuple[int, int] = (10, 26),
                 action_dim: int = 5,
                 num_agvs: int = 4,
                 num_tasks: int = 10,
                 embed_dim: int = 64,
                 hidden_dims: List[int] = None,
                 use_dual_attention: bool = True,
                 use_hierarchical_actions: bool = True,
                 action_type: str = "discrete",
                 activation: str = "relu",
                 layer_norm: bool = True,
                 dropout: float = 0.1):
        """
        初始化注意力增强策略网络
        
        Args:
            agv_state_dim: AGV状态维度
            task_state_dim: 任务状态维度
            global_state_shape: 全局状态形状
            action_dim: 动作维度
            num_agvs: AGV数量
            num_tasks: 任务数量
            embed_dim: 嵌入维度
            hidden_dims: 隐藏层维度列表
            use_dual_attention: 是否使用双层注意力
            use_hierarchical_actions: 是否使用层次化动作
            action_type: 动作类型 ("discrete" 或 "continuous")
            activation: 激活函数类型
            layer_norm: 是否使用层归一化
            dropout: Dropout概率
        """
        super(AttentionEnhancedPolicyNetwork, self).__init__()
        
        self.agv_state_dim = agv_state_dim
        self.task_state_dim = task_state_dim
        self.global_state_shape = global_state_shape
        self.action_dim = action_dim
        self.num_agvs = num_agvs
        self.num_tasks = num_tasks
        self.embed_dim = embed_dim
        self.hidden_dims = hidden_dims or [256, 128]
        self.use_dual_attention = use_dual_attention
        self.use_hierarchical_actions = use_hierarchical_actions
        self.action_type = action_type
        self.layer_norm = layer_norm
        self.dropout = dropout
        
        # 激活函数
        if activation == "relu":
            self.activation = nn.ReLU()
        elif activation == "tanh":
            self.activation = nn.Tanh()
        elif activation == "gelu":
            self.activation = nn.GELU()
        else:
            self.activation = nn.ReLU()
        
        # 状态编码器
        self.agv_encoder = nn.Sequential(
            nn.Linear(agv_state_dim, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation
        )
        
        self.task_encoder = nn.Sequential(
            nn.Linear(task_state_dim, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 全局状态编码器
        self.global_encoder = nn.Sequential(
            nn.Conv2d(1, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32) if layer_norm else nn.Identity(),
            self.activation,
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.AdaptiveAvgPool2d((4, 4)),
            nn.Flatten(),
            nn.Linear(64 * 4 * 4, embed_dim),
            nn.LayerNorm(embed_dim) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 双层注意力机制
        if use_dual_attention:
            # 任务分配注意力
            self.task_attention = TaskAllocationAttention(
                agv_state_dim=embed_dim,
                task_state_dim=embed_dim,
                embed_dim=embed_dim,
                num_heads=8,
                dropout=dropout
            )
            
            # 协作感知注意力
            self.collaboration_attention = CollaborationAwareAttention(
                embed_dim=embed_dim,
                num_heads=8,
                distance_thresholds=[5.0, 15.0, 30.0],
                use_adaptive_temperature=True,
                use_adaptive_hierarchy=True,
                dropout=dropout
            )
            
            # 双层注意力融合
            self.attention_fusion = DualLayerAttentionFusion(
                embed_dim=embed_dim,
                num_agvs=num_agvs,
                num_tasks=num_tasks,
                fusion_method="gated",
                dropout=dropout
            )
        
        # 层次化动作生成
        if use_hierarchical_actions:
            self.action_hierarchy = HierarchicalActionGenerator(
                embed_dim=embed_dim,
                action_dim=action_dim,
                action_type=action_type,
                hidden_dims=self.hidden_dims,
                activation=self.activation,
                layer_norm=layer_norm,
                dropout=dropout
            )
        else:
            # 标准动作生成
            self.action_generator = StandardActionGenerator(
                embed_dim=embed_dim,
                action_dim=action_dim,
                action_type=action_type,
                hidden_dims=self.hidden_dims,
                activation=self.activation,
                layer_norm=layer_norm,
                dropout=dropout
            )
        
        # 特征融合网络
        fusion_input_dim = embed_dim  # 注意力输出
        if not use_dual_attention:
            fusion_input_dim = embed_dim * 2  # AGV + 任务特征
        
        self.feature_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim + embed_dim, self.hidden_dims[0]),  # +全局特征
            nn.LayerNorm(self.hidden_dims[0]) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout)
        )
        
        # 策略头部网络
        policy_layers = []
        input_dim = self.hidden_dims[0]
        
        for hidden_dim in self.hidden_dims[1:]:
            policy_layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.LayerNorm(hidden_dim) if layer_norm else nn.Identity(),
                self.activation,
                nn.Dropout(dropout)
            ])
            input_dim = hidden_dim
        
        self.policy_head = nn.Sequential(*policy_layers)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Conv2d):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
    
    def forward(self, 
                agv_state: torch.Tensor,
                task_states: torch.Tensor,
                global_state: torch.Tensor,
                agv_positions: Optional[torch.Tensor] = None,
                agv_velocities: Optional[torch.Tensor] = None,
                all_agv_states: Optional[torch.Tensor] = None,
                deterministic: bool = False) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_state: 当前AGV状态 [batch_size, agv_state_dim]
            task_states: 任务状态 [batch_size, num_tasks, task_state_dim]
            global_state: 全局状态 [batch_size, height, width]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            all_agv_states: 所有AGV状态 [batch_size, num_agvs, agv_state_dim]
            deterministic: 是否确定性采样
            
        Returns:
            policy_output: 策略输出字典
        """
        batch_size = agv_state.size(0)
        
        # 编码状态
        agv_features = self.agv_encoder(agv_state)  # [batch_size, embed_dim]
        
        # 编码任务状态
        task_states_flat = task_states.view(batch_size * self.num_tasks, -1)
        task_features = self.task_encoder(task_states_flat)
        task_features = task_features.view(batch_size, self.num_tasks, self.embed_dim)
        
        # 编码全局状态
        global_state = global_state.unsqueeze(1)  # [batch_size, 1, height, width]
        global_features = self.global_encoder(global_state)  # [batch_size, embed_dim]
        
        # 双层注意力处理
        if self.use_dual_attention and all_agv_states is not None:
            # 编码所有AGV状态
            all_agv_states_flat = all_agv_states.view(batch_size * self.num_agvs, -1)
            all_agv_features = self.agv_encoder(all_agv_states_flat)
            all_agv_features = all_agv_features.view(batch_size, self.num_agvs, self.embed_dim)
            
            # 任务分配注意力
            task_attention_result = self.task_attention(
                agv_states=all_agv_states,
                task_states=task_states
            )
            
            # 协作感知注意力
            if agv_positions is not None and agv_velocities is not None:
                collaboration_result = self.collaboration_attention(
                    agv_embeddings=all_agv_features,
                    agv_positions=agv_positions,
                    agv_velocities=agv_velocities,
                    agv_states=all_agv_states
                )
                
                # 双层注意力融合
                fusion_result = self.attention_fusion(
                    task_attention_output=task_attention_result['final_output'],
                    collaboration_attention_output=collaboration_result['collaboration_output'],
                    agv_states=all_agv_states,
                    task_states=task_states,
                    agv_positions=agv_positions,
                    agv_velocities=agv_velocities,
                    global_context=global_features
                )

                # 提取当前AGV的注意力特征
                current_agv_idx = 0  # 假设当前AGV是第一个，实际应用中需要传入AGV索引
                attention_features = fusion_result['fused_output'][:, current_agv_idx, :]
                
                attention_info = {
                    'task_attention': task_attention_result,
                    'collaboration_attention': collaboration_result,
                    'fusion_result': fusion_result
                }
            else:
                # 只使用任务分配注意力
                attention_features = task_attention_result['final_output'][:, 0, :]
                attention_info = {'task_attention': task_attention_result}
        else:
            # 不使用注意力机制，直接使用编码特征
            attention_features = agv_features
            attention_info = {}
        
        # 特征融合
        if self.use_dual_attention:
            fused_features = torch.cat([attention_features, global_features], dim=1)
        else:
            # 简单聚合任务特征
            task_features_agg = torch.mean(task_features, dim=1)
            fused_features = torch.cat([agv_features, task_features_agg, global_features], dim=1)
        
        fused_features = self.feature_fusion(fused_features)
        
        # 策略头部
        policy_features = self.policy_head(fused_features)
        
        # 动作生成
        if self.use_hierarchical_actions:
            action_result = self.action_hierarchy(policy_features, attention_features, deterministic)
        else:
            action_result = self.action_generator(policy_features, deterministic)
        
        # 组装输出
        output = {
            'actions': action_result['actions'],
            'action_logprobs': action_result['action_logprobs'],
            'action_probs': action_result.get('action_probs'),
            'action_values': action_result.get('action_values'),
            'policy_features': policy_features,
            'attention_features': attention_features,
            'attention_info': attention_info
        }
        
        # 添加层次化动作信息
        if self.use_hierarchical_actions:
            output.update({
                'high_level_actions': action_result.get('high_level_actions'),
                'low_level_actions': action_result.get('low_level_actions'),
                'action_hierarchy_info': action_result.get('hierarchy_info')
            })
        
        return output
    
    def get_action_distribution(self, 
                              agv_state: torch.Tensor,
                              task_states: torch.Tensor,
                              global_state: torch.Tensor,
                              **kwargs) -> Union[Categorical, Normal]:
        """获取动作分布"""
        output = self.forward(agv_state, task_states, global_state, **kwargs)
        
        if self.action_type == "discrete":
            return Categorical(probs=output['action_probs'])
        else:
            # 连续动作，假设输出包含均值和标准差
            mean = output['actions']
            std = output.get('action_std', torch.ones_like(mean))
            return Normal(mean, std)
    
    def evaluate_actions(self, 
                        agv_state: torch.Tensor,
                        task_states: torch.Tensor,
                        global_state: torch.Tensor,
                        actions: torch.Tensor,
                        **kwargs) -> Dict[str, torch.Tensor]:
        """评估动作"""
        output = self.forward(agv_state, task_states, global_state, **kwargs)
        
        if self.action_type == "discrete":
            action_dist = Categorical(probs=output['action_probs'])
            action_logprobs = action_dist.log_prob(actions)
            entropy = action_dist.entropy()
        else:
            mean = output['actions']
            std = output.get('action_std', torch.ones_like(mean))
            action_dist = Normal(mean, std)
            action_logprobs = action_dist.log_prob(actions).sum(dim=-1)
            entropy = action_dist.entropy().sum(dim=-1)
        
        return {
            'action_logprobs': action_logprobs,
            'entropy': entropy,
            'action_values': output.get('action_values'),
            'attention_info': output['attention_info']
        }


class HierarchicalActionGenerator(nn.Module):
    """层次化动作生成器"""

    def __init__(self,
                 embed_dim: int,
                 action_dim: int,
                 action_type: str,
                 hidden_dims: List[int],
                 activation: nn.Module,
                 layer_norm: bool,
                 dropout: float):
        super(HierarchicalActionGenerator, self).__init__()

        self.embed_dim = embed_dim
        self.action_dim = action_dim
        self.action_type = action_type
        self.layer_norm = layer_norm

        # 高层动作生成器（策略选择）
        self.high_level_generator = nn.Sequential(
            nn.Linear(embed_dim, hidden_dims[0] // 2),
            nn.LayerNorm(hidden_dims[0] // 2) if layer_norm else nn.Identity(),
            activation,
            nn.Dropout(dropout),
            nn.Linear(hidden_dims[0] // 2, 3),  # 3种高层策略：探索、利用、协作
            nn.LogSoftmax(dim=-1)  # 使用LogSoftmax提高数值稳定性
        )

        # 低层动作生成器
        self.low_level_generators = nn.ModuleDict({
            'explore': self._create_action_head(embed_dim, action_dim, action_type, hidden_dims, activation, layer_norm, dropout),
            'exploit': self._create_action_head(embed_dim, action_dim, action_type, hidden_dims, activation, layer_norm, dropout),
            'collaborate': self._create_action_head(embed_dim, action_dim, action_type, hidden_dims, activation, layer_norm, dropout)
        })

        # 注意力引导网络
        # 需要动态计算输入维度
        self.attention_guide = None  # 延迟初始化

    def _create_action_head(self, embed_dim, action_dim, action_type, hidden_dims, activation, layer_norm, dropout):
        """创建动作头部"""
        layers = []
        input_dim = embed_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.LayerNorm(hidden_dim) if layer_norm else nn.Identity(),
                activation,
                nn.Dropout(dropout)
            ])
            input_dim = hidden_dim

        if action_type == "discrete":
            layers.append(nn.Linear(input_dim, action_dim))
        else:
            # 连续动作：输出均值和对数标准差
            layers.extend([
                nn.Linear(input_dim, action_dim * 2)  # 均值 + 对数标准差
            ])

        return nn.Sequential(*layers)

    def forward(self,
                policy_features: torch.Tensor,
                attention_features: torch.Tensor,
                deterministic: bool = False) -> Dict[str, torch.Tensor]:
        """层次化动作生成"""
        batch_size = policy_features.size(0)

        # 延迟初始化注意力引导网络
        if self.attention_guide is None:
            input_dim = policy_features.size(1) + attention_features.size(1)
            self.attention_guide = nn.Sequential(
                nn.Linear(input_dim, self.embed_dim),
                nn.LayerNorm(self.embed_dim) if self.layer_norm else nn.Identity(),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(self.embed_dim, self.embed_dim)
            ).to(policy_features.device)

        # 注意力引导特征
        guided_features = self.attention_guide(
            torch.cat([policy_features, attention_features], dim=1)
        )

        # 高层策略选择
        high_level_log_probs = self.high_level_generator(guided_features)
        high_level_probs = torch.exp(high_level_log_probs)

        if deterministic:
            high_level_actions = torch.argmax(high_level_probs, dim=-1)
        else:
            high_level_dist = Categorical(logits=high_level_log_probs)
            high_level_actions = high_level_dist.sample()

        # 根据高层策略生成低层动作
        strategy_names = ['explore', 'exploit', 'collaborate']
        low_level_outputs = {}

        for i, strategy in enumerate(strategy_names):
            strategy_output = self.low_level_generators[strategy](guided_features)

            if self.action_type == "discrete":
                strategy_probs = F.softmax(strategy_output, dim=-1)
                low_level_outputs[strategy] = {
                    'logits': strategy_output,
                    'probs': strategy_probs
                }
            else:
                mean, log_std = torch.chunk(strategy_output, 2, dim=-1)
                std = torch.exp(log_std.clamp(-20, 2))
                low_level_outputs[strategy] = {
                    'mean': mean,
                    'std': std
                }

        # 选择对应策略的低层动作
        final_actions = []
        final_logprobs = []
        final_probs = []

        for b in range(batch_size):
            strategy_idx = high_level_actions[b].item()
            strategy = strategy_names[strategy_idx]

            if self.action_type == "discrete":
                if deterministic:
                    action = torch.argmax(low_level_outputs[strategy]['probs'][b])
                else:
                    action_dist = Categorical(probs=low_level_outputs[strategy]['probs'][b])
                    action = action_dist.sample()

                logprob = torch.log(low_level_outputs[strategy]['probs'][b, action] + 1e-8)
                prob = low_level_outputs[strategy]['probs'][b]

                final_actions.append(action)
                final_logprobs.append(logprob)
                final_probs.append(prob)
            else:
                mean = low_level_outputs[strategy]['mean'][b]
                std = low_level_outputs[strategy]['std'][b]

                if deterministic:
                    action = mean
                else:
                    action_dist = Normal(mean, std)
                    action = action_dist.sample()

                logprob = Normal(mean, std).log_prob(action).sum()

                final_actions.append(action)
                final_logprobs.append(logprob)

        final_actions = torch.stack(final_actions)
        final_logprobs = torch.stack(final_logprobs)

        result = {
            'actions': final_actions,
            'action_logprobs': final_logprobs,
            'high_level_actions': high_level_actions,
            'low_level_actions': low_level_outputs,
            'hierarchy_info': {
                'high_level_probs': high_level_probs,
                'strategy_names': strategy_names,
                'guided_features': guided_features
            }
        }

        if self.action_type == "discrete":
            result['action_probs'] = torch.stack(final_probs) if final_probs else None

        return result


class StandardActionGenerator(nn.Module):
    """标准动作生成器"""

    def __init__(self,
                 embed_dim: int,
                 action_dim: int,
                 action_type: str,
                 hidden_dims: List[int],
                 activation: nn.Module,
                 layer_norm: bool,
                 dropout: float):
        super(StandardActionGenerator, self).__init__()

        self.embed_dim = embed_dim
        self.action_dim = action_dim
        self.action_type = action_type

        # 动作头部 - 延迟初始化
        self.action_head = None
        self.hidden_dims = hidden_dims
        self.action_type = action_type
        self.action_dim = action_dim
        self.layer_norm = layer_norm
        self.activation = activation
        self.dropout = dropout

    def forward(self,
                policy_features: torch.Tensor,
                deterministic: bool = False) -> Dict[str, torch.Tensor]:
        """标准动作生成"""
        # 延迟初始化动作头部
        if self.action_head is None:
            input_dim = policy_features.size(1)
            layers = []

            for hidden_dim in self.hidden_dims:
                layers.extend([
                    nn.Linear(input_dim, hidden_dim),
                    nn.LayerNorm(hidden_dim) if self.layer_norm else nn.Identity(),
                    self.activation,
                    nn.Dropout(self.dropout)
                ])
                input_dim = hidden_dim

            if self.action_type == "discrete":
                layers.append(nn.Linear(input_dim, self.action_dim))
            else:
                # 连续动作：输出均值和对数标准差
                layers.extend([
                    nn.Linear(input_dim, self.action_dim * 2)
                ])

            self.action_head = nn.Sequential(*layers).to(policy_features.device)

        action_output = self.action_head(policy_features)

        if self.action_type == "discrete":
            # 添加数值稳定性
            action_output = torch.clamp(action_output, min=-10, max=10)
            action_probs = F.softmax(action_output, dim=-1)
            # 防止NaN
            action_probs = torch.clamp(action_probs, min=1e-8, max=1.0)
            action_probs = action_probs / action_probs.sum(dim=-1, keepdim=True)

            if deterministic:
                actions = torch.argmax(action_probs, dim=-1)
                action_logprobs = torch.log(torch.max(action_probs, dim=-1)[0] + 1e-8)
            else:
                # 使用logits而不是probs来避免数值问题
                action_dist = Categorical(logits=action_output)
                actions = action_dist.sample()
                action_logprobs = action_dist.log_prob(actions)

            return {
                'actions': actions,
                'action_logprobs': action_logprobs,
                'action_probs': action_probs,
                'action_logits': action_output
            }
        else:
            mean, log_std = torch.chunk(action_output, 2, dim=-1)
            std = torch.exp(log_std.clamp(-20, 2))

            if deterministic:
                actions = mean
                action_logprobs = Normal(mean, std).log_prob(actions).sum(dim=-1)
            else:
                action_dist = Normal(mean, std)
                actions = action_dist.sample()
                action_logprobs = action_dist.log_prob(actions).sum(dim=-1)

            return {
                'actions': actions,
                'action_logprobs': action_logprobs,
                'action_mean': mean,
                'action_std': std
            }


class AttentionPolicyWrapper(nn.Module):
    """注意力策略包装器，用于与现有MAPPO框架集成"""

    def __init__(self,
                 policy_network: AttentionEnhancedPolicyNetwork,
                 agv_id: int = 0):
        super(AttentionPolicyWrapper, self).__init__()

        self.policy_network = policy_network
        self.agv_id = agv_id

    def forward(self,
                obs: Dict[str, torch.Tensor],
                deterministic: bool = False) -> Dict[str, torch.Tensor]:
        """
        包装器前向传播，适配MAPPO接口

        Args:
            obs: 观测字典，包含：
                - 'agv_state': 当前AGV状态
                - 'task_states': 任务状态
                - 'global_state': 全局状态
                - 'agv_positions': AGV位置（可选）
                - 'agv_velocities': AGV速度（可选）
                - 'all_agv_states': 所有AGV状态（可选）
            deterministic: 是否确定性采样

        Returns:
            policy_output: 策略输出
        """
        return self.policy_network(
            agv_state=obs['agv_state'],
            task_states=obs['task_states'],
            global_state=obs['global_state'],
            agv_positions=obs.get('agv_positions'),
            agv_velocities=obs.get('agv_velocities'),
            all_agv_states=obs.get('all_agv_states'),
            deterministic=deterministic
        )

    def get_action(self,
                   obs: Dict[str, torch.Tensor],
                   deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor]:
        """获取动作和对数概率"""
        output = self.forward(obs, deterministic)
        return output['actions'], output['action_logprobs']

    def evaluate_actions(self,
                        obs: Dict[str, torch.Tensor],
                        actions: torch.Tensor) -> Dict[str, torch.Tensor]:
        """评估动作"""
        return self.policy_network.evaluate_actions(
            agv_state=obs['agv_state'],
            task_states=obs['task_states'],
            global_state=obs['global_state'],
            actions=actions,
            agv_positions=obs.get('agv_positions'),
            agv_velocities=obs.get('agv_velocities'),
            all_agv_states=obs.get('all_agv_states')
        )
