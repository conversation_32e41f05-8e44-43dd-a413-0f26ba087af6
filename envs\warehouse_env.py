"""
多AGV仓储环境主类

实现符合Gymnasium标准的环境接口，整合所有子系统
"""

import numpy as np
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, List, Tuple, Any, Optional, Union
import copy

from envs.grid_world import GridWorld
from envs.agv_model import AGVModel, AGVAction, AGVStatus
from envs.task_manager import TaskManager, TaskPriority
from envs.collision_detector import CollisionDetector, CollisionType
from envs.visualization import WarehouseVisualizer
from utils.config import Config
from utils.logger import Logger


class WarehouseEnv(gym.Env):
    """多AGV仓储环境"""
    
    metadata = {
        'render_modes': ['human', 'rgb_array'],
        'render_fps': 10
    }
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化环境
        
        Args:
            config: 配置对象
        """
        super().__init__()
        
        # 配置
        self.config = config or Config()
        
        # 环境参数
        self.map_width = self.config.env.map_width
        self.map_height = self.config.env.map_height
        self.num_agvs = self.config.env.num_agvs
        self.num_tasks = self.config.env.num_tasks
        self.max_episode_steps = self.config.env.max_episode_steps
        self.render_mode = self.config.env.render_mode
        
        # 子系统初始化
        self.grid_world = GridWorld(
            width=self.map_width,
            height=self.map_height,
            num_shelves=self.config.env.num_shelves,
            shelf_width=self.config.env.shelf_width,
            shelf_height=self.config.env.shelf_height
        )
        
        self.task_manager = TaskManager(
            max_tasks=self.num_tasks,
            task_weights=self.config.env.task_weights
        )
        
        self.collision_detector = CollisionDetector(self.grid_world)
        
        # AGV初始化
        self.agvs: List[AGVModel] = []
        self._initialize_agvs()
        
        # 可视化器
        self.visualizer = None
        if self.render_mode == 'human':
            self.visualizer = WarehouseVisualizer(self.grid_world)
        
        # 状态管理
        self.current_step = 0
        self.episode_reward = 0.0
        self.done = False
        
        # 定义动作和观察空间
        self._setup_spaces()
        
        # 性能统计
        self.episode_stats = {
            'total_reward': 0.0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'collisions': 0,
            'total_distance': 0,
            'completion_rate': 0.0
        }
    
    def _initialize_agvs(self):
        """初始化AGV"""
        self.agvs.clear()
        
        # 在可通行区域随机放置AGV
        free_positions = list(self.grid_world.free_spaces)
        
        for i in range(self.num_agvs):
            if i < len(free_positions):
                x, y = free_positions[i]
            else:
                x, y = self.grid_world.get_random_free_position()
            
            agv = AGVModel(
                agv_id=i,
                initial_x=x,
                initial_y=y,
                capacity=self.config.env.agv_capacity
            )
            self.agvs.append(agv)
    
    def _setup_spaces(self):
        """设置动作和观察空间"""
        # 动作空间：每个AGV有7个可能的动作
        self.action_space = spaces.MultiDiscrete([7] * self.num_agvs)
        
        # 观察空间：复合观察空间
        self.observation_space = spaces.Dict({
            # AGV状态：每个AGV 10维状态向量
            'agv_states': spaces.Box(
                low=-np.inf, high=np.inf,
                shape=(self.num_agvs, 10), dtype=np.float32
            ),
            
            # 任务状态：每个任务 11维状态向量
            'task_states': spaces.Box(
                low=-np.inf, high=np.inf,
                shape=(self.num_tasks, 11), dtype=np.float32
            ),
            
            # 全局状态：地图信息
            'global_state': spaces.Box(
                low=0, high=10,
                shape=(self.map_height, self.map_width), dtype=np.float32
            ),
            
            # 动作掩码：每个AGV的有效动作
            'action_masks': spaces.Box(
                low=0, high=1,
                shape=(self.num_agvs, 7), dtype=np.float32
            )
        })
    
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[Dict, Dict]:
        """重置环境"""
        super().reset(seed=seed)
        
        # 重置状态
        self.current_step = 0
        self.episode_reward = 0.0
        self.done = False
        
        # 重置子系统
        self.task_manager.reset()
        self.collision_detector.reset_statistics()
        
        # 重置AGV
        for agv in self.agvs:
            x, y = self.grid_world.get_random_free_position()
            agv.reset(x, y)
        
        # 生成初始任务
        self._generate_initial_tasks()
        
        # 重置统计
        self.episode_stats = {
            'total_reward': 0.0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'collisions': 0,
            'total_distance': 0,
            'completion_rate': 0.0
        }
        
        # 获取初始观察
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, info
    
    def _generate_initial_tasks(self):
        """生成初始任务"""
        # 使用货架位置作为拾取点
        pickup_positions = []
        for shelf in self.grid_world.shelves:
            # 在每个货架的中心位置生成拾取点
            center_x = shelf['x'] + shelf['width'] // 2
            center_y = shelf['y'] + shelf['height'] // 2
            pickup_positions.append((center_x, center_y))

        dropoff_positions = self.grid_world.dropoff_points

        if pickup_positions and dropoff_positions:
            self.task_manager.generate_random_tasks(
                num_tasks=self.num_tasks,
                pickup_positions=pickup_positions,
                dropoff_positions=dropoff_positions
            )
    
    def step(self, actions: Union[np.ndarray, List[int]]) -> Tuple[Dict, float, bool, bool, Dict]:
        """执行一步"""
        if self.done:
            raise RuntimeError("环境已结束，请调用reset()重置")
        
        # 转换动作格式
        if isinstance(actions, np.ndarray):
            actions = actions.tolist()
        
        # 执行动作
        rewards = self._execute_actions(actions)
        
        # 更新环境状态
        self._update_environment()
        
        # 计算奖励
        total_reward = sum(rewards)
        self.episode_reward += total_reward
        
        # 检查终止条件
        terminated = self._check_termination()
        truncated = self.current_step >= self.max_episode_steps
        self.done = terminated or truncated
        
        # 获取观察和信息
        observation = self._get_observation()
        info = self._get_info()
        
        # 更新统计
        self._update_statistics()
        
        self.current_step += 1
        
        return observation, total_reward, terminated, truncated, info
    
    def _execute_actions(self, actions: List[int]) -> List[float]:
        """执行AGV动作"""
        rewards = []
        
        for i, (agv, action_idx) in enumerate(zip(self.agvs, actions)):
            action = AGVAction(action_idx)
            reward = 0.0
            
            # 检查动作有效性
            other_agvs = [a for a in self.agvs if a.id != agv.id]
            is_valid, collision_type = self.collision_detector.check_action_validity(
                agv, action, other_agvs
            )
            
            if is_valid:
                # 执行动作
                if action in [AGVAction.MOVE_UP, AGVAction.MOVE_DOWN, 
                             AGVAction.MOVE_LEFT, AGVAction.MOVE_RIGHT]:
                    agv.execute_action(action)
                    reward += self._calculate_movement_reward(agv)
                
                elif action == AGVAction.PICKUP:
                    reward += self._handle_pickup(agv)
                
                elif action == AGVAction.DROPOFF:
                    reward += self._handle_dropoff(agv)
                
                elif action == AGVAction.WAIT:
                    agv.execute_action(action)
                    reward += self._calculate_wait_reward(agv)
            
            else:
                # 处理碰撞
                self.collision_detector.record_collision(collision_type, [agv.id])
                reward += self._calculate_collision_penalty(collision_type)
                
                # 执行等待动作
                agv.execute_action(AGVAction.WAIT)
            
            rewards.append(reward)
        
        return rewards
    
    def _handle_pickup(self, agv: AGVModel) -> float:
        """处理拾取动作"""
        reward = 0.0
        
        # 查找当前位置的可拾取任务
        available_tasks = self.task_manager.get_unassigned_tasks()
        
        for task in available_tasks:
            if (task.pickup_position == agv.position and 
                agv.can_take_task(task.weight)):
                
                # 分配并开始任务
                self.task_manager.assign_task(task.id, agv.id)
                self.task_manager.start_task(task.id)
                
                # AGV拾取任务
                if agv.pickup_task(task.weight):
                    self.task_manager.complete_pickup(task.id)
                    reward += 10.0  # 拾取奖励
                    break
        
        return reward
    
    def _handle_dropoff(self, agv: AGVModel) -> float:
        """处理放置动作"""
        reward = 0.0
        
        # 查找当前AGV携带的任务
        for task_id in agv.carried_tasks.copy():
            task = self.task_manager.get_task(task_id)
            
            if task and task.dropoff_position == agv.position:
                # 完成任务
                if agv.dropoff_task(task_id, task.weight):
                    self.task_manager.complete_task(task_id)
                    reward += 20.0  # 完成任务奖励
                    
                    # 根据任务优先级给予额外奖励
                    if task.priority == TaskPriority.URGENT:
                        reward += 10.0
                    elif task.priority == TaskPriority.HIGH:
                        reward += 5.0
        
        return reward
    
    def _calculate_movement_reward(self, agv: AGVModel) -> float:
        """计算移动奖励"""
        reward = -0.1  # 基础移动成本
        
        # 如果朝向目标移动，给予奖励
        if agv.target_position is not None:
            old_distance = self.grid_world.get_distance(agv.last_position, agv.target_position)
            new_distance = self.grid_world.get_distance(agv.position, agv.target_position)
            
            if new_distance < old_distance:
                reward += 0.5  # 接近目标奖励
            elif new_distance > old_distance:
                reward -= 0.2  # 远离目标惩罚
        
        return reward
    
    def _calculate_wait_reward(self, agv: AGVModel) -> float:
        """计算等待奖励"""
        return -0.05  # 等待的小惩罚
    
    def _calculate_collision_penalty(self, collision_type: CollisionType) -> float:
        """计算碰撞惩罚"""
        penalties = {
            CollisionType.AGV_COLLISION: -5.0,
            CollisionType.WALL_COLLISION: -2.0,
            CollisionType.SHELF_COLLISION: -3.0,
            CollisionType.BOUNDARY_COLLISION: -2.0,
            CollisionType.DEADLOCK: -10.0
        }
        return penalties.get(collision_type, -1.0)
    
    def _update_environment(self):
        """更新环境状态"""
        # 更新任务管理器
        self.task_manager.step()
        
        # 检查死锁
        if self.collision_detector.check_deadlock(self.agvs):
            # 简单的死锁解决：随机重新分配位置
            self._resolve_deadlock()
    
    def _resolve_deadlock(self):
        """解决死锁"""
        for agv in self.agvs:
            if agv.total_wait_time > self.collision_detector.deadlock_threshold:
                new_pos = self.grid_world.get_random_free_position()
                agv.x, agv.y = new_pos
                agv.total_wait_time = 0
    
    def _check_termination(self) -> bool:
        """检查终止条件"""
        # 所有任务完成
        if self.task_manager.total_tasks_completed >= self.num_tasks:
            return True
        
        # 任务失败率过高
        if (self.task_manager.total_tasks_failed > 0 and
            self.task_manager.total_tasks_failed / self.num_tasks > 0.5):
            return True
        
        return False
    
    def _get_observation(self) -> Dict[str, np.ndarray]:
        """获取观察"""
        # AGV状态
        agv_states = np.array([agv.get_state_vector() for agv in self.agvs])
        
        # 任务状态
        task_states = self.task_manager.get_all_tasks_state()
        
        # 全局状态（地图）
        global_state = self.grid_world.to_array().astype(np.float32)
        
        # 动作掩码
        action_masks = np.array([
            self.collision_detector.generate_action_mask(agv, 
                [a for a in self.agvs if a.id != agv.id])
            for agv in self.agvs
        ])
        
        return {
            'agv_states': agv_states,
            'task_states': task_states,
            'global_state': global_state,
            'action_masks': action_masks
        }
    
    def _get_info(self) -> Dict[str, Any]:
        """获取信息"""
        return {
            'step': self.current_step,
            'episode_reward': self.episode_reward,
            'task_stats': self.task_manager.get_task_statistics(),
            'collision_stats': self.collision_detector.get_collision_statistics(),
            'agv_stats': self._get_agv_statistics(),
            'episode_stats': self.episode_stats.copy()
        }
    
    def _get_agv_statistics(self) -> Dict[str, Any]:
        """获取AGV统计信息"""
        return {
            'total_distance': sum(agv.total_distance for agv in self.agvs),
            'total_tasks_completed': sum(agv.total_tasks_completed for agv in self.agvs),
            'total_wait_time': sum(agv.total_wait_time for agv in self.agvs),
            'total_collisions': sum(agv.collision_count for agv in self.agvs),
            'average_load': np.mean([agv.current_load for agv in self.agvs])
        }
    
    def _update_statistics(self):
        """更新统计信息"""
        self.episode_stats['total_reward'] = self.episode_reward
        self.episode_stats['tasks_completed'] = self.task_manager.total_tasks_completed
        self.episode_stats['tasks_failed'] = self.task_manager.total_tasks_failed
        self.episode_stats['collisions'] = self.collision_detector.total_collisions
        self.episode_stats['total_distance'] = sum(agv.total_distance for agv in self.agvs)
        
        if self.num_tasks > 0:
            self.episode_stats['completion_rate'] = (
                self.task_manager.total_tasks_completed / self.num_tasks
            )
    
    def render(self, mode: Optional[str] = None):
        """渲染环境"""
        if mode is None:
            mode = self.render_mode
        
        if mode == 'human':
            if self.visualizer is None:
                self.visualizer = WarehouseVisualizer(self.grid_world)
                self.visualizer.setup_figure()
            
            self.visualizer.update_display(
                self.agvs, self.task_manager, step=self.current_step
            )
            self.visualizer.show(block=False)
            
        elif mode == 'rgb_array':
            if self.visualizer is None:
                self.visualizer = WarehouseVisualizer(self.grid_world)
                self.visualizer.setup_figure()
            
            self.visualizer.update_display(
                self.agvs, self.task_manager, step=self.current_step
            )
            return self.visualizer._capture_frame()
    
    def close(self):
        """关闭环境"""
        if self.visualizer is not None:
            self.visualizer.close()
            self.visualizer = None
    
    def get_action_meanings(self) -> List[str]:
        """获取动作含义"""
        return [action.name for action in AGVAction]
    
    def seed(self, seed: Optional[int] = None):
        """设置随机种子"""
        np.random.seed(seed)
        return [seed]

    def get_wrapper_attr(self, name: str):
        """获取包装器属性（Gymnasium兼容性）"""
        return getattr(self, name)

    def action_space_sample(self, mask: Optional[np.ndarray] = None) -> np.ndarray:
        """
        采样动作（支持动作掩码）

        Args:
            mask: 动作掩码 [num_agents, action_dim]

        Returns:
            actions: 采样的动作 [num_agents]
        """
        actions = []

        for i in range(self.num_agvs):
            if mask is not None and mask.shape[0] > i:
                # 使用掩码采样
                valid_actions = np.where(mask[i] > 0)[0]
                if len(valid_actions) > 0:
                    action = np.random.choice(valid_actions)
                else:
                    action = 4  # 默认等待动作
            else:
                # 随机采样
                action = self.action_space.sample()

            actions.append(action)

        return np.array(actions)

    def get_env_info(self) -> Dict[str, Any]:
        """
        获取环境信息（用于算法配置）

        Returns:
            env_info: 环境信息字典
        """
        return {
            'num_agents': self.num_agvs,
            'num_tasks': self.num_tasks,
            'map_size': (self.map_width, self.map_height),
            'action_space': self.action_space,
            'observation_space': self.observation_space,
            'max_episode_steps': self.max_episode_steps,
            'reward_range': (-float('inf'), float('inf')),
            'action_meanings': self.get_action_meanings(),
            'state_shape': self._get_state_shape(),
            'obs_shape': self._get_obs_shape(),
            'n_actions': self.action_space.nvec[0] if hasattr(self.action_space, 'nvec') else self.action_space.n
        }

    def _get_state_shape(self) -> Tuple[int, ...]:
        """获取全局状态形状"""
        # 全局状态包含所有AGV状态、任务状态和地图状态
        agv_state_size = self.num_agvs * 10  # 每个AGV 10维状态
        task_state_size = self.num_tasks * 11  # 每个任务 11维状态
        map_state_size = self.map_width * self.map_height  # 地图状态

        return (agv_state_size + task_state_size + map_state_size,)

    def _get_obs_shape(self) -> Dict[str, Tuple[int, ...]]:
        """获取观察形状"""
        return {
            'agv_states': (self.num_agvs, 10),
            'task_states': (self.num_tasks, 11),
            'global_state': (self.map_height, self.map_width),
            'action_masks': (self.num_agvs, 7)
        }

    def get_state(self) -> np.ndarray:
        """
        获取全局状态（用于中心化训练）

        Returns:
            state: 全局状态向量
        """
        # AGV状态
        agv_states = np.array([agv.get_state_vector() for agv in self.agvs]).flatten()

        # 任务状态
        task_states = self.task_manager.get_all_tasks_state().flatten()

        # 地图状态
        map_state = self.grid_world.to_array().flatten()

        # 拼接所有状态
        global_state = np.concatenate([agv_states, task_states, map_state])

        return global_state.astype(np.float32)

    def get_avail_actions(self) -> np.ndarray:
        """
        获取可用动作掩码

        Returns:
            avail_actions: 可用动作掩码 [num_agents, n_actions]
        """
        # 需要导入动作掩码生成器
        from agents.action_masking import ActionMaskGenerator

        if not hasattr(self, '_mask_generator'):
            self._mask_generator = ActionMaskGenerator(
                self.grid_world, self.collision_detector
            )

        return self._mask_generator.generate_batch_masks(self.agvs, self.task_manager)

    def get_obs_agent(self, agent_id: int) -> Dict[str, np.ndarray]:
        """
        获取单个智能体的观察

        Args:
            agent_id: 智能体ID

        Returns:
            obs: 智能体观察
        """
        if agent_id >= self.num_agvs:
            raise ValueError(f"智能体ID {agent_id} 超出范围 [0, {self.num_agvs-1}]")

        obs = self._get_observation()

        return {
            'agv_state': obs['agv_states'][agent_id],
            'task_states': obs['task_states'],
            'global_state': obs['global_state'],
            'action_mask': obs['action_masks'][agent_id]
        }

    def get_obs_size(self) -> int:
        """获取观察大小（展平后）"""
        obs_shapes = self._get_obs_shape()
        total_size = 0

        for key, shape in obs_shapes.items():
            if key == 'agv_states':
                total_size += shape[1]  # 单个AGV的状态大小
            elif key == 'task_states':
                total_size += np.prod(shape)
            elif key == 'global_state':
                total_size += np.prod(shape)
            elif key == 'action_masks':
                total_size += shape[1]  # 单个AGV的动作掩码大小

        return total_size

    def get_state_size(self) -> int:
        """获取全局状态大小"""
        return self._get_state_shape()[0]

    def save_replay(self, filepath: str):
        """
        保存回放数据

        Args:
            filepath: 保存路径
        """
        replay_data = {
            'config': self.config.__dict__ if hasattr(self.config, '__dict__') else {},
            'episode_stats': self.episode_stats,
            'current_step': self.current_step,
            'agv_states': [agv.get_state_dict() for agv in self.agvs],
            'task_stats': self.task_manager.get_task_statistics(),
            'collision_stats': self.collision_detector.get_collision_statistics()
        }

        import pickle
        with open(filepath, 'wb') as f:
            pickle.dump(replay_data, f)

    def load_replay(self, filepath: str):
        """
        加载回放数据

        Args:
            filepath: 文件路径
        """
        import pickle
        with open(filepath, 'rb') as f:
            replay_data = pickle.load(f)

        # 恢复环境状态
        self.current_step = replay_data.get('current_step', 0)
        self.episode_stats = replay_data.get('episode_stats', {})

        return replay_data

    def get_env_stats(self) -> Dict[str, Any]:
        """
        获取环境统计信息

        Returns:
            stats: 统计信息字典
        """
        return {
            'episode_stats': self.episode_stats.copy(),
            'agv_stats': self._get_agv_statistics(),
            'task_stats': self.task_manager.get_task_statistics(),
            'collision_stats': self.collision_detector.get_collision_statistics(),
            'environment_stats': {
                'current_step': self.current_step,
                'total_agvs': self.num_agvs,
                'total_tasks': self.num_tasks,
                'map_size': f"{self.map_width}x{self.map_height}",
                'total_shelves': len(self.grid_world.shelves),
                'free_spaces': len(self.grid_world.free_spaces)
            }
        }

    def reset_stats(self):
        """重置统计信息"""
        self.episode_stats = {
            'total_reward': 0.0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'collisions': 0,
            'total_distance': 0,
            'completion_rate': 0.0
        }
        self.collision_detector.reset_statistics()

    def is_terminal(self) -> bool:
        """检查是否到达终止状态"""
        return self.done

    def get_total_actions(self) -> int:
        """获取总动作数"""
        return 7  # AGV有7种可能的动作


class MultiAgentWarehouseEnv(WarehouseEnv):
    """多智能体仓储环境（标准多智能体接口）"""

    def __init__(self, config: Optional[Config] = None):
        """初始化多智能体环境"""
        super().__init__(config)

        # 智能体ID列表
        self.agent_ids = [f"agv_{i}" for i in range(self.num_agvs)]
        self.possible_agents = self.agent_ids.copy()
        self.agents = self.agent_ids.copy()

        # 为每个智能体定义观察和动作空间
        self._agent_obs_spaces = {}
        self._agent_action_spaces = {}

        for agent_id in self.agent_ids:
            self._agent_obs_spaces[agent_id] = spaces.Dict({
                'agv_state': spaces.Box(
                    low=-np.inf, high=np.inf, shape=(10,), dtype=np.float32
                ),
                'task_states': spaces.Box(
                    low=-np.inf, high=np.inf, shape=(self.num_tasks, 11), dtype=np.float32
                ),
                'global_state': spaces.Box(
                    low=0, high=10, shape=(self.map_height, self.map_width), dtype=np.float32
                ),
                'action_mask': spaces.Box(
                    low=0, high=1, shape=(7,), dtype=np.float32
                )
            })
            self._agent_action_spaces[agent_id] = spaces.Discrete(7)

    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[Dict, Dict]:
        """重置环境（多智能体版本）"""
        obs, info = super().reset(seed=seed, options=options)

        # 转换为多智能体格式
        multi_obs = {}
        multi_info = {}

        for i, agent_id in enumerate(self.agent_ids):
            multi_obs[agent_id] = {
                'agv_state': obs['agv_states'][i],
                'task_states': obs['task_states'],
                'global_state': obs['global_state'],
                'action_mask': obs['action_masks'][i]
            }
            multi_info[agent_id] = info.copy()

        return multi_obs, multi_info

    def step(self, actions: Dict[str, int]) -> Tuple[Dict, Dict, Dict, Dict, Dict]:
        """执行动作（多智能体版本）"""
        # 转换动作格式
        action_list = [actions[agent_id] for agent_id in self.agent_ids]

        # 执行动作
        obs, reward, terminated, truncated, info = super().step(action_list)

        # 转换为多智能体格式
        multi_obs = {}
        multi_rewards = {}
        multi_terminated = {}
        multi_truncated = {}
        multi_info = {}

        for i, agent_id in enumerate(self.agent_ids):
            multi_obs[agent_id] = {
                'agv_state': obs['agv_states'][i],
                'task_states': obs['task_states'],
                'global_state': obs['global_state'],
                'action_mask': obs['action_masks'][i]
            }
            multi_rewards[agent_id] = reward  # 共享奖励
            multi_terminated[agent_id] = terminated
            multi_truncated[agent_id] = truncated
            multi_info[agent_id] = info.copy()

        return multi_obs, multi_rewards, multi_terminated, multi_truncated, multi_info

    def observation_space_for_agent(self, agent: str) -> spaces.Space:
        """获取智能体观察空间"""
        return self._agent_obs_spaces[agent]

    def action_space_for_agent(self, agent: str) -> spaces.Space:
        """获取智能体动作空间"""
        return self._agent_action_spaces[agent]

    def observe(self, agent: str) -> Dict[str, np.ndarray]:
        """获取智能体观察"""
        agent_idx = int(agent.split('_')[1])
        return self.get_obs_agent(agent_idx)

    def state(self) -> np.ndarray:
        """获取全局状态"""
        return self.get_state()


class VectorizedWarehouseEnv:
    """向量化仓储环境（支持并行环境）"""

    def __init__(self,
                 num_envs: int,
                 config: Optional[Config] = None,
                 async_envs: bool = False):
        """
        初始化向量化环境

        Args:
            num_envs: 环境数量
            config: 环境配置
            async_envs: 是否使用异步环境
        """
        self.num_envs = num_envs
        self.config = config or Config()
        self.async_envs = async_envs

        # 创建环境列表
        self.envs = [WarehouseEnv(config) for _ in range(num_envs)]

        # 环境属性
        self.observation_space = self.envs[0].observation_space
        self.action_space = self.envs[0].action_space
        self.num_agvs = self.envs[0].num_agvs

        # 状态跟踪
        self.dones = [False] * num_envs

    def reset(self, seed: Optional[List[int]] = None) -> Tuple[List[Dict], List[Dict]]:
        """重置所有环境"""
        if seed is None:
            seed = [None] * self.num_envs

        obs_list = []
        info_list = []

        for i, env in enumerate(self.envs):
            obs, info = env.reset(seed=seed[i])
            obs_list.append(obs)
            info_list.append(info)
            self.dones[i] = False

        return obs_list, info_list

    def step(self, actions: List[np.ndarray]) -> Tuple[List[Dict], List[float], List[bool], List[bool], List[Dict]]:
        """执行动作"""
        obs_list = []
        reward_list = []
        terminated_list = []
        truncated_list = []
        info_list = []

        for i, (env, action) in enumerate(zip(self.envs, actions)):
            if not self.dones[i]:
                obs, reward, terminated, truncated, info = env.step(action)
                self.dones[i] = terminated or truncated
            else:
                # 环境已结束，返回最后的观察
                obs = env._get_observation()
                reward = 0.0
                terminated = True
                truncated = False
                info = {}

            obs_list.append(obs)
            reward_list.append(reward)
            terminated_list.append(terminated)
            truncated_list.append(truncated)
            info_list.append(info)

        return obs_list, reward_list, terminated_list, truncated_list, info_list

    def render(self, mode: str = "human", env_idx: int = 0):
        """渲染指定环境"""
        if 0 <= env_idx < self.num_envs:
            return self.envs[env_idx].render(mode)

    def close(self):
        """关闭所有环境"""
        for env in self.envs:
            env.close()

    def get_env_stats(self) -> List[Dict]:
        """获取所有环境的统计信息"""
        return [env.get_env_stats() for env in self.envs]


def make_warehouse_env(config: Optional[Dict] = None,
                      env_type: str = "single",
                      **kwargs) -> Union[WarehouseEnv, MultiAgentWarehouseEnv, VectorizedWarehouseEnv]:
    """
    环境工厂函数

    Args:
        config: 环境配置
        env_type: 环境类型 ("single", "multi_agent", "vectorized")
        **kwargs: 额外参数

    Returns:
        env: 创建的环境实例
    """
    if isinstance(config, dict):
        from utils.config import Config
        # 创建Config对象并设置属性
        config_obj = Config()
        for key, value in config.items():
            if hasattr(config_obj, key):
                if isinstance(value, dict):
                    # 处理嵌套字典
                    nested_obj = getattr(config_obj, key)
                    for nested_key, nested_value in value.items():
                        setattr(nested_obj, nested_key, nested_value)
                else:
                    setattr(config_obj, key, value)
        config = config_obj

    if env_type == "single":
        return WarehouseEnv(config)
    elif env_type == "multi_agent":
        return MultiAgentWarehouseEnv(config)
    elif env_type == "vectorized":
        num_envs = kwargs.get('num_envs', 4)
        async_envs = kwargs.get('async_envs', False)
        return VectorizedWarehouseEnv(num_envs, config, async_envs)
    else:
        raise ValueError(f"未知的环境类型: {env_type}")


# 注册环境到Gymnasium
def register_warehouse_envs():
    """注册仓储环境到Gymnasium"""
    try:
        import gymnasium as gym

        # 注册单智能体环境
        gym.register(
            id='WarehouseEnv-v0',
            entry_point='envs.warehouse_env:WarehouseEnv',
            max_episode_steps=1000,
        )

        # 注册多智能体环境
        gym.register(
            id='MultiAgentWarehouseEnv-v0',
            entry_point='envs.warehouse_env:MultiAgentWarehouseEnv',
            max_episode_steps=1000,
        )

        print("✅ 仓储环境已注册到Gymnasium")

    except ImportError:
        print("⚠️ Gymnasium未安装，跳过环境注册")
    except Exception as e:
        print(f"⚠️ 环境注册失败: {e}")
