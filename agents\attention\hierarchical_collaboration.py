"""
层次化协作注意力机制

实现近距离、中距离、远距离的层次化协作注意力机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, Tuple, Optional, List, Union
from enum import Enum

from .relative_position_encoding import RelativePositionEncoding, create_relative_position_encoding


class CollaborationLevel(Enum):
    """协作层次枚举"""
    CLOSE = "close"          # 近距离协作 (0-5米)
    MEDIUM = "medium"        # 中距离协作 (5-15米)
    FAR = "far"             # 远距离协作 (15米以上)
    GLOBAL = "global"       # 全局协作


class HierarchicalCollaborationAttention(nn.Module):
    """层次化协作注意力机制"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 distance_thresholds: List[float] = [5.0, 15.0, 30.0],
                 collaboration_types: List[str] = ["close", "medium", "far"],
                 use_relative_position: bool = True,
                 fusion_method: str = "adaptive_weighted",
                 dropout: float = 0.1):
        """
        初始化层次化协作注意力机制
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            distance_thresholds: 距离阈值列表
            collaboration_types: 协作类型列表
            use_relative_position: 是否使用相对位置编码
            fusion_method: 融合方法
            dropout: Dropout概率
        """
        super(HierarchicalCollaborationAttention, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.distance_thresholds = distance_thresholds
        self.collaboration_types = collaboration_types
        self.num_levels = len(collaboration_types)
        self.use_relative_position = use_relative_position
        self.fusion_method = fusion_method
        
        # 为每个协作层次创建注意力层
        self.collaboration_layers = nn.ModuleDict()
        for collab_type in collaboration_types:
            self.collaboration_layers[collab_type] = CollaborationAttentionLayer(
                embed_dim=embed_dim,
                num_heads=num_heads,
                collaboration_level=CollaborationLevel(collab_type),
                dropout=dropout
            )
        
        # 相对位置编码
        if use_relative_position:
            self.position_encoding = create_relative_position_encoding(
                encoding_type="directional",
                embed_dim=embed_dim,
                max_distance=max(distance_thresholds)
            )
        
        # 层次融合机制
        if fusion_method == "adaptive_weighted":
            self.level_weight_predictor = nn.Sequential(
                nn.Linear(embed_dim * 2, embed_dim // 2),  # AGV状态 + 环境上下文
                nn.ReLU(),
                nn.Linear(embed_dim // 2, self.num_levels),
                nn.Softmax(dim=-1)
            )
        elif fusion_method == "gated":
            self.fusion_gates = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(embed_dim * 2, embed_dim),
                    nn.Sigmoid()
                ) for _ in range(self.num_levels)
            ])
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(embed_dim * self.num_levels, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 输出投影
        self.output_projection = nn.Linear(embed_dim, embed_dim)
    
    def compute_distance_masks(self, 
                             agv_positions: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算不同距离层次的掩码
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            
        Returns:
            distance_masks: 距离掩码字典
        """
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算距离矩阵
        pos_i = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        pos_j = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        distances = torch.norm(pos_j - pos_i, dim=-1)  # [batch_size, num_agvs, num_agvs]
        
        distance_masks = {}
        
        for i, collab_type in enumerate(self.collaboration_types):
            if i == 0:
                # 近距离：0 到第一个阈值
                mask = (distances <= self.distance_thresholds[i])
            elif i == len(self.collaboration_types) - 1:
                # 远距离：最后一个阈值以上
                mask = (distances > self.distance_thresholds[i-1])
            else:
                # 中距离：在两个阈值之间
                mask = ((distances > self.distance_thresholds[i-1]) & 
                       (distances <= self.distance_thresholds[i]))
            
            # 排除自己
            mask = mask & (torch.eye(num_agvs, device=distances.device).unsqueeze(0) == 0)
            distance_masks[collab_type] = mask.float()
        
        return distance_masks
    
    def compute_environment_context(self, 
                                  agv_embeddings: torch.Tensor,
                                  agv_positions: torch.Tensor) -> torch.Tensor:
        """
        计算环境上下文
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            
        Returns:
            env_context: 环境上下文 [batch_size, num_agvs, embed_dim]
        """
        batch_size, num_agvs, _ = agv_embeddings.shape
        
        # 计算局部密度
        pos_i = agv_positions.unsqueeze(2)
        pos_j = agv_positions.unsqueeze(1)
        distances = torch.norm(pos_j - pos_i, dim=-1)
        
        # 在5米范围内的AGV数量
        local_density = torch.sum((distances < 5.0) & (distances > 0), dim=-1, keepdim=True)
        
        # 计算平均距离到其他AGV
        mask = distances > 0
        avg_distance = torch.sum(distances * mask, dim=-1, keepdim=True) / (torch.sum(mask, dim=-1, keepdim=True) + 1e-8)
        
        # 计算分散程度
        center = torch.mean(agv_positions, dim=1, keepdim=True)  # [batch_size, 1, 2]
        distances_to_center = torch.norm(agv_positions - center, dim=-1, keepdim=True)
        
        # 组合环境特征
        env_features = torch.cat([
            local_density.float(),
            avg_distance / 30.0,  # 归一化
            distances_to_center / 30.0  # 归一化
        ], dim=-1)  # [batch_size, num_agvs, 3]

        # 扩展环境特征到嵌入维度
        if env_features.shape[-1] < self.embed_dim:
            # 重复环境特征以匹配嵌入维度
            repeat_times = self.embed_dim // env_features.shape[-1]
            remainder = self.embed_dim % env_features.shape[-1]

            repeated_features = env_features.repeat(1, 1, repeat_times)
            if remainder > 0:
                repeated_features = torch.cat([
                    repeated_features,
                    env_features[:, :, :remainder]
                ], dim=-1)

            env_context = repeated_features
        else:
            env_context = env_features[:, :, :self.embed_dim]
        
        return env_context
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: Optional[torch.Tensor] = None,
                agv_states: Optional[torch.Tensor] = None,
                global_context: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            agv_states: AGV状态 [batch_size, num_agvs, state_dim]
            global_context: 全局上下文 [batch_size, embed_dim]
            
        Returns:
            output_dict: 输出字典
        """
        batch_size, num_agvs, embed_dim = agv_embeddings.shape
        
        # 计算距离掩码
        distance_masks = self.compute_distance_masks(agv_positions)
        
        # 计算相对位置编码
        if self.use_relative_position:
            position_encodings = self.position_encoding(
                agv_positions, agv_velocities, agv_states
            )
        else:
            position_encodings = torch.zeros(
                batch_size, num_agvs, num_agvs, embed_dim,
                device=agv_embeddings.device
            )
        
        # 计算环境上下文
        env_context = self.compute_environment_context(agv_embeddings, agv_positions)
        
        # 各层次协作注意力计算
        level_outputs = {}
        level_attention_weights = {}
        
        for collab_type in self.collaboration_types:
            mask = distance_masks[collab_type]
            
            # 层次特定的协作注意力
            level_output, level_attention = self.collaboration_layers[collab_type](
                agv_embeddings=agv_embeddings,
                position_encodings=position_encodings,
                attention_mask=mask,
                env_context=env_context,
                global_context=global_context
            )
            
            level_outputs[collab_type] = level_output
            level_attention_weights[collab_type] = level_attention
        
        # 层次融合
        if self.fusion_method == "adaptive_weighted":
            # 自适应加权融合
            fusion_input = torch.cat([agv_embeddings, env_context], dim=-1)
            level_weights = self.level_weight_predictor(fusion_input)  # [batch_size, num_agvs, num_levels]
            
            fused_output = torch.zeros_like(agv_embeddings)
            for i, collab_type in enumerate(self.collaboration_types):
                weight = level_weights[:, :, i:i+1]  # [batch_size, num_agvs, 1]
                fused_output += weight * level_outputs[collab_type]
                
        elif self.fusion_method == "gated":
            # 门控融合
            fused_output = torch.zeros_like(agv_embeddings)
            gate_input = torch.cat([agv_embeddings, env_context], dim=-1)
            
            for i, collab_type in enumerate(self.collaboration_types):
                gate = self.fusion_gates[i](gate_input)
                fused_output += gate * level_outputs[collab_type]
                
        else:
            # 简单拼接融合
            concatenated = torch.cat(list(level_outputs.values()), dim=-1)
            fused_output = self.final_fusion(concatenated)
        
        # 最终输出投影
        final_output = self.output_projection(fused_output)
        
        return {
            'collaboration_output': final_output,
            'level_outputs': level_outputs,
            'level_attention_weights': level_attention_weights,
            'distance_masks': distance_masks,
            'environment_context': env_context,
            'level_weights': level_weights if self.fusion_method == "adaptive_weighted" else None
        }


class CollaborationAttentionLayer(nn.Module):
    """单层协作注意力层"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 collaboration_level: CollaborationLevel = CollaborationLevel.CLOSE,
                 dropout: float = 0.1):
        """
        初始化协作注意力层
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            collaboration_level: 协作层次
            dropout: Dropout概率
        """
        super(CollaborationAttentionLayer, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.collaboration_level = collaboration_level
        
        assert self.head_dim * num_heads == embed_dim, "embed_dim必须能被num_heads整除"
        
        # 查询、键、值投影
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        
        # 位置编码投影
        self.pos_proj = nn.Linear(embed_dim, embed_dim)
        
        # 协作特定的参数
        if collaboration_level == CollaborationLevel.CLOSE:
            # 近距离协作：关注精确协调
            self.temperature = 0.5  # 更尖锐的注意力分布
            self.collaboration_weight = 1.0
        elif collaboration_level == CollaborationLevel.MEDIUM:
            # 中距离协作：关注路径协调
            self.temperature = 1.0
            self.collaboration_weight = 0.8
        elif collaboration_level == CollaborationLevel.FAR:
            # 远距离协作：关注全局协调
            self.temperature = 2.0  # 更平滑的注意力分布
            self.collaboration_weight = 0.6
        else:
            self.temperature = 1.0
            self.collaboration_weight = 1.0
        
        # 输出投影
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(embed_dim)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 协作增强网络
        self.collaboration_enhancer = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, embed_dim),
            nn.Sigmoid()
        )
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                position_encodings: torch.Tensor,
                attention_mask: torch.Tensor,
                env_context: Optional[torch.Tensor] = None,
                global_context: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            position_encodings: 位置编码 [batch_size, num_agvs, num_agvs, embed_dim]
            attention_mask: 注意力掩码 [batch_size, num_agvs, num_agvs]
            env_context: 环境上下文 [batch_size, num_agvs, embed_dim]
            global_context: 全局上下文 [batch_size, embed_dim]
            
        Returns:
            output: 输出 [batch_size, num_agvs, embed_dim]
            attention_weights: 注意力权重 [batch_size, num_heads, num_agvs, num_agvs]
        """
        batch_size, num_agvs, embed_dim = agv_embeddings.shape
        
        # 计算查询、键、值
        Q = self.q_proj(agv_embeddings)  # [batch_size, num_agvs, embed_dim]
        K = self.k_proj(agv_embeddings)  # [batch_size, num_agvs, embed_dim]
        V = self.v_proj(agv_embeddings)  # [batch_size, num_agvs, embed_dim]
        
        # 添加位置编码到键
        pos_encoding = self.pos_proj(position_encodings)  # [batch_size, num_agvs, num_agvs, embed_dim]
        K_with_pos = K.unsqueeze(1) + pos_encoding  # [batch_size, num_agvs, num_agvs, embed_dim]
        
        # 重塑为多头格式
        Q = Q.view(batch_size, num_agvs, self.num_heads, self.head_dim).transpose(1, 2)
        K_with_pos = K_with_pos.view(batch_size, num_agvs, num_agvs, self.num_heads, self.head_dim).permute(0, 3, 1, 2, 4)
        V = V.view(batch_size, num_agvs, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        attention_scores = torch.einsum('bhid,bhjid->bhij', Q, K_with_pos) / (math.sqrt(self.head_dim) * self.temperature)
        
        # 应用注意力掩码
        if attention_mask is not None:
            mask_expanded = attention_mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
            attention_scores = attention_scores.masked_fill(mask_expanded == 0, float('-inf'))
        
        # 计算注意力权重
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        attended_values = torch.matmul(attention_weights, V)  # [batch_size, num_heads, num_agvs, head_dim]
        
        # 重塑输出
        attended_values = attended_values.transpose(1, 2).contiguous().view(
            batch_size, num_agvs, embed_dim
        )
        
        # 协作增强
        if env_context is not None:
            enhancement_input = torch.cat([attended_values, env_context], dim=-1)
            enhancement_gate = self.collaboration_enhancer(enhancement_input)
            attended_values = attended_values * enhancement_gate
        
        # 输出投影
        output = self.out_proj(attended_values)
        
        # 残差连接和层归一化
        output = self.layer_norm(output + agv_embeddings * self.collaboration_weight)
        
        return output, attention_weights


class AdaptiveHierarchicalCollaboration(HierarchicalCollaborationAttention):
    """自适应层次化协作注意力"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads: int = 8,
                 distance_thresholds: List[float] = [5.0, 15.0, 30.0],
                 collaboration_types: List[str] = ["close", "medium", "far"],
                 adaptation_method: str = "learned",
                 dropout: float = 0.1):
        """
        初始化自适应层次化协作注意力
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            distance_thresholds: 初始距离阈值
            collaboration_types: 协作类型列表
            adaptation_method: 自适应方法
            dropout: Dropout概率
        """
        super().__init__(embed_dim, num_heads, distance_thresholds, collaboration_types, 
                        True, "adaptive_weighted", dropout)
        
        self.adaptation_method = adaptation_method
        self.initial_thresholds = distance_thresholds.copy()
        
        if adaptation_method == "learned":
            # 学习的阈值调整
            self.threshold_predictor = nn.Sequential(
                nn.Linear(embed_dim + 3, embed_dim // 2),  # AGV状态 + 环境特征
                nn.ReLU(),
                nn.Linear(embed_dim // 2, len(distance_thresholds)),
                nn.Sigmoid()
            )
        
        # 动态权重调整
        self.dynamic_weight_adjuster = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Linear(embed_dim, len(collaboration_types)),
            nn.Softmax(dim=-1)
        )
    
    def adapt_distance_thresholds(self, 
                                agv_embeddings: torch.Tensor,
                                agv_positions: torch.Tensor,
                                env_features: torch.Tensor) -> List[float]:
        """
        自适应调整距离阈值
        
        Args:
            agv_embeddings: AGV嵌入
            agv_positions: AGV位置
            env_features: 环境特征
            
        Returns:
            adapted_thresholds: 调整后的距离阈值
        """
        if self.adaptation_method == "learned":
            # 计算全局特征
            global_agv_features = torch.mean(agv_embeddings, dim=1)  # [batch_size, embed_dim]
            global_env_features = torch.mean(env_features, dim=1)    # [batch_size, 3]
            
            adaptation_input = torch.cat([global_agv_features, global_env_features], dim=-1)
            threshold_adjustments = self.threshold_predictor(adaptation_input)  # [batch_size, num_thresholds]
            
            # 调整阈值（在原始阈值的0.5-2.0倍范围内）
            threshold_multipliers = 0.5 + 1.5 * threshold_adjustments
            adapted_thresholds = []
            
            for i, base_threshold in enumerate(self.initial_thresholds):
                multiplier = threshold_multipliers[0, i].item()  # 使用第一个batch的值
                adapted_thresholds.append(base_threshold * multiplier)
            
            return adapted_thresholds
        
        else:
            # 基于环境密度的简单自适应
            avg_density = torch.mean(env_features[:, :, 0])  # 平均局部密度
            
            if avg_density > 2.0:  # 高密度环境
                scale_factor = 0.8
            elif avg_density < 1.0:  # 低密度环境
                scale_factor = 1.2
            else:
                scale_factor = 1.0
            
            return [threshold * scale_factor for threshold in self.initial_thresholds]
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: Optional[torch.Tensor] = None,
                agv_states: Optional[torch.Tensor] = None,
                global_context: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播（自适应版本）
        """
        # 计算环境特征
        env_context = self.compute_environment_context(agv_embeddings, agv_positions)
        
        # 提取环境特征用于自适应
        batch_size, num_agvs, _ = agv_embeddings.shape
        pos_i = agv_positions.unsqueeze(2)
        pos_j = agv_positions.unsqueeze(1)
        distances = torch.norm(pos_j - pos_i, dim=-1)
        
        local_density = torch.sum((distances < 5.0) & (distances > 0), dim=-1, keepdim=True)
        avg_distance = torch.sum(distances * (distances > 0), dim=-1, keepdim=True) / (torch.sum(distances > 0, dim=-1, keepdim=True) + 1e-8)
        center = torch.mean(agv_positions, dim=1, keepdim=True)
        distances_to_center = torch.norm(agv_positions - center, dim=-1, keepdim=True)
        
        env_features = torch.cat([local_density.float(), avg_distance / 30.0, distances_to_center / 30.0], dim=-1)
        
        # 自适应调整距离阈值
        adapted_thresholds = self.adapt_distance_thresholds(agv_embeddings, agv_positions, env_features)
        
        # 临时更新阈值
        original_thresholds = self.distance_thresholds.copy()
        self.distance_thresholds = adapted_thresholds
        
        # 调用父类方法
        output_dict = super().forward(agv_embeddings, agv_positions, agv_velocities, agv_states, global_context)
        
        # 恢复原始阈值
        self.distance_thresholds = original_thresholds
        
        # 添加自适应信息到输出
        output_dict['adapted_thresholds'] = adapted_thresholds
        output_dict['env_features'] = env_features
        
        return output_dict


class MultiScaleCollaborationAttention(nn.Module):
    """多尺度协作注意力"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 num_heads_list: List[int] = [4, 8, 16],
                 distance_thresholds: List[float] = [5.0, 15.0, 30.0],
                 scale_factors: List[float] = [0.5, 1.0, 2.0],
                 dropout: float = 0.1):
        """
        初始化多尺度协作注意力
        
        Args:
            embed_dim: 嵌入维度
            num_heads_list: 不同尺度的注意力头数
            distance_thresholds: 距离阈值
            scale_factors: 尺度因子
            dropout: Dropout概率
        """
        super(MultiScaleCollaborationAttention, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_scales = len(scale_factors)
        self.scale_factors = scale_factors
        
        # 为每个尺度创建协作注意力
        self.scale_attentions = nn.ModuleList([
            HierarchicalCollaborationAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                distance_thresholds=[t * scale for t in distance_thresholds],
                collaboration_types=["close", "medium", "far"],
                dropout=dropout
            ) for num_heads, scale in zip(num_heads_list, scale_factors)
        ])
        
        # 尺度融合
        self.scale_fusion = nn.Sequential(
            nn.Linear(embed_dim * self.num_scales, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 尺度权重
        self.scale_weights = nn.Parameter(torch.ones(self.num_scales) / self.num_scales)
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: Optional[torch.Tensor] = None,
                agv_states: Optional[torch.Tensor] = None,
                global_context: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        """
        scale_outputs = []
        scale_attention_weights = []
        
        # 计算每个尺度的协作注意力
        for scale_attention in self.scale_attentions:
            scale_result = scale_attention(
                agv_embeddings, agv_positions, agv_velocities, agv_states, global_context
            )
            scale_outputs.append(scale_result['collaboration_output'])
            scale_attention_weights.append(scale_result['level_attention_weights'])
        
        # 尺度融合
        scale_weights_normalized = F.softmax(self.scale_weights, dim=0)
        
        # 加权融合
        weighted_output = sum(
            weight * output for weight, output in zip(scale_weights_normalized, scale_outputs)
        )
        
        # 拼接融合
        concatenated_output = torch.cat(scale_outputs, dim=-1)
        fused_output = self.scale_fusion(concatenated_output)
        
        # 最终输出
        final_output = weighted_output + fused_output
        
        return {
            'collaboration_output': final_output,
            'scale_outputs': scale_outputs,
            'scale_attention_weights': scale_attention_weights,
            'scale_weights': scale_weights_normalized
        }
