"""
MAML元学习集成

集成Model-Agnostic Meta-Learning框架，实现快速适应能力
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from collections import deque, OrderedDict
import logging
from dataclasses import dataclass
import copy


@dataclass
class MAMLConfig:
    """MAML配置"""
    # 元学习参数
    meta_lr: float = 1e-3               # 元学习率
    inner_lr: float = 1e-2              # 内层学习率
    inner_steps: int = 5                # 内层更新步数
    meta_batch_size: int = 16           # 元批次大小
    
    # 任务采样
    task_batch_size: int = 32           # 任务批次大小
    support_size: int = 10              # 支持集大小
    query_size: int = 15                # 查询集大小
    
    # 适应参数
    adaptation_steps: int = 10          # 适应步数
    adaptation_lr: float = 1e-2         # 适应学习率
    
    # 正则化
    first_order: bool = False           # 是否使用一阶近似
    allow_unused: bool = True           # 允许未使用的参数
    allow_nograd: bool = True           # 允许无梯度参数


class MAMLTask:
    """MAML任务"""
    
    def __init__(self, 
                 task_id: str,
                 support_data: List[Tuple],
                 query_data: List[Tuple],
                 task_description: Optional[Dict] = None):
        """
        初始化MAML任务
        
        Args:
            task_id: 任务ID
            support_data: 支持集数据
            query_data: 查询集数据
            task_description: 任务描述
        """
        self.task_id = task_id
        self.support_data = support_data
        self.query_data = query_data
        self.task_description = task_description or {}
    
    def get_support_batch(self, batch_size: Optional[int] = None) -> Tuple:
        """获取支持集批次"""
        if batch_size is None:
            batch_size = len(self.support_data)
        
        indices = np.random.choice(len(self.support_data), 
                                 min(batch_size, len(self.support_data)), 
                                 replace=False)
        
        batch = [self.support_data[i] for i in indices]
        return self._collate_batch(batch)
    
    def get_query_batch(self, batch_size: Optional[int] = None) -> Tuple:
        """获取查询集批次"""
        if batch_size is None:
            batch_size = len(self.query_data)
        
        indices = np.random.choice(len(self.query_data), 
                                 min(batch_size, len(self.query_data)), 
                                 replace=False)
        
        batch = [self.query_data[i] for i in indices]
        return self._collate_batch(batch)
    
    def _collate_batch(self, batch: List[Tuple]) -> Tuple:
        """整理批次数据"""
        if not batch:
            return tuple()
        
        # 假设每个样本是 (state, action, reward, next_state, done) 的元组
        states = torch.stack([item[0] for item in batch])
        actions = torch.stack([item[1] for item in batch])
        rewards = torch.stack([item[2] for item in batch])
        next_states = torch.stack([item[3] for item in batch])
        dones = torch.stack([item[4] for item in batch])
        
        return states, actions, rewards, next_states, dones


class MAMLTaskSampler:
    """MAML任务采样器"""
    
    def __init__(self, 
                 config: MAMLConfig,
                 environment_configs: List[Dict],
                 logger: logging.Logger = None):
        """
        初始化任务采样器
        
        Args:
            config: MAML配置
            environment_configs: 环境配置列表
            logger: 日志记录器
        """
        self.config = config
        self.environment_configs = environment_configs
        self.logger = logger or logging.getLogger(__name__)
        
        # 任务历史
        self.task_history = deque(maxlen=1000)
        self.task_performance = {}
        
    def sample_tasks(self, num_tasks: int) -> List[MAMLTask]:
        """
        采样任务
        
        Args:
            num_tasks: 任务数量
            
        Returns:
            任务列表
        """
        tasks = []
        
        for i in range(num_tasks):
            # 随机选择环境配置
            env_config = np.random.choice(self.environment_configs)
            
            # 生成任务数据
            task = self._generate_task(f"task_{i}", env_config)
            tasks.append(task)
            
            self.task_history.append(task.task_id)
        
        return tasks
    
    def _generate_task(self, task_id: str, env_config: Dict) -> MAMLTask:
        """生成单个任务"""
        # 这里应该根据环境配置生成实际的任务数据
        # 为了演示，我们生成随机数据
        
        state_dim = env_config.get('state_dim', 64)
        action_dim = env_config.get('action_dim', 5)
        
        # 生成支持集
        support_data = []
        for _ in range(self.config.support_size):
            state = torch.randn(state_dim)
            action = torch.randint(0, action_dim, (1,)).float()
            reward = torch.randn(1)
            next_state = torch.randn(state_dim)
            done = torch.randint(0, 2, (1,)).float()
            
            support_data.append((state, action, reward, next_state, done))
        
        # 生成查询集
        query_data = []
        for _ in range(self.config.query_size):
            state = torch.randn(state_dim)
            action = torch.randint(0, action_dim, (1,)).float()
            reward = torch.randn(1)
            next_state = torch.randn(state_dim)
            done = torch.randint(0, 2, (1,)).float()
            
            query_data.append((state, action, reward, next_state, done))
        
        return MAMLTask(
            task_id=task_id,
            support_data=support_data,
            query_data=query_data,
            task_description=env_config
        )


class MAMLLearner:
    """MAML学习器"""
    
    def __init__(self, 
                 config: MAMLConfig,
                 policy_network: nn.Module,
                 value_network: nn.Module,
                 loss_function: Callable,
                 logger: logging.Logger = None):
        """
        初始化MAML学习器
        
        Args:
            config: MAML配置
            policy_network: 策略网络
            value_network: 价值网络
            loss_function: 损失函数
            logger: 日志记录器
        """
        self.config = config
        self.policy_network = policy_network
        self.value_network = value_network
        self.loss_function = loss_function
        self.logger = logger or logging.getLogger(__name__)
        
        # 元优化器
        self.meta_optimizer = optim.Adam(
            list(policy_network.parameters()) + list(value_network.parameters()),
            lr=config.meta_lr
        )
        
        # 统计信息
        self.meta_step = 0
        self.adaptation_history = deque(maxlen=100)
        self.meta_loss_history = deque(maxlen=100)
        
    def meta_update(self, tasks: List[MAMLTask]) -> Dict[str, float]:
        """
        执行元更新
        
        Args:
            tasks: 任务列表
            
        Returns:
            更新结果
        """
        self.meta_optimizer.zero_grad()
        
        meta_losses = []
        task_losses = []
        
        for task in tasks:
            # 内层适应
            adapted_policy, adapted_value, inner_loss = self._inner_adaptation(task)
            
            # 计算查询损失
            query_loss = self._compute_query_loss(task, adapted_policy, adapted_value)
            meta_losses.append(query_loss)
            task_losses.append(inner_loss)
        
        # 平均元损失
        meta_loss = torch.stack(meta_losses).mean()
        
        # 反向传播
        meta_loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(
            list(self.policy_network.parameters()) + list(self.value_network.parameters()),
            max_norm=1.0
        )
        
        # 元更新
        self.meta_optimizer.step()
        
        # 记录统计信息
        self.meta_step += 1
        self.meta_loss_history.append(meta_loss.item())
        
        result = {
            'meta_loss': meta_loss.item(),
            'avg_inner_loss': torch.stack(task_losses).mean().item(),
            'meta_step': self.meta_step
        }
        
        return result
    
    def _inner_adaptation(self, task: MAMLTask) -> Tuple[nn.Module, nn.Module, torch.Tensor]:
        """内层适应"""
        # 复制网络
        adapted_policy = copy.deepcopy(self.policy_network)
        adapted_value = copy.deepcopy(self.value_network)
        
        # 内层优化器
        inner_optimizer = optim.SGD(
            list(adapted_policy.parameters()) + list(adapted_value.parameters()),
            lr=self.config.inner_lr
        )
        
        total_inner_loss = 0.0
        
        # 内层更新步骤
        for step in range(self.config.inner_steps):
            inner_optimizer.zero_grad()
            
            # 获取支持集批次
            support_batch = task.get_support_batch(self.config.task_batch_size)
            
            # 计算损失
            inner_loss = self._compute_inner_loss(support_batch, adapted_policy, adapted_value)
            
            # 反向传播
            inner_loss.backward()
            inner_optimizer.step()
            
            total_inner_loss += inner_loss.item()
        
        avg_inner_loss = torch.tensor(total_inner_loss / self.config.inner_steps)
        
        return adapted_policy, adapted_value, avg_inner_loss
    
    def _compute_inner_loss(self, 
                          batch: Tuple,
                          policy_net: nn.Module,
                          value_net: nn.Module) -> torch.Tensor:
        """计算内层损失"""
        if not batch:
            return torch.tensor(0.0, requires_grad=True)
        
        states, actions, rewards, next_states, dones = batch
        
        # 策略损失（这里简化为MSE损失）
        policy_output = policy_net(states)
        policy_loss = nn.MSELoss()(policy_output, actions)
        
        # 价值损失
        values = value_net(states)
        next_values = value_net(next_states)
        targets = rewards + 0.99 * next_values * (1 - dones)
        value_loss = nn.MSELoss()(values, targets.detach())
        
        total_loss = policy_loss + value_loss
        
        return total_loss
    
    def _compute_query_loss(self, 
                          task: MAMLTask,
                          adapted_policy: nn.Module,
                          adapted_value: nn.Module) -> torch.Tensor:
        """计算查询损失"""
        # 获取查询集批次
        query_batch = task.get_query_batch(self.config.task_batch_size)
        
        if not query_batch:
            return torch.tensor(0.0, requires_grad=True)
        
        # 计算查询损失
        query_loss = self._compute_inner_loss(query_batch, adapted_policy, adapted_value)
        
        return query_loss
    
    def fast_adaptation(self, 
                       task: MAMLTask,
                       adaptation_steps: Optional[int] = None) -> Tuple[nn.Module, nn.Module]:
        """
        快速适应到新任务
        
        Args:
            task: 目标任务
            adaptation_steps: 适应步数
            
        Returns:
            适应后的网络
        """
        if adaptation_steps is None:
            adaptation_steps = self.config.adaptation_steps
        
        # 复制当前网络
        adapted_policy = copy.deepcopy(self.policy_network)
        adapted_value = copy.deepcopy(self.value_network)
        
        # 适应优化器
        adaptation_optimizer = optim.SGD(
            list(adapted_policy.parameters()) + list(adapted_value.parameters()),
            lr=self.config.adaptation_lr
        )
        
        adaptation_losses = []
        
        # 适应步骤
        for step in range(adaptation_steps):
            adaptation_optimizer.zero_grad()
            
            # 获取支持集批次
            support_batch = task.get_support_batch(self.config.task_batch_size)
            
            # 计算损失
            loss = self._compute_inner_loss(support_batch, adapted_policy, adapted_value)
            
            # 反向传播
            loss.backward()
            adaptation_optimizer.step()
            
            adaptation_losses.append(loss.item())
        
        # 记录适应历史
        self.adaptation_history.append({
            'task_id': task.task_id,
            'adaptation_losses': adaptation_losses,
            'final_loss': adaptation_losses[-1] if adaptation_losses else 0.0
        })
        
        self.logger.info(f"快速适应完成: 任务 {task.task_id}, 最终损失: {adaptation_losses[-1]:.4f}")
        
        return adapted_policy, adapted_value
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'meta_step': self.meta_step,
            'adaptation_count': len(self.adaptation_history)
        }
        
        if self.meta_loss_history:
            stats.update({
                'avg_meta_loss': np.mean(self.meta_loss_history),
                'meta_loss_trend': np.polyfit(range(len(self.meta_loss_history)), 
                                            list(self.meta_loss_history), 1)[0]
            })
        
        if self.adaptation_history:
            recent_adaptations = list(self.adaptation_history)[-10:]
            final_losses = [a['final_loss'] for a in recent_adaptations]
            stats.update({
                'avg_adaptation_loss': np.mean(final_losses),
                'adaptation_success_rate': np.mean([l < 1.0 for l in final_losses])
            })
        
        return stats
