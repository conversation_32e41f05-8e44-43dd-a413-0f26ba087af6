"""
注意力机制模块

包含：
- feature_embedding.py: 特征嵌入层
- sparse_attention.py: Top-K稀疏注意力机制
- constraint_enhancement.py: 约束增强机制
- temporal_consistency.py: 时序一致性约束
- multi_head_attention.py: 多头注意力机制
- attention_output.py: 注意力权重计算与输出
- task_allocation_attention.py: 第一层任务分配注意力机制
"""

from .feature_embedding import (
    AGVFeatureEmbedding, TaskFeatureEmbedding, UnifiedFeatureEmbedding
)
from .sparse_attention import (
    TopKSparseAttention, AdaptiveTopKSparseAttention, MultiScaleTopKAttention
)
from .constraint_enhancement import (
    ConstraintEnhancement, AdaptiveConstraintEnhancement
)
from .temporal_consistency import (
    TemporalConsistencyConstraint, AdaptiveTemporalConsistency
)
from .multi_head_attention import (
    MultiHeadAttention, TaskAllocationMultiHeadAttention,
    HierarchicalMultiHeadAttention, CrossAttentionModule
)
from .attention_output import (
    AttentionOutput, TaskAllocationOutput, AdaptiveAttentionOutput, MultiScaleAttentionOutput
)
from .task_allocation_attention import (
    TaskAllocationAttention, HierarchicalTaskAllocationAttention
)
from .relative_position_encoding import (
    RelativePositionEncoding, GridRelativePositionEncoding,
    DirectionalRelativePositionEncoding, create_relative_position_encoding
)
from .hierarchical_collaboration import (
    HierarchicalCollaborationAttention, AdaptiveHierarchicalCollaboration,
    MultiScaleCollaborationAttention, CollaborationAttentionLayer
)
from .adaptive_temperature import (
    AdaptiveTemperatureController, MultiLevelTemperatureController,
    EnvironmentComplexityEvaluator, ConflictDetector
)
from .collaboration_constraints import (
    CollaborationConstraintIntegrator, CollisionRiskCalculator,
    PathConflictCalculator, LoadBalanceCalculator, CollaborationHistoryTracker,
    SafetyDistanceCalculator, DeadlockDetector
)
from .collaboration_attention import (
    CollaborationAwareAttention, CollaborationQualityAssessor,
    MultiModalCollaborationAttention
)
from .collaboration_state_representation import (
    CollaborationStateRepresentation, IntentionPredictor,
    TemporalEncoder, RelativeStateEncoder, CollaborationContextEncoder,
    MultiModalStateRepresentation
)
from .collaboration_information_aggregation import (
    CollaborationInformationAggregator, DynamicWeightPredictor,
    AttentionFusion, GatedFusion, HierarchicalFusion, AdaptiveFusion,
    AggregationQualityAssessor
)
from .dual_layer_fusion import (
    DualLayerAttentionFusion, GatedFusionNetwork, WeightedFusionNetwork,
    AttentionFusionNetwork, HierarchicalFusionNetwork, AdaptiveFusionNetwork,
    EnvironmentComplexityEvaluator, AdaptiveWeightPredictor, FusionQualityAssessor
)
from .collaboration_state_representation import (
    CollaborationStateRepresentation, MultiModalStateRepresentation,
    IntentionPredictor, TemporalEncoder, RelativeStateEncoder,
    CollaborationContextEncoder, IntentionType
)
from .collaboration_information_aggregation import (
    CollaborationInformationAggregator, DynamicWeightPredictor,
    AttentionFusion, GatedFusion, HierarchicalFusion, AdaptiveFusion,
    AggregationQualityAssessor, AggregationMethod
)

__all__ = [
    # 特征嵌入
    'AGVFeatureEmbedding', 'TaskFeatureEmbedding', 'UnifiedFeatureEmbedding',

    # 稀疏注意力
    'TopKSparseAttention', 'AdaptiveTopKSparseAttention', 'MultiScaleTopKAttention',

    # 约束增强
    'ConstraintEnhancement', 'AdaptiveConstraintEnhancement',

    # 时序一致性
    'TemporalConsistencyConstraint', 'AdaptiveTemporalConsistency',

    # 多头注意力
    'MultiHeadAttention', 'TaskAllocationMultiHeadAttention',
    'HierarchicalMultiHeadAttention', 'CrossAttentionModule',

    # 注意力输出
    'AttentionOutput', 'TaskAllocationOutput', 'AdaptiveAttentionOutput', 'MultiScaleAttentionOutput',

    # 任务分配注意力
    'TaskAllocationAttention', 'HierarchicalTaskAllocationAttention',

    # 相对位置编码
    'RelativePositionEncoding', 'GridRelativePositionEncoding',
    'DirectionalRelativePositionEncoding', 'create_relative_position_encoding',

    # 层次化协作
    'HierarchicalCollaborationAttention', 'AdaptiveHierarchicalCollaboration',
    'MultiScaleCollaborationAttention', 'CollaborationAttentionLayer',

    # 自适应温度
    'AdaptiveTemperatureController', 'MultiLevelTemperatureController',
    'EnvironmentComplexityEvaluator', 'ConflictDetector',

    # 协作约束
    'CollaborationConstraintIntegrator', 'CollisionRiskCalculator',
    'PathConflictCalculator', 'LoadBalanceCalculator', 'CollaborationHistoryTracker',
    'SafetyDistanceCalculator', 'DeadlockDetector',

    # 协作感知注意力
    'CollaborationAwareAttention', 'CollaborationQualityAssessor',
    'MultiModalCollaborationAttention',

    # 协作状态表示
    'CollaborationStateRepresentation', 'IntentionPredictor',
    'TemporalEncoder', 'RelativeStateEncoder', 'CollaborationContextEncoder',
    'MultiModalStateRepresentation',

    # 协作信息聚合
    'CollaborationInformationAggregator', 'DynamicWeightPredictor',
    'AttentionFusion', 'GatedFusion', 'HierarchicalFusion', 'AdaptiveFusion',
    'AggregationQualityAssessor',

    # 双层注意力融合
    'DualLayerAttentionFusion', 'GatedFusionNetwork', 'WeightedFusionNetwork',
    'AttentionFusionNetwork', 'HierarchicalFusionNetwork', 'AdaptiveFusionNetwork',
    'EnvironmentComplexityEvaluator', 'AdaptiveWeightPredictor', 'FusionQualityAssessor',

    # 协作状态表示
    'CollaborationStateRepresentation', 'MultiModalStateRepresentation',
    'IntentionPredictor', 'TemporalEncoder', 'RelativeStateEncoder',
    'CollaborationContextEncoder', 'IntentionType',

    # 协作信息聚合
    'CollaborationInformationAggregator', 'DynamicWeightPredictor',
    'AttentionFusion', 'GatedFusion', 'HierarchicalFusion', 'AdaptiveFusion',
    'AggregationQualityAssessor', 'AggregationMethod'
]

__all__ = ['TaskAttention', 'CollabAttention', 'AttentionFusion']
