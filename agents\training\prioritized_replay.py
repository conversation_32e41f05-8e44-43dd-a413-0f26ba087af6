"""
优先级经验回放机制

基于多智能体TD误差的优先级经验回放，提高训练效率
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, NamedTuple
import random
from collections import deque
import threading
import time
from dataclasses import dataclass


class Experience(NamedTuple):
    """经验元组"""
    state: torch.Tensor
    action: torch.Tensor
    reward: torch.Tensor
    next_state: torch.Tensor
    done: torch.Tensor
    agent_id: int
    episode_id: int
    step_id: int
    global_state: Optional[torch.Tensor] = None
    attention_info: Optional[Dict] = None


@dataclass
class PrioritizedReplayConfig:
    """优先级经验回放配置"""
    buffer_size: int = 100000           # 缓冲区大小
    alpha: float = 0.6                  # 优先级指数
    beta: float = 0.4                   # 重要性采样指数
    beta_increment: float = 0.001       # beta增长率
    epsilon: float = 1e-6               # 数值稳定性参数
    max_priority: float = 1.0           # 最大优先级
    min_priority: float = 0.01          # 最小优先级
    
    # 多智能体特定参数
    agent_balance_factor: float = 0.3   # 智能体平衡因子
    cooperation_bonus: float = 0.2      # 协作奖励
    conflict_penalty: float = 0.1       # 冲突惩罚
    
    # 采样参数
    batch_size: int = 256               # 批次大小
    min_experiences: int = 1000         # 最少经验数
    update_frequency: int = 4           # 更新频率


class SumTree:
    """求和树数据结构，用于高效的优先级采样"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.tree = np.zeros(2 * capacity - 1)
        self.data = np.zeros(capacity, dtype=object)
        self.write = 0
        self.n_entries = 0
    
    def _propagate(self, idx: int, change: float):
        """向上传播优先级变化"""
        parent = (idx - 1) // 2
        self.tree[parent] += change
        
        if parent != 0:
            self._propagate(parent, change)
    
    def _retrieve(self, idx: int, s: float) -> int:
        """检索叶子节点索引"""
        left = 2 * idx + 1
        right = left + 1
        
        if left >= len(self.tree):
            return idx
        
        if s <= self.tree[left]:
            return self._retrieve(left, s)
        else:
            return self._retrieve(right, s - self.tree[left])
    
    def total(self) -> float:
        """获取总优先级"""
        return self.tree[0]
    
    def add(self, priority: float, data: Experience):
        """添加经验"""
        idx = self.write + self.capacity - 1
        
        self.data[self.write] = data
        self.update(idx, priority)
        
        self.write += 1
        if self.write >= self.capacity:
            self.write = 0
        
        if self.n_entries < self.capacity:
            self.n_entries += 1
    
    def update(self, idx: int, priority: float):
        """更新优先级"""
        change = priority - self.tree[idx]
        self.tree[idx] = priority
        self._propagate(idx, change)
    
    def get(self, s: float) -> Tuple[int, float, Experience]:
        """获取经验"""
        idx = self._retrieve(0, s)
        dataIdx = idx - self.capacity + 1
        
        return idx, self.tree[idx], self.data[dataIdx]


class MultiAgentPrioritizedReplayBuffer:
    """多智能体优先级经验回放缓冲区"""
    
    def __init__(self, 
                 config: PrioritizedReplayConfig,
                 num_agents: int = 4):
        """
        初始化多智能体优先级经验回放缓冲区
        
        Args:
            config: 配置参数
            num_agents: 智能体数量
        """
        self.config = config
        self.num_agents = num_agents
        
        # 求和树
        self.tree = SumTree(config.buffer_size)
        
        # 多智能体统计
        self.agent_counts = np.zeros(num_agents)
        self.agent_priorities = np.ones(num_agents)
        
        # 协作统计
        self.cooperation_scores = deque(maxlen=1000)
        self.conflict_counts = deque(maxlen=1000)
        
        # 当前beta值
        self.beta = config.beta
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 统计信息
        self.total_added = 0
        self.total_sampled = 0
        self.priority_updates = 0
    
    def add(self, 
            state: torch.Tensor,
            action: torch.Tensor,
            reward: torch.Tensor,
            next_state: torch.Tensor,
            done: torch.Tensor,
            agent_id: int,
            episode_id: int,
            step_id: int,
            td_error: Optional[float] = None,
            global_state: Optional[torch.Tensor] = None,
            attention_info: Optional[Dict] = None,
            cooperation_score: Optional[float] = None):
        """
        添加经验到缓冲区
        
        Args:
            state: 状态
            action: 动作
            reward: 奖励
            next_state: 下一状态
            done: 是否结束
            agent_id: 智能体ID
            episode_id: 回合ID
            step_id: 步骤ID
            td_error: TD误差
            global_state: 全局状态
            attention_info: 注意力信息
            cooperation_score: 协作得分
        """
        with self.lock:
            # 创建经验
            experience = Experience(
                state=state,
                action=action,
                reward=reward,
                next_state=next_state,
                done=done,
                agent_id=agent_id,
                episode_id=episode_id,
                step_id=step_id,
                global_state=global_state,
                attention_info=attention_info
            )
            
            # 计算优先级
            priority = self._calculate_priority(
                td_error, agent_id, cooperation_score, reward
            )
            
            # 添加到树
            self.tree.add(priority, experience)
            
            # 更新统计
            self.agent_counts[agent_id] += 1
            self.total_added += 1
            
            if cooperation_score is not None:
                self.cooperation_scores.append(cooperation_score)
    
    def _calculate_priority(self, 
                          td_error: Optional[float],
                          agent_id: int,
                          cooperation_score: Optional[float],
                          reward: torch.Tensor) -> float:
        """计算经验优先级"""
        # 基础优先级（基于TD误差）
        if td_error is not None:
            base_priority = abs(td_error) + self.config.epsilon
        else:
            # 如果没有TD误差，使用奖励的绝对值
            base_priority = abs(float(reward.mean())) + self.config.epsilon
        
        # 智能体平衡调整
        agent_balance = self._get_agent_balance_factor(agent_id)
        
        # 协作奖励
        cooperation_bonus = 0.0
        if cooperation_score is not None:
            cooperation_bonus = self.config.cooperation_bonus * cooperation_score
        
        # 最终优先级
        priority = (base_priority * agent_balance + cooperation_bonus) ** self.config.alpha
        
        # 限制优先级范围
        priority = np.clip(priority, self.config.min_priority, self.config.max_priority)
        
        return priority
    
    def _get_agent_balance_factor(self, agent_id: int) -> float:
        """获取智能体平衡因子"""
        if self.total_added == 0:
            return 1.0
        
        # 计算智能体经验比例
        agent_ratio = self.agent_counts[agent_id] / max(1, self.total_added)
        expected_ratio = 1.0 / self.num_agents
        
        # 如果某个智能体经验过少，提高其优先级
        if agent_ratio < expected_ratio:
            balance_factor = 1.0 + self.config.agent_balance_factor * (expected_ratio - agent_ratio)
        else:
            balance_factor = 1.0
        
        return balance_factor
    
    def sample(self, batch_size: Optional[int] = None) -> Tuple[List[Experience], np.ndarray, np.ndarray]:
        """
        采样经验批次
        
        Args:
            batch_size: 批次大小
            
        Returns:
            experiences: 经验列表
            indices: 索引数组
            weights: 重要性采样权重
        """
        if batch_size is None:
            batch_size = self.config.batch_size
        
        if self.tree.n_entries < self.config.min_experiences:
            return [], np.array([]), np.array([])
        
        with self.lock:
            experiences = []
            indices = []
            priorities = []
            
            # 计算采样段
            segment = self.tree.total() / batch_size
            
            for i in range(batch_size):
                # 在每个段内随机采样
                a = segment * i
                b = segment * (i + 1)
                s = random.uniform(a, b)
                
                idx, priority, experience = self.tree.get(s)
                
                experiences.append(experience)
                indices.append(idx)
                priorities.append(priority)
            
            # 计算重要性采样权重
            priorities = np.array(priorities)
            weights = (self.tree.n_entries * priorities) ** (-self.beta)
            weights /= weights.max()  # 归一化
            
            # 更新beta
            self.beta = min(1.0, self.beta + self.config.beta_increment)
            
            # 更新统计
            self.total_sampled += batch_size
            
            return experiences, np.array(indices), weights
    
    def update_priorities(self, indices: np.ndarray, td_errors: np.ndarray):
        """
        更新经验优先级
        
        Args:
            indices: 经验索引
            td_errors: TD误差
        """
        with self.lock:
            for idx, td_error in zip(indices, td_errors):
                # 获取经验以获取智能体ID
                experience = self.tree.data[idx - self.tree.capacity + 1]
                if experience is not None:
                    # 重新计算优先级
                    priority = self._calculate_priority(
                        td_error, experience.agent_id, None, experience.reward
                    )
                    self.tree.update(idx, priority)
            
            self.priority_updates += len(indices)

    def get_statistics(self) -> Dict:
        """获取缓冲区统计信息"""
        with self.lock:
            stats = {
                'buffer_size': self.tree.n_entries,
                'total_added': self.total_added,
                'total_sampled': self.total_sampled,
                'priority_updates': self.priority_updates,
                'current_beta': self.beta,
                'agent_counts': self.agent_counts.tolist(),
                'agent_ratios': (self.agent_counts / max(1, self.total_added)).tolist()
            }

            if self.cooperation_scores:
                stats.update({
                    'avg_cooperation_score': np.mean(self.cooperation_scores),
                    'cooperation_trend': np.polyfit(range(len(self.cooperation_scores)),
                                                   list(self.cooperation_scores), 1)[0]
                })

            return stats

    def clear(self):
        """清空缓冲区"""
        with self.lock:
            self.tree = SumTree(self.config.buffer_size)
            self.agent_counts = np.zeros(self.num_agents)
            self.cooperation_scores.clear()
            self.conflict_counts.clear()
            self.total_added = 0
            self.total_sampled = 0
            self.priority_updates = 0


class MultiAgentTDErrorCalculator:
    """多智能体TD误差计算器"""

    def __init__(self,
                 gamma: float = 0.99,
                 use_double_q: bool = True,
                 use_attention_weights: bool = True):
        """
        初始化TD误差计算器

        Args:
            gamma: 折扣因子
            use_double_q: 是否使用Double Q-learning
            use_attention_weights: 是否使用注意力权重
        """
        self.gamma = gamma
        self.use_double_q = use_double_q
        self.use_attention_weights = use_attention_weights

    def calculate_td_errors(self,
                          experiences: List[Experience],
                          policy_network,
                          value_network,
                          target_policy_network=None,
                          target_value_network=None) -> np.ndarray:
        """
        计算多智能体TD误差

        Args:
            experiences: 经验列表
            policy_network: 策略网络
            value_network: 价值网络
            target_policy_network: 目标策略网络
            target_value_network: 目标价值网络

        Returns:
            td_errors: TD误差数组
        """
        if not experiences:
            return np.array([])

        # 批量处理经验
        states = torch.stack([exp.state for exp in experiences])
        actions = torch.stack([exp.action for exp in experiences])
        rewards = torch.stack([exp.reward for exp in experiences])
        next_states = torch.stack([exp.next_state for exp in experiences])
        dones = torch.stack([exp.done for exp in experiences])

        # 全局状态（如果有）
        global_states = None
        if experiences[0].global_state is not None:
            global_states = torch.stack([exp.global_state for exp in experiences])

        with torch.no_grad():
            # 计算当前Q值
            if global_states is not None:
                current_q_values = value_network(states, global_states)
            else:
                current_q_values = value_network(states)

            # 计算目标Q值
            if target_value_network is not None:
                if self.use_double_q and target_policy_network is not None:
                    # Double Q-learning
                    if global_states is not None:
                        next_actions = target_policy_network(next_states, global_states)
                        next_q_values = target_value_network(next_states, global_states, next_actions)
                    else:
                        next_actions = target_policy_network(next_states)
                        next_q_values = target_value_network(next_states, next_actions)
                else:
                    # 标准Q-learning
                    if global_states is not None:
                        next_q_values = target_value_network(next_states, global_states)
                    else:
                        next_q_values = target_value_network(next_states)
            else:
                # 使用当前网络
                if global_states is not None:
                    next_q_values = value_network(next_states, global_states)
                else:
                    next_q_values = value_network(next_states)

            # 计算目标值
            target_q_values = rewards + self.gamma * next_q_values * (1 - dones)

            # 计算TD误差
            td_errors = torch.abs(current_q_values - target_q_values)

            # 如果使用注意力权重，调整TD误差
            if self.use_attention_weights:
                attention_weights = self._extract_attention_weights(experiences)
                if attention_weights is not None:
                    td_errors = td_errors * attention_weights

        return td_errors.cpu().numpy()

    def _extract_attention_weights(self, experiences: List[Experience]) -> Optional[torch.Tensor]:
        """提取注意力权重"""
        attention_weights = []

        for exp in experiences:
            if exp.attention_info is not None and 'attention_weights' in exp.attention_info:
                # 使用注意力权重的平均值作为重要性指标
                weights = exp.attention_info['attention_weights']
                if isinstance(weights, torch.Tensor):
                    avg_weight = torch.mean(weights)
                    attention_weights.append(avg_weight)
                else:
                    attention_weights.append(1.0)
            else:
                attention_weights.append(1.0)

        if attention_weights:
            return torch.tensor(attention_weights)

        return None


class AdaptivePriorityScheduler:
    """自适应优先级调度器"""

    def __init__(self,
                 initial_alpha: float = 0.6,
                 initial_beta: float = 0.4,
                 alpha_decay: float = 0.999,
                 beta_growth: float = 1.001,
                 min_alpha: float = 0.1,
                 max_beta: float = 1.0):
        """
        初始化自适应优先级调度器

        Args:
            initial_alpha: 初始alpha值
            initial_beta: 初始beta值
            alpha_decay: alpha衰减率
            beta_growth: beta增长率
            min_alpha: 最小alpha值
            max_beta: 最大beta值
        """
        self.alpha = initial_alpha
        self.beta = initial_beta
        self.alpha_decay = alpha_decay
        self.beta_growth = beta_growth
        self.min_alpha = min_alpha
        self.max_beta = max_beta

        # 性能跟踪
        self.performance_history = deque(maxlen=100)
        self.update_count = 0

    def update(self, performance_score: float):
        """更新调度器"""
        self.performance_history.append(performance_score)
        self.update_count += 1

        # 自适应调整alpha和beta
        if len(self.performance_history) >= 10:
            recent_trend = self._calculate_performance_trend()

            # 如果性能提升，可以减少alpha（降低优先级差异）
            if recent_trend > 0:
                self.alpha = max(self.min_alpha, self.alpha * self.alpha_decay)
            else:
                # 如果性能下降，增加alpha（增加优先级差异）
                self.alpha = min(1.0, self.alpha / self.alpha_decay)

            # beta总是逐渐增长到1.0
            self.beta = min(self.max_beta, self.beta * self.beta_growth)

    def _calculate_performance_trend(self) -> float:
        """计算性能趋势"""
        if len(self.performance_history) < 5:
            return 0.0

        recent_performance = list(self.performance_history)[-10:]
        trend = np.polyfit(range(len(recent_performance)), recent_performance, 1)[0]

        return trend

    def get_current_params(self) -> Tuple[float, float]:
        """获取当前参数"""
        return self.alpha, self.beta
    
    def get_statistics(self) -> Dict:
        """获取缓冲区统计信息"""
        with self.lock:
            stats = {
                'buffer_size': self.tree.n_entries,
                'total_added': self.total_added,
                'total_sampled': self.total_sampled,
                'priority_updates': self.priority_updates,
                'current_beta': self.beta,
                'agent_counts': self.agent_counts.tolist(),
                'agent_ratios': (self.agent_counts / max(1, self.total_added)).tolist()
            }
            
            if self.cooperation_scores:
                stats.update({
                    'avg_cooperation_score': np.mean(self.cooperation_scores),
                    'cooperation_trend': np.polyfit(range(len(self.cooperation_scores)), 
                                                   list(self.cooperation_scores), 1)[0]
                })
            
            return stats
    
    def clear(self):
        """清空缓冲区"""
        with self.lock:
            self.tree = SumTree(self.config.buffer_size)
            self.agent_counts = np.zeros(self.num_agents)
            self.cooperation_scores.clear()
            self.conflict_counts.clear()
            self.total_added = 0
            self.total_sampled = 0
            self.priority_updates = 0
    
    def save_buffer(self, filepath: str):
        """保存缓冲区状态"""
        with self.lock:
            state = {
                'experiences': [],
                'priorities': [],
                'agent_counts': self.agent_counts.tolist(),
                'total_added': self.total_added,
                'beta': self.beta
            }
            
            # 保存所有经验和优先级
            for i in range(self.tree.n_entries):
                experience = self.tree.data[i]
                priority = self.tree.tree[i + self.tree.capacity - 1]
                
                if experience is not None:
                    # 转换tensor为列表以便序列化
                    exp_dict = {
                        'state': experience.state.tolist(),
                        'action': experience.action.tolist(),
                        'reward': experience.reward.tolist(),
                        'next_state': experience.next_state.tolist(),
                        'done': experience.done.tolist(),
                        'agent_id': experience.agent_id,
                        'episode_id': experience.episode_id,
                        'step_id': experience.step_id
                    }
                    
                    state['experiences'].append(exp_dict)
                    state['priorities'].append(priority)
            
            import json
            with open(filepath, 'w') as f:
                json.dump(state, f)
    
    def load_buffer(self, filepath: str):
        """加载缓冲区状态"""
        import json
        with open(filepath, 'r') as f:
            state = json.load(f)
        
        with self.lock:
            self.clear()
            
            # 恢复统计信息
            self.agent_counts = np.array(state['agent_counts'])
            self.total_added = state['total_added']
            self.beta = state['beta']
            
            # 恢复经验
            for exp_dict, priority in zip(state['experiences'], state['priorities']):
                experience = Experience(
                    state=torch.tensor(exp_dict['state']),
                    action=torch.tensor(exp_dict['action']),
                    reward=torch.tensor(exp_dict['reward']),
                    next_state=torch.tensor(exp_dict['next_state']),
                    done=torch.tensor(exp_dict['done']),
                    agent_id=exp_dict['agent_id'],
                    episode_id=exp_dict['episode_id'],
                    step_id=exp_dict['step_id']
                )
                
                self.tree.add(priority, experience)
