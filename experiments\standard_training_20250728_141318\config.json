{"environment": {"num_agvs": 4, "num_tasks": 16, "max_episode_steps": 800}, "attention": {"embed_dim": 64, "num_heads": 4, "sparse_k": 6, "dropout": 0.2}, "network": {"policy_hidden_dims": [256, 128], "value_hidden_dims": [256, 128], "dropout": 0.2}, "training": {"lr": 0.0005, "num_sgd_iter": 8, "sgd_minibatch_size": 64, "train_batch_size": 2000, "curriculum_stages": [{"num_agvs": 2, "num_tasks": 6, "episodes": 500}, {"num_agvs": 3, "num_tasks": 10, "episodes": 800}, {"num_agvs": 4, "num_tasks": 16, "episodes": 1200}]}, "experiment": {"name": "training_run", "eval_interval": 50, "save_interval": 200, "log_interval": 5}, "total_episodes": 5000, "enable_curriculum": true, "log_interval": 10, "save_interval": 500}