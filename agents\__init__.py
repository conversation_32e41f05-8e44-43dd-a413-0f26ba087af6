"""
智能体模块

包含：
- networks/: 神经网络架构
  - policy_net.py: 策略网络
  - value_net.py: 价值网络
- attention/: 注意力机制模块
- gae_estimator.py: GAE优势估计
- ppo_loss.py: PPO损失函数
- mappo_trainer.py: MAPPO训练器
- action_masking.py: 动作掩码机制
- rllib_integration.py: RLlib集成接口
"""

from .networks.policy_net import PolicyNetwork, MultiAgentPolicyNetwork
from .networks.value_net import ValueNetwork, MultiAgentValueNetwork
from .gae_estimator import GAEEstimator, MultiAgentGAEEstimator
from .ppo_loss import PPOLoss, MultiAgentPPOLoss
from .mappo_trainer import MAPPOTrainer
from .action_masking import ActionMaskGenerator, MaskingPolicy
from .rllib_integration import RLlibTrainer, RLlibWarehouseEnv

__all__ = [
    'PolicyNetwork', 'MultiAgentPolicyNetwork',
    'ValueNetwork', 'MultiAgentValueNetwork',
    'GAEEstimator', 'MultiAgentGAEEstimator',
    'PPOLoss', 'MultiAgentPPOLoss',
    'MAPPOTrainer',
    'ActionMaskGenerator', 'MaskingPolicy',
    'RLlibTrainer', 'RLlibWarehouseEnv'
]
