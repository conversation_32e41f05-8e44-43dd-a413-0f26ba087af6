"""
协作约束集成

实现碰撞风险、路径冲突、负载均衡和协作历史约束
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, Tuple, Optional, List, Union, Deque
from collections import deque
from enum import Enum


class ConstraintType(Enum):
    """约束类型枚举"""
    COLLISION_RISK = "collision_risk"      # 碰撞风险约束
    PATH_CONFLICT = "path_conflict"        # 路径冲突约束
    LOAD_BALANCE = "load_balance"          # 负载均衡约束
    COLLABORATION_HISTORY = "collab_history"  # 协作历史约束
    SAFETY_DISTANCE = "safety_distance"    # 安全距离约束
    DEADLOCK_PREVENTION = "deadlock_prevention"  # 死锁预防约束


class CollaborationConstraintIntegrator(nn.Module):
    """协作约束集成器"""
    
    def __init__(self, 
                 embed_dim: int = 64,
                 constraint_types: List[str] = None,
                 constraint_weights: Dict[str, float] = None,
                 history_length: int = 10,
                 safety_distance: float = 2.0,
                 prediction_horizon: float = 3.0,
                 learnable_weights: bool = True):
        """
        初始化协作约束集成器
        
        Args:
            embed_dim: 嵌入维度
            constraint_types: 约束类型列表
            constraint_weights: 约束权重字典
            history_length: 历史长度
            safety_distance: 安全距离
            prediction_horizon: 预测时间范围
            learnable_weights: 是否学习约束权重
        """
        super(CollaborationConstraintIntegrator, self).__init__()
        
        self.embed_dim = embed_dim
        self.history_length = history_length
        self.safety_distance = safety_distance
        self.prediction_horizon = prediction_horizon
        
        if constraint_types is None:
            constraint_types = [
                "collision_risk", "path_conflict", "load_balance", 
                "collab_history", "safety_distance", "deadlock_prevention"
            ]
        self.constraint_types = constraint_types
        
        # 默认约束权重
        if constraint_weights is None:
            constraint_weights = {
                "collision_risk": 1.0,
                "path_conflict": 0.8,
                "load_balance": 0.6,
                "collab_history": 0.4,
                "safety_distance": 1.0,
                "deadlock_prevention": 0.9
            }
        
        # 约束权重参数
        if learnable_weights:
            self.constraint_weights = nn.ParameterDict({
                constraint_type: nn.Parameter(torch.tensor(constraint_weights.get(constraint_type, 1.0)))
                for constraint_type in constraint_types
            })
        else:
            self.register_buffer('constraint_weights', torch.tensor([
                constraint_weights.get(constraint_type, 1.0) 
                for constraint_type in constraint_types
            ]))
        
        # 各种约束计算器
        self.collision_risk_calculator = CollisionRiskCalculator(embed_dim, safety_distance, prediction_horizon)
        self.path_conflict_calculator = PathConflictCalculator(embed_dim, prediction_horizon)
        self.load_balance_calculator = LoadBalanceCalculator(embed_dim)
        self.collaboration_history_tracker = CollaborationHistoryTracker(embed_dim, history_length)
        self.safety_distance_calculator = SafetyDistanceCalculator(embed_dim, safety_distance)
        self.deadlock_detector = DeadlockDetector(embed_dim)
        
        # 约束融合网络
        self.constraint_fusion = nn.Sequential(
            nn.Linear(len(constraint_types), embed_dim // 4),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim // 4, 1),
            nn.Tanh()  # 输出约束调整值
        )
        
        # 自适应权重调整器
        self.adaptive_weight_adjuster = nn.Sequential(
            nn.Linear(embed_dim * 2 + len(constraint_types), embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, len(constraint_types)),
            nn.Softmax(dim=-1)
        )
    
    def forward(self, 
                agv_embeddings: torch.Tensor,
                agv_positions: torch.Tensor,
                agv_velocities: torch.Tensor,
                agv_states: torch.Tensor,
                base_attention_scores: torch.Tensor,
                task_assignments: Optional[torch.Tensor] = None,
                batch_id: str = "default") -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_embeddings: AGV嵌入 [batch_size, num_agvs, embed_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            agv_velocities: AGV速度 [batch_size, num_agvs, 2]
            agv_states: AGV状态 [batch_size, num_agvs, state_dim]
            base_attention_scores: 基础注意力分数 [batch_size, num_agvs, num_agvs]
            task_assignments: 任务分配 [batch_size, num_agvs, num_tasks]
            batch_id: 批次ID
            
        Returns:
            result_dict: 结果字典
        """
        batch_size, num_agvs, _ = agv_embeddings.shape
        
        constraint_scores = []
        constraint_details = {}
        
        # 计算各种约束分数
        for constraint_type in self.constraint_types:
            if constraint_type == "collision_risk":
                scores = self.collision_risk_calculator.compute_collision_risk_constraint(
                    agv_positions, agv_velocities, agv_embeddings
                )
                constraint_details['collision_risks'] = scores
                
            elif constraint_type == "path_conflict":
                scores = self.path_conflict_calculator.compute_path_conflict_constraint(
                    agv_positions, agv_velocities, agv_embeddings, agv_states
                )
                constraint_details['path_conflicts'] = scores
                
            elif constraint_type == "load_balance":
                scores = self.load_balance_calculator.compute_load_balance_constraint(
                    agv_embeddings, agv_states, task_assignments
                )
                constraint_details['load_balance'] = scores
                
            elif constraint_type == "collab_history":
                scores = self.collaboration_history_tracker.compute_history_constraint(
                    agv_embeddings, base_attention_scores, batch_id
                )
                constraint_details['collaboration_history'] = scores
                
            elif constraint_type == "safety_distance":
                scores = self.safety_distance_calculator.compute_safety_constraint(
                    agv_positions, agv_velocities
                )
                constraint_details['safety_distances'] = scores
                
            elif constraint_type == "deadlock_prevention":
                scores = self.deadlock_detector.compute_deadlock_prevention_constraint(
                    agv_positions, agv_velocities, agv_states
                )
                constraint_details['deadlock_prevention'] = scores
                
            else:
                # 默认约束（无约束）
                scores = torch.zeros(batch_size, num_agvs, num_agvs, device=agv_embeddings.device)
            
            constraint_scores.append(scores)
        
        # 堆叠约束分数
        constraint_scores = torch.stack(constraint_scores, dim=-1)
        # [batch_size, num_agvs, num_agvs, num_constraints]
        
        # 计算自适应权重
        global_agv_features = torch.mean(agv_embeddings, dim=1)  # [batch_size, embed_dim]
        constraint_summary = torch.mean(constraint_scores, dim=(1, 2))  # [batch_size, num_constraints]
        
        adaptive_input = torch.cat([
            global_agv_features, 
            torch.mean(agv_embeddings, dim=1),  # 重复以增加维度
            constraint_summary
        ], dim=-1)
        
        adaptive_weights = self.adaptive_weight_adjuster(adaptive_input)  # [batch_size, num_constraints]
        
        # 应用约束权重
        if isinstance(self.constraint_weights, nn.ParameterDict):
            static_weights = torch.stack([
                self.constraint_weights[constraint_type] 
                for constraint_type in self.constraint_types
            ])
        else:
            static_weights = self.constraint_weights
        
        # 结合静态权重和自适应权重
        combined_weights = static_weights.unsqueeze(0) * adaptive_weights  # [batch_size, num_constraints]
        
        # 加权约束分数
        weighted_scores = constraint_scores * combined_weights.unsqueeze(1).unsqueeze(1)
        
        # 通过融合网络计算最终约束调整
        constraint_adjustments = self.constraint_fusion(weighted_scores).squeeze(-1)
        # [batch_size, num_agvs, num_agvs]
        
        # 应用约束调整到基础注意力分数
        constrained_attention_scores = base_attention_scores + constraint_adjustments
        
        return {
            'constrained_attention_scores': constrained_attention_scores,
            'constraint_adjustments': constraint_adjustments,
            'constraint_details': constraint_details,
            'adaptive_weights': adaptive_weights,
            'static_weights': static_weights,
            'individual_constraint_scores': constraint_scores
        }


class CollisionRiskCalculator(nn.Module):
    """碰撞风险计算器"""
    
    def __init__(self, embed_dim: int, safety_distance: float, prediction_horizon: float):
        super(CollisionRiskCalculator, self).__init__()
        self.embed_dim = embed_dim
        self.safety_distance = safety_distance
        self.prediction_horizon = prediction_horizon
        
        # 碰撞风险预测网络
        self.risk_predictor = nn.Sequential(
            nn.Linear(6, embed_dim // 4),  # 距离、速度、角度特征
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1),
            nn.Sigmoid()
        )
    
    def compute_collision_risk_constraint(self, 
                                        agv_positions: torch.Tensor,
                                        agv_velocities: torch.Tensor,
                                        agv_embeddings: torch.Tensor) -> torch.Tensor:
        """计算碰撞风险约束"""
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算相对位置和速度
        pos_i = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        pos_j = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        vel_i = agv_velocities.unsqueeze(2)
        vel_j = agv_velocities.unsqueeze(1)
        
        relative_pos = pos_j - pos_i  # [batch_size, num_agvs, num_agvs, 2]
        relative_vel = vel_j - vel_i
        
        # 计算距离和相对速度
        distances = torch.norm(relative_pos, dim=-1)
        relative_speeds = torch.norm(relative_vel, dim=-1)
        
        # 计算接近速度（负值表示远离）
        approach_speeds = torch.sum(relative_pos * relative_vel, dim=-1) / (distances + 1e-8)
        
        # 预测未来最近距离
        future_positions_i = agv_positions + agv_velocities * self.prediction_horizon
        future_positions_j = future_positions_i.unsqueeze(1)
        future_positions_i = future_positions_i.unsqueeze(2)
        future_distances = torch.norm(future_positions_j - future_positions_i, dim=-1)
        
        # 计算碰撞时间
        collision_times = torch.where(
            approach_speeds > 0,
            (distances - self.safety_distance) / approach_speeds,
            torch.full_like(distances, float('inf'))
        )
        
        # 组合风险特征
        risk_features = torch.stack([
            torch.clamp(distances / (self.safety_distance * 3), 0, 1),  # 归一化距离
            torch.clamp(relative_speeds / 5.0, 0, 1),  # 归一化相对速度
            torch.sigmoid(approach_speeds),  # 接近速度
            torch.clamp(future_distances / (self.safety_distance * 3), 0, 1),  # 未来距离
            torch.sigmoid(-collision_times / self.prediction_horizon),  # 碰撞紧急程度
            torch.sigmoid(-(distances - self.safety_distance))  # 当前危险程度
        ], dim=-1)
        
        # 预测碰撞风险
        collision_risks = self.risk_predictor(risk_features).squeeze(-1)
        
        # 排除自己与自己的风险
        mask = torch.eye(num_agvs, device=agv_positions.device).unsqueeze(0) == 0
        collision_risks = collision_risks * mask
        
        # 转换为约束分数（高风险 -> 负分数，降低注意力）
        constraint_scores = -collision_risks * 2.0
        
        return constraint_scores


class PathConflictCalculator(nn.Module):
    """路径冲突计算器"""
    
    def __init__(self, embed_dim: int, prediction_horizon: float):
        super(PathConflictCalculator, self).__init__()
        self.embed_dim = embed_dim
        self.prediction_horizon = prediction_horizon
        
        # 路径冲突预测网络
        self.conflict_predictor = nn.Sequential(
            nn.Linear(8, embed_dim // 4),  # 路径特征
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1),
            nn.Sigmoid()
        )
    
    def compute_path_conflict_constraint(self, 
                                       agv_positions: torch.Tensor,
                                       agv_velocities: torch.Tensor,
                                       agv_embeddings: torch.Tensor,
                                       agv_states: torch.Tensor) -> torch.Tensor:
        """计算路径冲突约束"""
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 预测路径
        time_steps = torch.linspace(0, self.prediction_horizon, 5, device=agv_positions.device)
        predicted_paths = agv_positions.unsqueeze(-2) + agv_velocities.unsqueeze(-2) * time_steps.view(1, 1, -1, 1)
        # [batch_size, num_agvs, time_steps, 2]
        
        # 计算路径交叉点
        path_conflicts = torch.zeros(batch_size, num_agvs, num_agvs, device=agv_positions.device)
        
        for t in range(len(time_steps)):
            pos_t_i = predicted_paths[:, :, t, :].unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
            pos_t_j = predicted_paths[:, :, t, :].unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
            
            distances_t = torch.norm(pos_t_j - pos_t_i, dim=-1)
            conflicts_t = torch.sigmoid(-(distances_t - 2.0))  # 2米冲突阈值
            
            path_conflicts = torch.max(path_conflicts, conflicts_t)
        
        # 考虑目标方向的相似性
        # 假设AGV状态中包含目标位置信息
        if agv_states.shape[-1] > 6:  # 假设位置6-7是目标位置
            target_positions = agv_states[:, :, 6:8]
            target_directions = target_positions - agv_positions
            target_directions = target_directions / (torch.norm(target_directions, dim=-1, keepdim=True) + 1e-8)
            
            dir_i = target_directions.unsqueeze(2)
            dir_j = target_directions.unsqueeze(1)
            direction_similarity = torch.sum(dir_i * dir_j, dim=-1)
            
            # 方向相似且距离近的AGV更容易冲突
            direction_conflicts = torch.sigmoid(direction_similarity - 0.8) * torch.sigmoid(-(torch.norm(
                agv_positions.unsqueeze(2) - agv_positions.unsqueeze(1), dim=-1
            ) - 5.0))
            
            path_conflicts = torch.max(path_conflicts, direction_conflicts)
        
        # 转换为约束分数
        constraint_scores = -path_conflicts * 1.5
        
        return constraint_scores


class LoadBalanceCalculator(nn.Module):
    """负载均衡计算器"""
    
    def __init__(self, embed_dim: int):
        super(LoadBalanceCalculator, self).__init__()
        self.embed_dim = embed_dim
        
        # 负载评估网络
        self.load_evaluator = nn.Sequential(
            nn.Linear(4, embed_dim // 4),  # 载重、任务数、距离、能力特征
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1),
            nn.Sigmoid()
        )
    
    def compute_load_balance_constraint(self, 
                                      agv_embeddings: torch.Tensor,
                                      agv_states: torch.Tensor,
                                      task_assignments: Optional[torch.Tensor] = None) -> torch.Tensor:
        """计算负载均衡约束"""
        batch_size, num_agvs, _ = agv_embeddings.shape
        
        # 提取负载相关特征
        current_loads = agv_states[:, :, 4]  # 当前载重
        capacities = agv_states[:, :, 5]     # 载重容量
        task_counts = agv_states[:, :, 6]    # 当前任务数
        
        # 计算负载率
        load_ratios = current_loads / (capacities + 1e-8)
        
        # 计算负载不平衡程度
        load_i = load_ratios.unsqueeze(2)  # [batch_size, num_agvs, 1]
        load_j = load_ratios.unsqueeze(1)  # [batch_size, 1, num_agvs]

        load_differences = torch.abs(load_j - load_i).squeeze(-1)  # [batch_size, num_agvs, num_agvs]

        # 任务数不平衡
        task_i = task_counts.unsqueeze(2)
        task_j = task_counts.unsqueeze(1)
        task_differences = torch.abs(task_j - task_i)  # [batch_size, num_agvs, num_agvs]
        
        # 计算AGV间的距离（影响协作成本）
        positions = agv_states[:, :, :2]
        pos_i = positions.unsqueeze(2)
        pos_j = positions.unsqueeze(1)
        distances = torch.norm(pos_j - pos_i, dim=-1)
        
        # 计算平均负载
        avg_load = (load_i + load_j) / 2  # [batch_size, num_agvs, num_agvs]
        avg_load = avg_load.squeeze(-1) if avg_load.dim() > 3 else avg_load

        # 组合负载均衡特征
        balance_features = torch.stack([
            load_differences,
            task_differences / 10.0,  # 归一化任务数差异
            distances / 20.0,  # 归一化距离
            avg_load  # 平均负载
        ], dim=-1)
        
        # 评估负载不平衡程度
        imbalance_scores = self.load_evaluator(balance_features).squeeze(-1)
        
        # 高负载的AGV应该减少接受新任务的注意力
        high_load_penalty_i = torch.sigmoid((load_i - 0.8) * 5)  # [batch_size, num_agvs, 1]
        high_load_penalty_j = torch.sigmoid((load_j - 0.8) * 5)  # [batch_size, 1, num_agvs]

        # 组合高负载惩罚
        high_load_penalty = (high_load_penalty_i + high_load_penalty_j) / 2
        high_load_penalty = high_load_penalty.squeeze(-1)  # [batch_size, num_agvs, num_agvs]

        # 转换为约束分数（鼓励负载均衡）
        constraint_scores = imbalance_scores * 0.5 - high_load_penalty
        
        return constraint_scores


class CollaborationHistoryTracker(nn.Module):
    """协作历史跟踪器"""
    
    def __init__(self, embed_dim: int, history_length: int):
        super(CollaborationHistoryTracker, self).__init__()
        self.embed_dim = embed_dim
        self.history_length = history_length
        
        # 协作历史存储
        self.collaboration_history = {}
        
        # 历史分析网络
        self.history_analyzer = nn.Sequential(
            nn.Linear(3, embed_dim // 4),  # 协作频率、成功率、最近性
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1),
            nn.Tanh()
        )
    
    def update_collaboration_history(self, 
                                   batch_id: str,
                                   attention_weights: torch.Tensor,
                                   collaboration_success: Optional[torch.Tensor] = None):
        """更新协作历史"""
        if batch_id not in self.collaboration_history:
            self.collaboration_history[batch_id] = {
                'attention_history': deque(maxlen=self.history_length),
                'success_history': deque(maxlen=self.history_length)
            }
        
        # 添加当前注意力权重
        self.collaboration_history[batch_id]['attention_history'].append(
            attention_weights.detach().cpu()
        )
        
        # 添加协作成功信息（如果提供）
        if collaboration_success is not None:
            self.collaboration_history[batch_id]['success_history'].append(
                collaboration_success.detach().cpu()
            )
    
    def compute_history_constraint(self, 
                                 agv_embeddings: torch.Tensor,
                                 current_attention: torch.Tensor,
                                 batch_id: str) -> torch.Tensor:
        """计算协作历史约束"""
        batch_size, num_agvs, _ = agv_embeddings.shape
        
        if batch_id not in self.collaboration_history:
            # 没有历史记录，返回零约束
            return torch.zeros(batch_size, num_agvs, num_agvs, device=agv_embeddings.device)
        
        history = self.collaboration_history[batch_id]
        attention_history = history['attention_history']
        success_history = history['success_history']
        
        if len(attention_history) == 0:
            return torch.zeros(batch_size, num_agvs, num_agvs, device=agv_embeddings.device)
        
        # 计算协作频率
        historical_attention = torch.stack(list(attention_history), dim=0)  # [history_len, batch_size, num_agvs, num_agvs]
        collaboration_frequency = torch.mean(historical_attention, dim=0)  # [batch_size, num_agvs, num_agvs]
        
        # 计算协作成功率
        if len(success_history) > 0:
            historical_success = torch.stack(list(success_history), dim=0)
            collaboration_success_rate = torch.mean(historical_success, dim=0)
        else:
            collaboration_success_rate = torch.ones_like(collaboration_frequency) * 0.5
        
        # 计算最近协作程度
        if len(attention_history) > 0:
            recent_attention = attention_history[-1]  # 最近的注意力
            recency_weight = torch.sigmoid(recent_attention - 0.5)
        else:
            recency_weight = torch.zeros_like(collaboration_frequency)
        
        # 组合历史特征
        history_features = torch.stack([
            collaboration_frequency,
            collaboration_success_rate,
            recency_weight
        ], dim=-1)
        
        # 分析历史影响
        history_influence = self.history_analyzer(history_features).squeeze(-1)
        
        # 更新历史记录
        self.update_collaboration_history(batch_id, current_attention)
        
        return history_influence


class SafetyDistanceCalculator(nn.Module):
    """安全距离计算器"""
    
    def __init__(self, embed_dim: int, safety_distance: float):
        super(SafetyDistanceCalculator, self).__init__()
        self.embed_dim = embed_dim
        self.safety_distance = safety_distance
    
    def compute_safety_constraint(self, 
                                agv_positions: torch.Tensor,
                                agv_velocities: torch.Tensor) -> torch.Tensor:
        """计算安全距离约束"""
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算当前距离
        pos_i = agv_positions.unsqueeze(2)
        pos_j = agv_positions.unsqueeze(1)
        distances = torch.norm(pos_j - pos_i, dim=-1)
        
        # 计算相对速度
        vel_i = agv_velocities.unsqueeze(2)
        vel_j = agv_velocities.unsqueeze(1)
        relative_velocities = vel_j - vel_i
        relative_speeds = torch.norm(relative_velocities, dim=-1)
        
        # 动态安全距离（考虑速度）
        dynamic_safety_distance = self.safety_distance + relative_speeds * 0.5
        
        # 安全约束分数
        safety_violations = torch.sigmoid(-(distances - dynamic_safety_distance))
        
        # 排除自己
        mask = torch.eye(num_agvs, device=agv_positions.device).unsqueeze(0) == 0
        safety_violations = safety_violations * mask
        
        # 转换为约束分数
        constraint_scores = -safety_violations * 3.0
        
        return constraint_scores


class DeadlockDetector(nn.Module):
    """死锁检测器"""
    
    def __init__(self, embed_dim: int):
        super(DeadlockDetector, self).__init__()
        self.embed_dim = embed_dim
        
        # 死锁预测网络
        self.deadlock_predictor = nn.Sequential(
            nn.Linear(5, embed_dim // 4),  # 死锁特征
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1),
            nn.Sigmoid()
        )
    
    def compute_deadlock_prevention_constraint(self, 
                                             agv_positions: torch.Tensor,
                                             agv_velocities: torch.Tensor,
                                             agv_states: torch.Tensor) -> torch.Tensor:
        """计算死锁预防约束"""
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算速度大小
        speeds = torch.norm(agv_velocities, dim=-1)
        
        # 检测低速或停止的AGV
        low_speed_threshold = 0.1
        stopped_agvs = (speeds < low_speed_threshold).float()
        
        # 计算AGV密度
        pos_i = agv_positions.unsqueeze(2)
        pos_j = agv_positions.unsqueeze(1)
        distances = torch.norm(pos_j - pos_i, dim=-1)
        
        # 计算局部密度（3米范围内的AGV数量）
        local_density = torch.sum((distances < 3.0) & (distances > 0), dim=-1).float()
        
        # 检测循环等待模式
        # 简化版本：检测相互接近但速度很低的AGV对
        mutual_approach = torch.zeros(batch_size, num_agvs, num_agvs, device=agv_positions.device)
        
        for i in range(num_agvs):
            for j in range(num_agvs):
                if i != j:
                    # 计算AGV i 朝向 AGV j 的程度
                    direction_ij = agv_positions[:, j, :] - agv_positions[:, i, :]
                    direction_ij = direction_ij / (torch.norm(direction_ij, dim=-1, keepdim=True) + 1e-8)
                    
                    velocity_i = agv_velocities[:, i, :]
                    velocity_i = velocity_i / (torch.norm(velocity_i, dim=-1, keepdim=True) + 1e-8)
                    
                    approach_score = torch.sum(velocity_i * direction_ij, dim=-1)
                    mutual_approach[:, i, j] = torch.sigmoid(approach_score)
        
        # 组合死锁特征
        deadlock_features = torch.stack([
            distances / 10.0,  # 归一化距离
            stopped_agvs.unsqueeze(2).expand(-1, -1, num_agvs),  # AGV i 是否停止
            stopped_agvs.unsqueeze(1).expand(-1, num_agvs, -1),  # AGV j 是否停止
            local_density.unsqueeze(2).expand(-1, -1, num_agvs) / num_agvs,  # 归一化局部密度
            mutual_approach  # 相互接近程度
        ], dim=-1)
        
        # 预测死锁风险
        deadlock_risks = self.deadlock_predictor(deadlock_features).squeeze(-1)
        
        # 排除自己
        mask = torch.eye(num_agvs, device=agv_positions.device).unsqueeze(0) == 0
        deadlock_risks = deadlock_risks * mask
        
        # 转换为约束分数（高死锁风险 -> 负分数）
        constraint_scores = -deadlock_risks * 2.5
        
        return constraint_scores
