"""
环境可视化系统

实现实时可视化界面，显示AGV位置、任务状态、注意力热力图等
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
from typing import List, Dict, Optional, Tuple, Any
import seaborn as sns
from envs.grid_world import GridWorld, CellType
from envs.agv_model import AGVModel
from envs.task_manager import TaskManager, Task


class WarehouseVisualizer:
    """仓储环境可视化器"""
    
    def __init__(self, 
                 grid_world: GridWorld,
                 figsize: Tuple[int, int] = (12, 8),
                 dpi: int = 100):
        """
        初始化可视化器
        
        Args:
            grid_world: 网格世界环境
            figsize: 图形大小
            dpi: 分辨率
        """
        self.grid_world = grid_world
        self.figsize = figsize
        self.dpi = dpi
        
        # 颜色配置
        self.colors = {
            'empty': '#FFFFFF',      # 空地 - 白色
            'shelf': '#8B4513',      # 货架 - 棕色
            'wall': '#000000',       # 墙壁 - 黑色
            'passage': '#F0F0F0',    # 通道 - 浅灰色
            'pickup': '#90EE90',     # 拾取点 - 浅绿色
            'dropoff': '#FFB6C1',    # 放置点 - 浅粉色
            'agv_idle': '#0000FF',   # 空闲AGV - 蓝色
            'agv_moving': '#00FF00', # 移动AGV - 绿色
            'agv_busy': '#FF0000',   # 忙碌AGV - 红色
            'task_unassigned': '#FFFF00',  # 未分配任务 - 黄色
            'task_assigned': '#FFA500',    # 已分配任务 - 橙色
            'task_progress': '#FF69B4',    # 进行中任务 - 粉色
            'path': '#00FFFF',       # 路径 - 青色
            'attention': '#FF0000'   # 注意力 - 红色
        }
        
        # 图形对象
        self.fig = None
        self.ax = None
        self.grid_image = None
        self.agv_patches = {}
        self.task_patches = {}
        self.path_lines = {}
        self.attention_overlay = None
        
        # 状态
        self.current_step = 0
        self.animation = None
        self.is_recording = False
        self.frames = []
    
    def setup_figure(self):
        """设置图形"""
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']  # 设置中文字体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        self.fig, self.ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        self.ax.set_xlim(-0.5, self.grid_world.width - 0.5)
        self.ax.set_ylim(-0.5, self.grid_world.height - 0.5)
        self.ax.set_aspect('equal')
        self.ax.invert_yaxis()  # 翻转Y轴使(0,0)在左上角

        # 设置网格
        self.ax.set_xticks(range(self.grid_world.width))
        self.ax.set_yticks(range(self.grid_world.height))
        self.ax.grid(True, alpha=0.3, linewidth=0.5)

        # 设置标题
        self.ax.set_title('Multi-AGV Warehouse Environment', fontsize=14, fontweight='bold')
        self.ax.set_xlabel('X Coordinate')
        self.ax.set_ylabel('Y Coordinate')
    
    def draw_grid(self):
        """绘制网格背景"""
        grid_array = np.zeros((self.grid_world.height, self.grid_world.width, 3))
        
        for y in range(self.grid_world.height):
            for x in range(self.grid_world.width):
                cell_type = self.grid_world.get_cell_type(x, y)
                
                if cell_type == CellType.EMPTY:
                    color = self.colors['empty']
                elif cell_type == CellType.SHELF:
                    color = self.colors['shelf']
                elif cell_type == CellType.WALL:
                    color = self.colors['wall']
                elif cell_type == CellType.PASSAGE:
                    color = self.colors['passage']
                elif cell_type == CellType.PICKUP:
                    color = self.colors['pickup']
                elif cell_type == CellType.DROPOFF:
                    color = self.colors['dropoff']
                else:
                    color = self.colors['empty']
                
                # 转换颜色为RGB
                if isinstance(color, str) and color.startswith('#'):
                    rgb = tuple(int(color[i:i+2], 16) / 255.0 for i in (1, 3, 5))
                    grid_array[y, x] = rgb
        
        if self.grid_image is None:
            self.grid_image = self.ax.imshow(grid_array, alpha=0.7)
        else:
            self.grid_image.set_array(grid_array)
    
    def draw_shelves(self):
        """绘制货架"""
        for shelf in self.grid_world.shelves:
            rect = patches.Rectangle(
                (shelf['x'] - 0.4, shelf['y'] - 0.4),
                shelf['width'] - 0.2, shelf['height'] - 0.2,
                linewidth=2, edgecolor='black', facecolor=self.colors['shelf'],
                alpha=0.8
            )
            self.ax.add_patch(rect)
            
            # 添加货架ID标签
            self.ax.text(
                shelf['x'] + shelf['width'] / 2,
                shelf['y'] + shelf['height'] / 2,
                f"S{shelf['id']}",
                ha='center', va='center', fontsize=8, fontweight='bold'
            )
    
    def draw_agvs(self, agvs: List[AGVModel]):
        """绘制AGV"""
        # 清除旧的AGV图形
        for patch in self.agv_patches.values():
            patch.remove()
        self.agv_patches.clear()
        
        for agv in agvs:
            # 根据AGV状态选择颜色
            if agv.is_idle:
                color = self.colors['agv_idle']
            elif agv.is_busy:
                color = self.colors['agv_busy']
            else:
                color = self.colors['agv_moving']
            
            # 绘制AGV圆形
            circle = patches.Circle(
                (agv.x, agv.y), 0.3,
                facecolor=color, edgecolor='black', linewidth=2
            )
            self.ax.add_patch(circle)
            self.agv_patches[agv.id] = circle
            
            # 添加AGV ID标签
            self.ax.text(
                agv.x, agv.y, f"A{agv.id}",
                ha='center', va='center', fontsize=8, fontweight='bold', color='white'
            )
            
            # 显示载重信息
            if agv.current_load > 0:
                self.ax.text(
                    agv.x, agv.y - 0.5, f"{agv.current_load}/{agv.capacity}",
                    ha='center', va='center', fontsize=6, 
                    bbox=dict(boxstyle="round,pad=0.1", facecolor='yellow', alpha=0.7)
                )
            
            # 绘制朝向箭头
            if agv.velocity > 0:
                dx = 0.2 * np.cos(agv.theta)
                dy = 0.2 * np.sin(agv.theta)
                self.ax.arrow(
                    agv.x, agv.y, dx, dy,
                    head_width=0.1, head_length=0.1, fc='white', ec='white'
                )
    
    def draw_tasks(self, task_manager: TaskManager):
        """绘制任务"""
        # 清除旧的任务图形
        for patch in self.task_patches.values():
            patch.remove()
        self.task_patches.clear()
        
        for task_id, task in task_manager.tasks.items():
            # 根据任务状态选择颜色
            if task.is_available:
                color = self.colors['task_unassigned']
            elif task.is_assigned:
                color = self.colors['task_assigned']
            elif task.is_in_progress:
                color = self.colors['task_progress']
            else:
                continue  # 已完成或失败的任务不显示
            
            # 绘制拾取点
            pickup_rect = patches.Rectangle(
                (task.pickup_position[0] - 0.15, task.pickup_position[1] - 0.15),
                0.3, 0.3, facecolor=color, edgecolor='black', linewidth=1
            )
            self.ax.add_patch(pickup_rect)
            self.task_patches[f"pickup_{task_id}"] = pickup_rect
            
            # 绘制放置点
            dropoff_circle = patches.Circle(
                task.dropoff_position, 0.15,
                facecolor=color, edgecolor='black', linewidth=1
            )
            self.ax.add_patch(dropoff_circle)
            self.task_patches[f"dropoff_{task_id}"] = dropoff_circle
            
            # 绘制连接线
            line = plt.Line2D(
                [task.pickup_position[0], task.dropoff_position[0]],
                [task.pickup_position[1], task.dropoff_position[1]],
                color=color, linestyle='--', alpha=0.5, linewidth=1
            )
            self.ax.add_line(line)
            self.task_patches[f"line_{task_id}"] = line
            
            # 添加任务信息
            self.ax.text(
                task.pickup_position[0], task.pickup_position[1] + 0.25,
                f"T{task_id}\n{task.weight}kg",
                ha='center', va='center', fontsize=6,
                bbox=dict(boxstyle="round,pad=0.1", facecolor='white', alpha=0.8)
            )
    
    def draw_paths(self, agvs: List[AGVModel]):
        """绘制AGV路径"""
        # 清除旧的路径
        for line in self.path_lines.values():
            line.remove()
        self.path_lines.clear()
        
        for agv in agvs:
            if agv.planned_path and len(agv.planned_path) > 1:
                path_x = [pos[0] for pos in agv.planned_path]
                path_y = [pos[1] for pos in agv.planned_path]
                
                line = plt.Line2D(
                    path_x, path_y,
                    color=self.colors['path'], linewidth=2, alpha=0.6
                )
                self.ax.add_line(line)
                self.path_lines[agv.id] = line
    
    def draw_attention_heatmap(self, attention_weights: np.ndarray, positions: List[Tuple[int, int]]):
        """绘制注意力热力图"""
        if self.attention_overlay is not None:
            self.attention_overlay.remove()
        
        # 创建注意力热力图
        heatmap = np.zeros((self.grid_world.height, self.grid_world.width))
        
        for i, (x, y) in enumerate(positions):
            if i < len(attention_weights):
                heatmap[y, x] = attention_weights[i]
        
        # 绘制热力图
        self.attention_overlay = self.ax.imshow(
            heatmap, alpha=0.4, cmap='Reds', 
            vmin=0, vmax=np.max(attention_weights) if len(attention_weights) > 0 else 1
        )
    
    def update_display(self,
                      agvs: List[AGVModel],
                      task_manager: TaskManager,
                      attention_weights: Optional[np.ndarray] = None,
                      attention_positions: Optional[List[Tuple[int, int]]] = None,
                      step: int = 0):
        """更新显示 - 简化版本，只显示一张包含所有信息的图"""
        self.current_step = step

        # 清除画布
        self.ax.clear()

        # 重新设置坐标轴
        self.ax.set_xlim(-0.5, self.grid_world.width - 0.5)
        self.ax.set_ylim(-0.5, self.grid_world.height - 0.5)
        self.ax.set_aspect('equal')
        self.ax.invert_yaxis()

        # 设置网格
        self.ax.set_xticks(range(self.grid_world.width))
        self.ax.set_yticks(range(self.grid_world.height))
        self.ax.grid(True, alpha=0.3, linewidth=0.5)

        # 绘制各个组件（简化顺序）
        self.draw_grid()
        self.draw_shelves()
        self.draw_tasks(task_manager)
        self.draw_agvs(agvs)

        # 添加简化的统计信息
        self._add_simple_statistics(agvs, task_manager, step)

        if self.is_recording:
            self.frames.append(self._capture_frame())

    def _add_simple_statistics(self, agvs: List[AGVModel], task_manager: TaskManager, step: int):
        """添加简化的统计信息"""
        # AGV统计
        idle_count = sum(1 for agv in agvs if agv.is_idle)
        moving_count = sum(1 for agv in agvs if agv.is_moving)
        busy_count = sum(1 for agv in agvs if agv.is_busy)

        # 任务统计
        task_stats = task_manager.get_task_statistics()
        completed = task_stats.get('completed_tasks', 0)
        total = task_stats.get('total_tasks', 0)

        # 显示简化统计信息
        stats_text = f"Step: {step} | AGVs: Idle({idle_count}) Moving({moving_count}) Busy({busy_count}) | Tasks: {completed}/{total}"

        self.ax.text(
            0.5, 1.02, stats_text,
            transform=self.ax.transAxes, fontsize=10,
            horizontalalignment='center',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8)
        )

    def _add_statistics(self, agvs: List[AGVModel], task_manager: TaskManager):
        """添加统计信息"""
        stats_text = []
        
        # AGV统计
        idle_count = sum(1 for agv in agvs if agv.is_idle)
        moving_count = sum(1 for agv in agvs if agv.is_moving)
        busy_count = sum(1 for agv in agvs if agv.is_busy)
        
        stats_text.append(f"AGV状态: 空闲{idle_count} 移动{moving_count} 忙碌{busy_count}")
        
        # 任务统计
        task_stats = task_manager.get_task_statistics()
        stats_text.append(f"任务: 总计{task_stats.get('total_tasks', 0)} "
                         f"完成{task_stats.get('completed_tasks', 0)} "
                         f"失败{task_stats.get('failed_tasks', 0)}")
        
        # 完成率
        completion_rate = task_stats.get('completion_rate', 0)
        stats_text.append(f"完成率: {completion_rate:.2%}")
        
        # 显示统计信息
        stats_str = '\n'.join(stats_text)
        self.ax.text(
            0.02, 0.98, stats_str,
            transform=self.ax.transAxes, fontsize=10,
            verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8)
        )
    
    def _capture_frame(self) -> np.ndarray:
        """捕获当前帧"""
        self.fig.canvas.draw()
        buf = np.frombuffer(self.fig.canvas.tostring_rgb(), dtype=np.uint8)
        buf = buf.reshape(self.fig.canvas.get_width_height()[::-1] + (3,))
        return buf
    
    def start_recording(self):
        """开始录制"""
        self.is_recording = True
        self.frames = []
    
    def stop_recording(self):
        """停止录制"""
        self.is_recording = False
    
    def save_animation(self, filename: str, fps: int = 10):
        """保存动画"""
        if not self.frames:
            print("没有录制的帧")
            return
        
        import imageio
        imageio.mimsave(filename, self.frames, fps=fps)
        print(f"动画已保存到 {filename}")
    
    def save_screenshot(self, filename: str):
        """保存截图"""
        if self.fig is not None:
            self.fig.savefig(filename, dpi=self.dpi, bbox_inches='tight')
            print(f"截图已保存到 {filename}")
    
    def show(self, block: bool = True):
        """显示图形"""
        if self.fig is not None:
            plt.show(block=block)
    
    def close(self):
        """关闭图形"""
        if self.fig is not None:
            plt.close(self.fig)
            self.fig = None
            self.ax = None
    
    def create_legend(self):
        """创建图例"""
        legend_elements = [
            patches.Patch(color=self.colors['agv_idle'], label='空闲AGV'),
            patches.Patch(color=self.colors['agv_moving'], label='移动AGV'),
            patches.Patch(color=self.colors['agv_busy'], label='忙碌AGV'),
            patches.Patch(color=self.colors['task_unassigned'], label='未分配任务'),
            patches.Patch(color=self.colors['task_assigned'], label='已分配任务'),
            patches.Patch(color=self.colors['task_progress'], label='进行中任务'),
            patches.Patch(color=self.colors['shelf'], label='货架'),
            patches.Patch(color=self.colors['pickup'], label='拾取点'),
            patches.Patch(color=self.colors['dropoff'], label='放置点')
        ]
        
        self.ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))
    
    def __enter__(self):
        """上下文管理器入口"""
        self.setup_figure()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
