"""
训练稳定性保证

实现梯度裁剪、正则化技术、异常检测等训练稳定性保证措施
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
from collections import deque
import logging
import warnings
from dataclasses import dataclass
from enum import Enum


class StabilityLevel(Enum):
    """稳定性级别"""
    STABLE = "stable"           # 稳定
    UNSTABLE = "unstable"       # 不稳定
    CRITICAL = "critical"       # 危险
    DIVERGING = "diverging"     # 发散


@dataclass
class StabilityConfig:
    """稳定性配置"""
    # 梯度裁剪
    max_grad_norm: float = 0.5              # 最大梯度范数
    grad_clip_method: str = "norm"          # 裁剪方法: "norm", "value"
    adaptive_grad_clip: bool = True         # 自适应梯度裁剪
    
    # 正则化
    weight_decay: float = 1e-4              # 权重衰减
    dropout_rate: float = 0.1               # Dropout率
    batch_norm_momentum: float = 0.1        # BatchNorm动量
    
    # 异常检测
    loss_spike_threshold: float = 2.0       # 损失突增阈值
    gradient_explosion_threshold: float = 10.0  # 梯度爆炸阈值
    nan_detection: bool = True              # NaN检测
    inf_detection: bool = True              # 无穷大检测
    
    # 学习率调整
    lr_decay_factor: float = 0.5            # 学习率衰减因子
    lr_patience: int = 10                   # 学习率调整耐心
    min_lr: float = 1e-6                    # 最小学习率
    
    # 监控窗口
    monitoring_window: int = 100            # 监控窗口大小
    stability_threshold: float = 0.1        # 稳定性阈值


class GradientClipper:
    """梯度裁剪器"""
    
    def __init__(self, 
                 max_norm: float = 0.5,
                 method: str = "norm",
                 adaptive: bool = True):
        """
        初始化梯度裁剪器
        
        Args:
            max_norm: 最大梯度范数
            method: 裁剪方法
            adaptive: 是否自适应
        """
        self.max_norm = max_norm
        self.method = method
        self.adaptive = adaptive
        
        # 自适应参数
        self.grad_norm_history = deque(maxlen=100)
        self.clip_ratio_history = deque(maxlen=100)
        
    def clip_gradients(self, parameters) -> Dict[str, float]:
        """
        裁剪梯度
        
        Args:
            parameters: 网络参数
            
        Returns:
            裁剪统计信息
        """
        # 计算梯度范数
        total_norm = 0.0
        param_count = 0
        
        for p in parameters:
            if p.grad is not None:
                param_norm = p.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
                param_count += 1
        
        total_norm = total_norm ** (1. / 2)
        
        # 记录梯度范数历史
        self.grad_norm_history.append(total_norm)
        
        # 自适应调整裁剪阈值
        if self.adaptive and len(self.grad_norm_history) >= 10:
            recent_norms = list(self.grad_norm_history)[-10:]
            adaptive_max_norm = np.percentile(recent_norms, 95)
            clip_norm = min(self.max_norm, adaptive_max_norm)
        else:
            clip_norm = self.max_norm
        
        # 执行裁剪
        clip_ratio = 1.0
        if total_norm > clip_norm:
            clip_ratio = clip_norm / total_norm
            
            if self.method == "norm":
                torch.nn.utils.clip_grad_norm_(parameters, clip_norm)
            elif self.method == "value":
                torch.nn.utils.clip_grad_value_(parameters, clip_norm)
        
        # 记录裁剪比例
        self.clip_ratio_history.append(clip_ratio)
        
        return {
            'total_norm': total_norm,
            'clip_norm': clip_norm,
            'clip_ratio': clip_ratio,
            'param_count': param_count,
            'clipped': clip_ratio < 1.0
        }
    
    def get_statistics(self) -> Dict[str, float]:
        """获取裁剪统计信息"""
        if not self.grad_norm_history:
            return {}
        
        return {
            'avg_grad_norm': np.mean(self.grad_norm_history),
            'max_grad_norm': np.max(self.grad_norm_history),
            'grad_norm_std': np.std(self.grad_norm_history),
            'avg_clip_ratio': np.mean(self.clip_ratio_history),
            'clip_frequency': np.mean([r < 1.0 for r in self.clip_ratio_history])
        }


class AnomalyDetector:
    """异常检测器"""
    
    def __init__(self, 
                 loss_spike_threshold: float = 2.0,
                 gradient_explosion_threshold: float = 10.0,
                 window_size: int = 50):
        """
        初始化异常检测器
        
        Args:
            loss_spike_threshold: 损失突增阈值
            gradient_explosion_threshold: 梯度爆炸阈值
            window_size: 监控窗口大小
        """
        self.loss_spike_threshold = loss_spike_threshold
        self.gradient_explosion_threshold = gradient_explosion_threshold
        self.window_size = window_size
        
        # 历史记录
        self.loss_history = deque(maxlen=window_size)
        self.gradient_norm_history = deque(maxlen=window_size)
        self.parameter_norm_history = deque(maxlen=window_size)
        
        # 异常计数
        self.anomaly_counts = {
            'loss_spike': 0,
            'gradient_explosion': 0,
            'nan_detected': 0,
            'inf_detected': 0,
            'parameter_explosion': 0
        }
    
    def detect_anomalies(self, 
                        loss: float,
                        gradient_norm: float,
                        parameters) -> Dict[str, Any]:
        """
        检测训练异常
        
        Args:
            loss: 当前损失
            gradient_norm: 梯度范数
            parameters: 网络参数
            
        Returns:
            异常检测结果
        """
        anomalies = []
        
        # 检测NaN和无穷大
        if torch.isnan(torch.tensor(loss)) or torch.isinf(torch.tensor(loss)):
            anomalies.append('loss_nan_inf')
            self.anomaly_counts['nan_detected'] += 1
        
        if torch.isnan(torch.tensor(gradient_norm)) or torch.isinf(torch.tensor(gradient_norm)):
            anomalies.append('gradient_nan_inf')
            self.anomaly_counts['nan_detected'] += 1
        
        # 检测参数中的NaN和无穷大
        for p in parameters:
            if p.data is not None:
                if torch.isnan(p.data).any() or torch.isinf(p.data).any():
                    anomalies.append('parameter_nan_inf')
                    self.anomaly_counts['nan_detected'] += 1
                    break
        
        # 记录历史
        self.loss_history.append(loss)
        self.gradient_norm_history.append(gradient_norm)
        
        # 计算参数范数
        param_norm = 0.0
        for p in parameters:
            if p.data is not None:
                param_norm += p.data.norm(2).item() ** 2
        param_norm = param_norm ** 0.5
        self.parameter_norm_history.append(param_norm)
        
        # 检测损失突增
        if len(self.loss_history) >= 5:
            recent_losses = list(self.loss_history)[-5:]
            avg_loss = np.mean(recent_losses[:-1])
            if loss > avg_loss * self.loss_spike_threshold:
                anomalies.append('loss_spike')
                self.anomaly_counts['loss_spike'] += 1
        
        # 检测梯度爆炸
        if gradient_norm > self.gradient_explosion_threshold:
            anomalies.append('gradient_explosion')
            self.anomaly_counts['gradient_explosion'] += 1
        
        # 检测参数爆炸
        if len(self.parameter_norm_history) >= 5:
            recent_param_norms = list(self.parameter_norm_history)[-5:]
            avg_param_norm = np.mean(recent_param_norms[:-1])
            if param_norm > avg_param_norm * 3.0:
                anomalies.append('parameter_explosion')
                self.anomaly_counts['parameter_explosion'] += 1
        
        # 计算稳定性级别
        stability_level = self._assess_stability_level(anomalies)
        
        return {
            'anomalies': anomalies,
            'stability_level': stability_level,
            'loss': loss,
            'gradient_norm': gradient_norm,
            'parameter_norm': param_norm,
            'anomaly_counts': self.anomaly_counts.copy()
        }
    
    def _assess_stability_level(self, anomalies: List[str]) -> StabilityLevel:
        """评估稳定性级别"""
        if not anomalies:
            return StabilityLevel.STABLE
        
        critical_anomalies = ['loss_nan_inf', 'gradient_nan_inf', 'parameter_nan_inf']
        severe_anomalies = ['gradient_explosion', 'parameter_explosion']
        
        if any(a in critical_anomalies for a in anomalies):
            return StabilityLevel.DIVERGING
        elif any(a in severe_anomalies for a in anomalies):
            return StabilityLevel.CRITICAL
        elif len(anomalies) > 1:
            return StabilityLevel.UNSTABLE
        else:
            return StabilityLevel.UNSTABLE
    
    def get_statistics(self) -> Dict[str, float]:
        """获取异常检测统计信息"""
        stats = self.anomaly_counts.copy()
        
        if self.loss_history:
            stats.update({
                'avg_loss': np.mean(self.loss_history),
                'loss_std': np.std(self.loss_history),
                'loss_trend': np.polyfit(range(len(self.loss_history)), 
                                       list(self.loss_history), 1)[0] if len(self.loss_history) > 1 else 0.0
            })
        
        if self.gradient_norm_history:
            stats.update({
                'avg_gradient_norm': np.mean(self.gradient_norm_history),
                'gradient_norm_std': np.std(self.gradient_norm_history)
            })
        
        return stats


class LearningRateScheduler:
    """学习率调度器"""
    
    def __init__(self, 
                 optimizer,
                 decay_factor: float = 0.5,
                 patience: int = 10,
                 min_lr: float = 1e-6,
                 threshold: float = 1e-4):
        """
        初始化学习率调度器
        
        Args:
            optimizer: 优化器
            decay_factor: 衰减因子
            patience: 耐心值
            min_lr: 最小学习率
            threshold: 改善阈值
        """
        self.optimizer = optimizer
        self.decay_factor = decay_factor
        self.patience = patience
        self.min_lr = min_lr
        self.threshold = threshold
        
        # 状态跟踪
        self.best_loss = float('inf')
        self.wait_count = 0
        self.lr_reductions = 0
        
        # 历史记录
        self.lr_history = []
        self.loss_history = deque(maxlen=100)
    
    def step(self, loss: float) -> bool:
        """
        调度学习率
        
        Args:
            loss: 当前损失
            
        Returns:
            是否调整了学习率
        """
        self.loss_history.append(loss)
        current_lr = self.optimizer.param_groups[0]['lr']
        self.lr_history.append(current_lr)
        
        # 检查是否有改善
        if loss < self.best_loss - self.threshold:
            self.best_loss = loss
            self.wait_count = 0
            return False
        else:
            self.wait_count += 1
        
        # 如果超过耐心值，降低学习率
        if self.wait_count >= self.patience:
            if current_lr > self.min_lr:
                new_lr = max(current_lr * self.decay_factor, self.min_lr)
                
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = new_lr
                
                self.wait_count = 0
                self.lr_reductions += 1
                
                return True
        
        return False
    
    def get_current_lr(self) -> float:
        """获取当前学习率"""
        return self.optimizer.param_groups[0]['lr']
    
    def get_statistics(self) -> Dict[str, float]:
        """获取调度器统计信息"""
        return {
            'current_lr': self.get_current_lr(),
            'best_loss': self.best_loss,
            'wait_count': self.wait_count,
            'lr_reductions': self.lr_reductions,
            'avg_lr': np.mean(self.lr_history) if self.lr_history else 0.0
        }


class TrainingStabilityManager:
    """训练稳定性管理器"""

    def __init__(self,
                 config: StabilityConfig,
                 optimizer,
                 logger: logging.Logger = None):
        """
        初始化训练稳定性管理器

        Args:
            config: 稳定性配置
            optimizer: 优化器
            logger: 日志记录器
        """
        self.config = config
        self.optimizer = optimizer
        self.logger = logger or logging.getLogger(__name__)

        # 组件初始化
        self.gradient_clipper = GradientClipper(
            max_norm=config.max_grad_norm,
            method=config.grad_clip_method,
            adaptive=config.adaptive_grad_clip
        )

        self.anomaly_detector = AnomalyDetector(
            loss_spike_threshold=config.loss_spike_threshold,
            gradient_explosion_threshold=config.gradient_explosion_threshold,
            window_size=config.monitoring_window
        )

        self.lr_scheduler = LearningRateScheduler(
            optimizer=optimizer,
            decay_factor=config.lr_decay_factor,
            patience=config.lr_patience,
            min_lr=config.min_lr
        )

        # 状态跟踪
        self.training_step = 0
        self.stability_violations = 0
        self.emergency_stops = 0
        self.auto_adjustments = 0

        # 性能监控
        self.performance_history = deque(maxlen=config.monitoring_window)
        self.stability_scores = deque(maxlen=config.monitoring_window)

        self.logger.info("训练稳定性管理器初始化完成")

    def process_training_step(self,
                            loss: float,
                            parameters,
                            performance_metrics: Optional[Dict] = None) -> Dict[str, Any]:
        """
        处理训练步骤，执行稳定性检查和调整

        Args:
            loss: 当前损失
            parameters: 网络参数
            performance_metrics: 性能指标

        Returns:
            处理结果
        """
        self.training_step += 1

        # 1. 梯度裁剪
        grad_clip_result = self.gradient_clipper.clip_gradients(parameters)

        # 2. 异常检测
        gradient_norm = grad_clip_result['total_norm']
        anomaly_result = self.anomaly_detector.detect_anomalies(
            loss, gradient_norm, parameters
        )

        # 3. 学习率调度
        lr_adjusted = self.lr_scheduler.step(loss)

        # 4. 稳定性评估
        stability_score = self._calculate_stability_score(
            anomaly_result, grad_clip_result, performance_metrics
        )
        self.stability_scores.append(stability_score)

        # 5. 自动调整
        adjustments = self._apply_automatic_adjustments(
            anomaly_result, stability_score
        )

        # 6. 记录性能
        if performance_metrics:
            self.performance_history.append(performance_metrics)

        # 7. 生成报告
        result = {
            'training_step': self.training_step,
            'loss': loss,
            'gradient_norm': gradient_norm,
            'stability_score': stability_score,
            'stability_level': anomaly_result['stability_level'],
            'grad_clip_result': grad_clip_result,
            'anomaly_result': anomaly_result,
            'lr_adjusted': lr_adjusted,
            'current_lr': self.lr_scheduler.get_current_lr(),
            'adjustments': adjustments,
            'emergency_stop': adjustments.get('emergency_stop', False)
        }

        # 8. 日志记录
        self._log_stability_status(result)

        return result

    def _calculate_stability_score(self,
                                 anomaly_result: Dict,
                                 grad_clip_result: Dict,
                                 performance_metrics: Optional[Dict]) -> float:
        """计算稳定性得分"""
        score = 1.0

        # 异常惩罚
        anomaly_count = len(anomaly_result['anomalies'])
        score -= anomaly_count * 0.2

        # 梯度裁剪惩罚
        if grad_clip_result['clipped']:
            score -= 0.1 * (1.0 - grad_clip_result['clip_ratio'])

        # 稳定性级别惩罚
        stability_level = anomaly_result['stability_level']
        if stability_level == StabilityLevel.UNSTABLE:
            score -= 0.2
        elif stability_level == StabilityLevel.CRITICAL:
            score -= 0.4
        elif stability_level == StabilityLevel.DIVERGING:
            score -= 0.8

        # 性能指标奖励
        if performance_metrics:
            if 'success_rate' in performance_metrics:
                score += 0.1 * performance_metrics['success_rate']
            if 'efficiency' in performance_metrics:
                score += 0.1 * performance_metrics['efficiency']

        return max(0.0, min(1.0, score))

    def _apply_automatic_adjustments(self,
                                   anomaly_result: Dict,
                                   stability_score: float) -> Dict[str, Any]:
        """应用自动调整"""
        adjustments = {}

        stability_level = anomaly_result['stability_level']

        # 紧急停止条件
        if stability_level == StabilityLevel.DIVERGING:
            adjustments['emergency_stop'] = True
            self.emergency_stops += 1
            self.logger.error(f"检测到训练发散，执行紧急停止 (步骤 {self.training_step})")

        # 学习率调整
        elif stability_level == StabilityLevel.CRITICAL:
            current_lr = self.lr_scheduler.get_current_lr()
            new_lr = current_lr * 0.1  # 大幅降低学习率

            for param_group in self.optimizer.param_groups:
                param_group['lr'] = max(new_lr, self.config.min_lr)

            adjustments['lr_emergency_reduction'] = True
            adjustments['new_lr'] = self.lr_scheduler.get_current_lr()
            self.auto_adjustments += 1
            self.logger.warning(f"检测到危险状态，紧急降低学习率到 {adjustments['new_lr']}")

        # 梯度裁剪调整
        elif stability_score < self.config.stability_threshold:
            # 降低梯度裁剪阈值
            self.gradient_clipper.max_norm *= 0.8
            adjustments['grad_clip_reduction'] = True
            adjustments['new_grad_clip'] = self.gradient_clipper.max_norm
            self.auto_adjustments += 1
            self.logger.info(f"降低梯度裁剪阈值到 {adjustments['new_grad_clip']}")

        # 稳定性违规计数
        if stability_score < self.config.stability_threshold:
            self.stability_violations += 1

        return adjustments

    def _log_stability_status(self, result: Dict):
        """记录稳定性状态"""
        if result['emergency_stop']:
            self.logger.error(f"紧急停止 - 步骤 {result['training_step']}")
        elif result['stability_level'] == StabilityLevel.CRITICAL:
            self.logger.warning(f"危险状态 - 步骤 {result['training_step']}, 稳定性得分: {result['stability_score']:.3f}")
        elif result['stability_level'] == StabilityLevel.UNSTABLE:
            self.logger.info(f"不稳定状态 - 步骤 {result['training_step']}, 稳定性得分: {result['stability_score']:.3f}")

        # 定期报告
        if self.training_step % 1000 == 0:
            stats = self.get_comprehensive_statistics()
            self.logger.info(f"稳定性报告 (步骤 {self.training_step}): "
                           f"平均稳定性得分: {stats['avg_stability_score']:.3f}, "
                           f"违规次数: {stats['stability_violations']}, "
                           f"自动调整次数: {stats['auto_adjustments']}")

    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        stats = {
            'training_step': self.training_step,
            'stability_violations': self.stability_violations,
            'emergency_stops': self.emergency_stops,
            'auto_adjustments': self.auto_adjustments
        }

        # 稳定性统计
        if self.stability_scores:
            stats.update({
                'avg_stability_score': np.mean(self.stability_scores),
                'min_stability_score': np.min(self.stability_scores),
                'stability_trend': np.polyfit(range(len(self.stability_scores)),
                                            list(self.stability_scores), 1)[0] if len(self.stability_scores) > 1 else 0.0
            })

        # 组件统计
        stats.update({
            'gradient_clipper': self.gradient_clipper.get_statistics(),
            'anomaly_detector': self.anomaly_detector.get_statistics(),
            'lr_scheduler': self.lr_scheduler.get_statistics()
        })

        # 性能统计
        if self.performance_history:
            recent_performance = list(self.performance_history)[-10:]
            if recent_performance and 'success_rate' in recent_performance[0]:
                stats['recent_avg_success_rate'] = np.mean([p['success_rate'] for p in recent_performance])
            if recent_performance and 'efficiency' in recent_performance[0]:
                stats['recent_avg_efficiency'] = np.mean([p['efficiency'] for p in recent_performance])

        return stats

    def reset(self):
        """重置管理器状态"""
        self.training_step = 0
        self.stability_violations = 0
        self.emergency_stops = 0
        self.auto_adjustments = 0

        self.performance_history.clear()
        self.stability_scores.clear()

        # 重置组件
        self.gradient_clipper = GradientClipper(
            max_norm=self.config.max_grad_norm,
            method=self.config.grad_clip_method,
            adaptive=self.config.adaptive_grad_clip
        )

        self.anomaly_detector = AnomalyDetector(
            loss_spike_threshold=self.config.loss_spike_threshold,
            gradient_explosion_threshold=self.config.gradient_explosion_threshold,
            window_size=self.config.monitoring_window
        )

        self.logger.info("训练稳定性管理器已重置")

    def should_stop_training(self) -> bool:
        """判断是否应该停止训练"""
        # 连续紧急停止
        if self.emergency_stops >= 3:
            return True

        # 稳定性持续恶化
        if len(self.stability_scores) >= 50:
            recent_scores = list(self.stability_scores)[-50:]
            if np.mean(recent_scores) < 0.3:
                return True

        return False
