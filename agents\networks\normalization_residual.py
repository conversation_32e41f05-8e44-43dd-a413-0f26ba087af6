"""
层归一化和残差连接

实现注意力融合过程中的层归一化和残差连接，保证训练稳定性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List, Union


class AttentionLayerNorm(nn.Module):
    """注意力层归一化"""
    
    def __init__(self, 
                 embed_dim: int,
                 eps: float = 1e-6,
                 elementwise_affine: bool = True,
                 bias: bool = True):
        """
        初始化注意力层归一化
        
        Args:
            embed_dim: 嵌入维度
            eps: 数值稳定性参数
            elementwise_affine: 是否使用可学习的仿射变换
            bias: 是否使用偏置
        """
        super(AttentionLayerNorm, self).__init__()
        
        self.embed_dim = embed_dim
        self.eps = eps
        self.elementwise_affine = elementwise_affine
        
        if elementwise_affine:
            self.weight = nn.Parameter(torch.ones(embed_dim))
            if bias:
                self.bias = nn.Parameter(torch.zeros(embed_dim))
            else:
                self.register_parameter('bias', None)
        else:
            self.register_parameter('weight', None)
            self.register_parameter('bias', None)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # 计算均值和方差
        mean = x.mean(dim=-1, keepdim=True)
        var = ((x - mean) ** 2).mean(dim=-1, keepdim=True)
        
        # 归一化
        x_norm = (x - mean) / torch.sqrt(var + self.eps)
        
        # 仿射变换
        if self.elementwise_affine:
            x_norm = x_norm * self.weight
            if self.bias is not None:
                x_norm = x_norm + self.bias
        
        return x_norm


class AdaptiveLayerNorm(nn.Module):
    """自适应层归一化"""
    
    def __init__(self, 
                 embed_dim: int,
                 condition_dim: int,
                 eps: float = 1e-6):
        """
        初始化自适应层归一化
        
        Args:
            embed_dim: 嵌入维度
            condition_dim: 条件维度
            eps: 数值稳定性参数
        """
        super(AdaptiveLayerNorm, self).__init__()
        
        self.embed_dim = embed_dim
        self.condition_dim = condition_dim
        self.eps = eps
        
        # 条件网络
        self.condition_net = nn.Sequential(
            nn.Linear(condition_dim, embed_dim * 2),
            nn.ReLU(),
            nn.Linear(embed_dim * 2, embed_dim * 2)
        )
    
    def forward(self, x: torch.Tensor, condition: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征 [batch_size, seq_len, embed_dim]
            condition: 条件特征 [batch_size, condition_dim]
            
        Returns:
            normalized_x: 归一化后的特征
        """
        # 计算均值和方差
        mean = x.mean(dim=-1, keepdim=True)
        var = ((x - mean) ** 2).mean(dim=-1, keepdim=True)
        
        # 标准归一化
        x_norm = (x - mean) / torch.sqrt(var + self.eps)
        
        # 条件仿射变换
        condition_params = self.condition_net(condition)  # [batch_size, embed_dim * 2]
        gamma, beta = torch.chunk(condition_params, 2, dim=-1)  # [batch_size, embed_dim]
        
        # 扩展维度以匹配x_norm
        gamma = gamma.unsqueeze(1)  # [batch_size, 1, embed_dim]
        beta = beta.unsqueeze(1)    # [batch_size, 1, embed_dim]
        
        # 应用条件仿射变换
        x_adaptive = x_norm * (1 + gamma) + beta
        
        return x_adaptive


class ResidualConnection(nn.Module):
    """残差连接"""
    
    def __init__(self, 
                 embed_dim: int,
                 dropout: float = 0.1,
                 use_layer_norm: bool = True,
                 norm_type: str = "layer_norm"):
        """
        初始化残差连接
        
        Args:
            embed_dim: 嵌入维度
            dropout: Dropout概率
            use_layer_norm: 是否使用层归一化
            norm_type: 归一化类型
        """
        super(ResidualConnection, self).__init__()
        
        self.embed_dim = embed_dim
        self.dropout = nn.Dropout(dropout)
        self.use_layer_norm = use_layer_norm
        
        if use_layer_norm:
            if norm_type == "layer_norm":
                self.norm = nn.LayerNorm(embed_dim)
            elif norm_type == "attention_norm":
                self.norm = AttentionLayerNorm(embed_dim)
            else:
                self.norm = nn.LayerNorm(embed_dim)
    
    def forward(self, x: torch.Tensor, sublayer_output: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征（残差连接的起点）
            sublayer_output: 子层输出
            
        Returns:
            output: 残差连接后的输出
        """
        # 应用dropout
        sublayer_output = self.dropout(sublayer_output)
        
        # 残差连接
        output = x + sublayer_output
        
        # 层归一化
        if self.use_layer_norm:
            output = self.norm(output)
        
        return output


class PreNormResidualConnection(nn.Module):
    """预归一化残差连接"""
    
    def __init__(self, 
                 embed_dim: int,
                 dropout: float = 0.1,
                 norm_type: str = "layer_norm"):
        """
        初始化预归一化残差连接
        
        Args:
            embed_dim: 嵌入维度
            dropout: Dropout概率
            norm_type: 归一化类型
        """
        super(PreNormResidualConnection, self).__init__()
        
        self.embed_dim = embed_dim
        self.dropout = nn.Dropout(dropout)
        
        if norm_type == "layer_norm":
            self.norm = nn.LayerNorm(embed_dim)
        elif norm_type == "attention_norm":
            self.norm = AttentionLayerNorm(embed_dim)
        else:
            self.norm = nn.LayerNorm(embed_dim)
    
    def forward(self, x: torch.Tensor, sublayer_fn) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征
            sublayer_fn: 子层函数
            
        Returns:
            output: 预归一化残差连接后的输出
        """
        # 预归一化
        x_norm = self.norm(x)
        
        # 子层计算
        sublayer_output = sublayer_fn(x_norm)
        
        # 应用dropout
        sublayer_output = self.dropout(sublayer_output)
        
        # 残差连接
        output = x + sublayer_output
        
        return output


class AttentionResidualBlock(nn.Module):
    """注意力残差块"""
    
    def __init__(self, 
                 embed_dim: int,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 use_pre_norm: bool = True,
                 use_adaptive_norm: bool = False,
                 condition_dim: Optional[int] = None):
        """
        初始化注意力残差块
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            dropout: Dropout概率
            use_pre_norm: 是否使用预归一化
            use_adaptive_norm: 是否使用自适应归一化
            condition_dim: 条件维度（用于自适应归一化）
        """
        super(AttentionResidualBlock, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.use_pre_norm = use_pre_norm
        self.use_adaptive_norm = use_adaptive_norm
        
        # 多头注意力
        self.attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim * 4, embed_dim)
        )
        
        # 归一化和残差连接
        if use_adaptive_norm and condition_dim is not None:
            self.norm1 = AdaptiveLayerNorm(embed_dim, condition_dim)
            self.norm2 = AdaptiveLayerNorm(embed_dim, condition_dim)
        else:
            self.norm1 = AttentionLayerNorm(embed_dim)
            self.norm2 = AttentionLayerNorm(embed_dim)
        
        if use_pre_norm:
            self.residual1 = PreNormResidualConnection(embed_dim, dropout)
            self.residual2 = PreNormResidualConnection(embed_dim, dropout)
        else:
            self.residual1 = ResidualConnection(embed_dim, dropout)
            self.residual2 = ResidualConnection(embed_dim, dropout)
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, 
                x: torch.Tensor,
                condition: Optional[torch.Tensor] = None,
                attention_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入特征 [batch_size, seq_len, embed_dim]
            condition: 条件特征（用于自适应归一化）
            attention_mask: 注意力掩码
            
        Returns:
            output_dict: 输出字典
        """
        # 第一个残差块：自注意力
        if self.use_pre_norm:
            def attention_fn(x_norm):
                attn_output, attn_weights = self.attention(x_norm, x_norm, x_norm, attn_mask=attention_mask)
                return attn_output

            x1 = self.residual1(x, attention_fn)
            # 重新计算注意力权重用于返回
            _, attn_weights = self.attention(x1, x1, x1, attn_mask=attention_mask)
        else:
            attn_output, attn_weights = self.attention(x, x, x, attn_mask=attention_mask)
            x1 = self.residual1(x, attn_output)
        
        # 自适应归一化
        if self.use_adaptive_norm and condition is not None:
            x1 = self.norm1(x1, condition)
        
        # 第二个残差块：前馈网络
        if self.use_pre_norm:
            def ff_fn(x_norm):
                return self.feed_forward(x_norm)
            
            x2 = self.residual2(x1, ff_fn)
        else:
            ff_output = self.feed_forward(x1)
            x2 = self.residual2(x1, ff_output)
        
        # 自适应归一化
        if self.use_adaptive_norm and condition is not None:
            x2 = self.norm2(x2, condition)
        
        return {
            'output': x2,
            'attention_weights': attn_weights,
            'intermediate': x1
        }


class FusionResidualBlock(nn.Module):
    """融合残差块"""
    
    def __init__(self, 
                 embed_dim: int,
                 num_inputs: int = 2,
                 dropout: float = 0.1,
                 use_gate: bool = True,
                 use_layer_norm: bool = True):
        """
        初始化融合残差块
        
        Args:
            embed_dim: 嵌入维度
            num_inputs: 输入数量
            dropout: Dropout概率
            use_gate: 是否使用门控机制
            use_layer_norm: 是否使用层归一化
        """
        super(FusionResidualBlock, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_inputs = num_inputs
        self.use_gate = use_gate
        self.use_layer_norm = use_layer_norm
        
        # 输入投影
        self.input_projections = nn.ModuleList([
            nn.Linear(embed_dim, embed_dim) for _ in range(num_inputs)
        ])
        
        # 门控机制
        if use_gate:
            self.gate_network = nn.Sequential(
                nn.Linear(embed_dim * num_inputs, embed_dim),
                nn.ReLU(),
                nn.Linear(embed_dim, num_inputs),
                nn.Sigmoid()
            )
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim * 2, embed_dim)
        )
        
        # 残差连接
        self.residual = ResidualConnection(embed_dim, dropout, use_layer_norm)
    
    def forward(self, inputs: List[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            inputs: 输入特征列表
            
        Returns:
            output_dict: 输出字典
        """
        assert len(inputs) == self.num_inputs, f"Expected {self.num_inputs} inputs, got {len(inputs)}"
        
        # 输入投影
        projected_inputs = []
        for i, input_tensor in enumerate(inputs):
            projected = self.input_projections[i](input_tensor)
            projected_inputs.append(projected)
        
        # 门控融合
        if self.use_gate:
            # 计算门控权重
            concatenated = torch.cat(projected_inputs, dim=-1)
            gate_weights = self.gate_network(concatenated)  # [batch_size, seq_len, num_inputs]
            
            # 加权融合
            weighted_inputs = []
            for i, projected in enumerate(projected_inputs):
                weight = gate_weights[:, :, i:i+1]  # [batch_size, seq_len, 1]
                weighted = weight * projected
                weighted_inputs.append(weighted)
            
            fused = sum(weighted_inputs)
        else:
            # 简单平均融合
            fused = sum(projected_inputs) / len(projected_inputs)
        
        # 融合网络
        fusion_output = self.fusion_network(fused)
        
        # 残差连接（使用第一个输入作为残差）
        output = self.residual(inputs[0], fusion_output)
        
        return {
            'output': output,
            'fused': fused,
            'gate_weights': gate_weights if self.use_gate else None,
            'projected_inputs': projected_inputs
        }


class StabilizedAttentionFusion(nn.Module):
    """稳定化注意力融合"""
    
    def __init__(self, 
                 embed_dim: int,
                 num_layers: int = 3,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 use_gradient_checkpointing: bool = False):
        """
        初始化稳定化注意力融合
        
        Args:
            embed_dim: 嵌入维度
            num_layers: 层数
            num_heads: 注意力头数
            dropout: Dropout概率
            use_gradient_checkpointing: 是否使用梯度检查点
        """
        super(StabilizedAttentionFusion, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_layers = num_layers
        self.use_gradient_checkpointing = use_gradient_checkpointing
        
        # 注意力残差块
        self.attention_blocks = nn.ModuleList([
            AttentionResidualBlock(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout,
                use_pre_norm=True
            ) for _ in range(num_layers)
        ])
        
        # 融合残差块
        self.fusion_block = FusionResidualBlock(
            embed_dim=embed_dim,
            num_inputs=2,  # 任务注意力 + 协作注意力
            dropout=dropout,
            use_gate=True
        )
        
        # 最终归一化
        self.final_norm = AttentionLayerNorm(embed_dim)
        
        # 梯度缩放
        self.gradient_scaler = GradientScaler()
    
    def forward(self, 
                task_attention_output: torch.Tensor,
                collaboration_attention_output: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            task_attention_output: 任务注意力输出
            collaboration_attention_output: 协作注意力输出
            attention_mask: 注意力掩码
            
        Returns:
            output_dict: 输出字典
        """
        # 融合两个注意力输出
        fusion_result = self.fusion_block([task_attention_output, collaboration_attention_output])
        fused_output = fusion_result['output']
        
        # 通过注意力残差块
        attention_outputs = []
        current_output = fused_output
        
        for i, attention_block in enumerate(self.attention_blocks):
            if self.use_gradient_checkpointing and self.training:
                # 使用梯度检查点节省内存
                block_result = torch.utils.checkpoint.checkpoint(
                    attention_block, current_output, None, attention_mask
                )
            else:
                block_result = attention_block(current_output, attention_mask=attention_mask)
            
            current_output = block_result['output']
            attention_outputs.append(block_result)
        
        # 最终归一化
        final_output = self.final_norm(current_output)
        
        # 梯度缩放
        final_output = self.gradient_scaler(final_output)
        
        return {
            'fused_output': final_output,
            'fusion_result': fusion_result,
            'attention_outputs': attention_outputs,
            'intermediate_output': current_output
        }


class GradientScaler(nn.Module):
    """梯度缩放器"""
    
    def __init__(self, scale_factor: float = 1.0):
        super(GradientScaler, self).__init__()
        self.scale_factor = scale_factor
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播时不变，反向传播时缩放梯度"""
        return GradientScaleFunction.apply(x, self.scale_factor)


class GradientScaleFunction(torch.autograd.Function):
    """梯度缩放函数"""
    
    @staticmethod
    def forward(ctx, x, scale_factor):
        ctx.scale_factor = scale_factor
        return x
    
    @staticmethod
    def backward(ctx, grad_output):
        return grad_output * ctx.scale_factor, None
