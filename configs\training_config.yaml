# 注意力增强MAPPO训练配置文件

# 环境配置
environment:
  width: 26                    # 仓库宽度
  height: 10                   # 仓库高度
  num_agvs: 4                  # AGV数量（将通过课程学习动态调整）
  num_shelves: 15              # 货架数量
  max_tasks: 8                 # 最大任务数（将通过课程学习动态调整）
  render_mode: "rgb_array"     # 渲染模式
  
# MAPPO算法配置
mappo:
  # 网络架构
  hidden_dim: 256              # 隐藏层维度
  num_layers: 3                # 网络层数
  attention_embed_dim: 64      # 注意力嵌入维度
  
  # 学习参数
  learning_rate: 3e-4          # 学习率
  gamma: 0.99                  # 折扣因子
  gae_lambda: 0.95             # GAE lambda参数
  
  # PPO参数
  clip_ratio: 0.2              # PPO裁剪比例
  entropy_coef: 0.01           # 熵系数
  value_coef: 0.5              # 价值损失系数
  max_grad_norm: 0.5           # 最大梯度范数
  
  # 训练参数
  batch_size: 256              # 批次大小
  mini_batch_size: 64          # 小批次大小
  update_epochs: 4             # 更新轮数
  
# 注意力机制配置
attention:
  # 第一层：任务分配注意力
  agv_embed_dim: 64            # AGV嵌入维度
  task_embed_dim: 64           # 任务嵌入维度
  num_heads: 8                 # 注意力头数
  top_k: 5                     # Top-K稀疏化参数
  use_constraints: true        # 是否使用约束增强
  use_temporal_consistency: true  # 是否使用时序一致性
  
  # 第二层：协作感知注意力
  embed_dim: 64                # 嵌入维度
  distance_thresholds: [5.0, 15.0, 30.0]  # 距离阈值
  collaboration_types: ["close", "medium", "far"]  # 协作类型
  use_adaptive_temperature: true           # 是否使用自适应温度
  use_enhanced_state_representation: true  # 是否使用增强状态表示
  use_information_aggregation: true        # 是否使用信息聚合
  
# 训练优化策略配置
optimization:
  # 组件开关
  use_curriculum_learning: true      # 是否使用课程学习
  use_prioritized_replay: true       # 是否使用优先级经验回放
  use_stability_manager: true        # 是否使用稳定性管理
  use_environment_adaptation: true   # 是否使用环境适应
  use_maml: false                    # 是否使用MAML（较复杂，默认关闭）
  
  # 课程学习配置
  curriculum:
    stage_1_episodes: 5000           # 阶段1回合数（2个AGV）
    stage_2_episodes: 7500           # 阶段2回合数（3个AGV）
    stage_3_episodes: 10000          # 阶段3回合数（4个AGV）
    promotion_threshold: 0.8         # 晋级阈值
    demotion_threshold: 0.4          # 降级阈值
    performance_window: 1000         # 性能评估窗口
    
  # 优先级经验回放配置
  prioritized_replay:
    buffer_size: 100000              # 缓冲区大小
    alpha: 0.6                       # 优先级指数
    beta: 0.4                        # 重要性采样指数
    beta_increment: 0.001            # beta增长率
    batch_size: 256                  # 批次大小
    min_experiences: 1000            # 最少经验数
    
  # 训练稳定性配置
  stability:
    max_grad_norm: 0.5               # 最大梯度范数
    loss_spike_threshold: 2.0        # 损失突增阈值
    gradient_explosion_threshold: 10.0  # 梯度爆炸阈值
    lr_decay_factor: 0.5             # 学习率衰减因子
    lr_patience: 10                  # 学习率调整耐心
    
  # 环境适应配置
  adaptation:
    detection_window: 100            # 检测窗口大小
    change_threshold: 0.2            # 变化阈值
    sudden_change_threshold: 0.5     # 突然变化阈值
    adaptation_lr_multiplier: 2.0    # 适应学习率倍数
    
# 训练配置
training:
  total_episodes: 25000              # 总训练回合数
  eval_frequency: 1000               # 评估频率
  save_frequency: 5000               # 保存频率
  log_frequency: 100                 # 日志频率
  
  # 早停配置
  early_stopping:
    patience: 5000                   # 早停耐心
    min_delta: 0.01                  # 最小改善
    
# 评估配置
evaluation:
  num_eval_episodes: 100             # 评估回合数
  eval_deterministic: true           # 是否确定性评估
  render_eval: false                 # 是否渲染评估
  
# 日志配置
logging:
  level: "INFO"                      # 日志级别
  save_logs: true                    # 是否保存日志
  tensorboard: true                  # 是否使用TensorBoard
  
  # 记录指标
  metrics:
    - "episode_reward"               # 回合奖励
    - "success_rate"                 # 成功率
    - "efficiency"                   # 效率
    - "collision_rate"               # 碰撞率
    - "cooperation_score"            # 协作得分
    - "attention_entropy"            # 注意力熵
    - "curriculum_stage"             # 课程阶段
    - "stability_score"              # 稳定性得分
    
# 实验配置
experiment:
  name: "attention_enhanced_mappo"   # 实验名称
  description: "双层注意力增强的MAPPO多AGV仓储任务"
  tags: ["attention", "mappo", "multi-agent", "warehouse"]
  
  # 随机种子
  seed: 42                           # 随机种子
  deterministic: false               # 是否确定性
  
  # 设备配置
  device: "auto"                     # 设备选择（auto/cpu/cuda）
  num_workers: 4                     # 工作进程数
  
# 模型保存配置
model:
  save_best: true                    # 是否保存最佳模型
  save_last: true                    # 是否保存最后模型
  save_frequency: 5000               # 保存频率
  
  # 检查点配置
  checkpoint:
    save_optimizer: true             # 是否保存优化器状态
    save_scheduler: true             # 是否保存调度器状态
    save_training_state: true       # 是否保存训练状态
    
# 可视化配置
visualization:
  plot_training_curves: true        # 是否绘制训练曲线
  plot_attention_maps: true         # 是否绘制注意力图
  plot_collaboration_network: true  # 是否绘制协作网络
  save_videos: false                # 是否保存视频（较耗时）
  
  # 注意力可视化
  attention_visualization:
    frequency: 1000                  # 可视化频率
    save_heatmaps: true             # 是否保存热力图
    save_statistics: true           # 是否保存统计信息
