#!/usr/bin/env python3
"""
标准训练启动脚本
基于融合双层注意力机制的MAPPO多AGV协同调度系统

使用方法:
python start_training.py [--config CONFIG_FILE] [--mode MODE]

模式选项:
- demo: 快速演示训练 (50 episodes)
- standard: 标准训练 (2500 episodes, 课程学习)
- full: 完整训练 (5000 episodes)
- custom: 自定义配置训练
"""

import argparse
import sys
import os
import yaml
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'torch', 'numpy', 'matplotlib', 'yaml'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包检查通过")
    return True

def load_config(config_path: str):
    """加载配置文件"""
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return None
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print(f"✅ 配置文件加载成功: {config_path}")
        return config
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None

def create_experiment_config(mode: str, base_config: dict):
    """根据训练模式创建实验配置"""
    experiment_config = base_config.copy()
    
    if mode == "demo":
        # 快速演示配置
        experiment_config.update({
            'total_episodes': 50,
            'num_agvs': 2,
            'num_tasks': 4,
            'max_episode_steps': 100,
            'log_interval': 5,
            'save_interval': 25
        })
        print("🎯 演示模式: 50个episodes, 2个AGV")
        
    elif mode == "standard":
        # 标准训练配置（课程学习）
        experiment_config.update({
            'total_episodes': 2500,
            'enable_curriculum': True,
            'log_interval': 10,
            'save_interval': 200
        })
        print("🎯 标准模式: 2500个episodes, 课程学习")
        
    elif mode == "full":
        # 完整训练配置
        experiment_config.update({
            'total_episodes': 5000,
            'enable_curriculum': True,
            'log_interval': 10,
            'save_interval': 500
        })
        print("🎯 完整模式: 5000个episodes, 完整课程学习")
        
    return experiment_config

def setup_experiment_directory():
    """设置实验目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_name = f"standard_training_{timestamp}"
    experiment_dir = Path("experiments") / experiment_name
    experiment_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 实验目录: {experiment_dir}")
    return experiment_dir

def start_training(config_path: str, mode: str):
    """启动训练"""
    print("🚀 开始标准训练...")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 加载配置
    if config_path:
        config = load_config(config_path)
        if config is None:
            return False
    else:
        # 使用默认配置
        config = {}
        print("📋 使用默认配置")
    
    # 创建实验配置
    experiment_config = create_experiment_config(mode, config)
    
    # 设置实验目录
    experiment_dir = setup_experiment_directory()
    
    # 保存实验配置
    config_file = experiment_dir / "config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(experiment_config, f, indent=2, ensure_ascii=False)
    
    print(f"💾 实验配置已保存: {config_file}")
    print("=" * 60)
    
    # 导入并启动训练
    try:
        from complete_agv_system import Config, MAPPOTrainer
        
        # 创建训练配置对象
        training_config = Config()
        
        # 更新配置参数
        for key, value in experiment_config.items():
            if hasattr(training_config, key):
                setattr(training_config, key, value)
        
        # 设置实验目录
        training_config.experiment_dir = str(experiment_dir)
        
        print("🔧 训练配置:")
        print(f"   - Episodes: {training_config.total_episodes}")
        print(f"   - AGV数量: {training_config.num_agvs}")
        print(f"   - 任务数量: {training_config.num_tasks}")
        print(f"   - 学习率: {training_config.learning_rate}")
        print(f"   - 实验目录: {experiment_dir}")
        print("=" * 60)
        
        # 创建训练器
        print("🏗️  创建MAPPO训练器...")
        trainer = MAPPOTrainer(training_config)
        
        # 开始训练
        print("🎯 开始训练...")
        rewards = trainer.train()
        
        print("=" * 60)
        print("🎉 训练完成!")
        print(f"📊 平均奖励: {sum(rewards)/len(rewards):.2f}")
        print(f"🏆 最佳奖励: {max(rewards):.2f}")
        print(f"📁 结果保存在: {experiment_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MAPPO多AGV协同调度系统训练启动器")
    parser.add_argument("--config", "-c", type=str, default="configs/training.yaml",
                       help="配置文件路径 (默认: configs/training.yaml)")
    parser.add_argument("--mode", "-m", type=str, default="standard",
                       choices=["demo", "standard", "full", "custom"],
                       help="训练模式 (默认: standard)")
    
    args = parser.parse_args()
    
    print("🤖 基于融合双层注意力机制的MAPPO多AGV协同调度系统")
    print("🎯 标准训练启动器")
    print("=" * 60)
    
    # 启动训练
    success = start_training(args.config, args.mode)
    
    if success:
        print("\n✅ 训练任务完成")
    else:
        print("\n❌ 训练任务失败")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
