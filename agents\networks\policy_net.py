"""
策略网络架构

实现基础的策略网络，包括局部观察处理、特征提取和动作生成
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List
from utils.math_utils import softmax_with_temperature


class PolicyNetwork(nn.Module):
    """MAPPO策略网络"""
    
    def __init__(self, 
                 agv_state_dim: int = 10,
                 task_state_dim: int = 11,
                 global_state_shape: Tuple[int, int] = (10, 26),
                 action_dim: int = 7,
                 hidden_dims: List[int] = None,
                 activation: str = "relu",
                 layer_norm: bool = True,
                 dropout: float = 0.1):
        """
        初始化策略网络
        
        Args:
            agv_state_dim: AGV状态维度
            task_state_dim: 任务状态维度
            global_state_shape: 全局状态形状 (height, width)
            action_dim: 动作维度
            hidden_dims: 隐藏层维度列表
            activation: 激活函数类型
            layer_norm: 是否使用层归一化
            dropout: Dropout概率
        """
        super(PolicyNetwork, self).__init__()
        
        self.agv_state_dim = agv_state_dim
        self.task_state_dim = task_state_dim
        self.global_state_shape = global_state_shape
        self.action_dim = action_dim
        self.hidden_dims = hidden_dims or [512, 256]
        self.layer_norm = layer_norm
        self.dropout = dropout
        
        # 激活函数
        if activation == "relu":
            self.activation = nn.ReLU()
        elif activation == "tanh":
            self.activation = nn.Tanh()
        elif activation == "gelu":
            self.activation = nn.GELU()
        else:
            self.activation = nn.ReLU()
        
        # AGV状态编码器
        self.agv_encoder = nn.Sequential(
            nn.Linear(agv_state_dim, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(64, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 任务状态编码器
        self.task_encoder = nn.Sequential(
            nn.Linear(task_state_dim, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout),
            nn.Linear(64, 64),
            nn.LayerNorm(64) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 全局状态编码器（CNN）
        self.global_encoder = nn.Sequential(
            nn.Conv2d(1, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32) if layer_norm else nn.Identity(),
            self.activation,
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64) if layer_norm else nn.Identity(),
            self.activation,
            nn.AdaptiveAvgPool2d((4, 4)),  # 自适应池化到固定大小
            nn.Flatten(),
            nn.Linear(64 * 4 * 4, 128),
            nn.LayerNorm(128) if layer_norm else nn.Identity(),
            self.activation
        )
        
        # 特征融合层
        # AGV特征(64) + 任务特征(64) + 全局特征(128) = 256
        fusion_input_dim = 64 + 64 + 128
        
        self.feature_fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, self.hidden_dims[0]),
            nn.LayerNorm(self.hidden_dims[0]) if layer_norm else nn.Identity(),
            self.activation,
            nn.Dropout(dropout)
        )
        
        # 策略头部网络
        policy_layers = []
        input_dim = self.hidden_dims[0]
        
        for hidden_dim in self.hidden_dims[1:]:
            policy_layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.LayerNorm(hidden_dim) if layer_norm else nn.Identity(),
                self.activation,
                nn.Dropout(dropout)
            ])
            input_dim = hidden_dim
        
        self.policy_head = nn.Sequential(*policy_layers)
        
        # 动作输出层
        self.action_logits = nn.Linear(input_dim, action_dim)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Conv2d):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
    
    def forward(self, 
                agv_state: torch.Tensor,
                task_states: torch.Tensor,
                global_state: torch.Tensor,
                action_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            agv_state: AGV状态 [batch_size, agv_state_dim]
            task_states: 任务状态 [batch_size, num_tasks, task_state_dim]
            global_state: 全局状态 [batch_size, height, width]
            action_mask: 动作掩码 [batch_size, action_dim]
            
        Returns:
            action_logits: 动作logits [batch_size, action_dim]
            action_probs: 动作概率 [batch_size, action_dim]
        """
        batch_size = agv_state.size(0)
        
        # 编码AGV状态
        agv_features = self.agv_encoder(agv_state)  # [batch_size, 64]
        
        # 编码任务状态（聚合所有任务）
        task_features = self.task_encoder(task_states)  # [batch_size, num_tasks, 64]
        task_features = torch.mean(task_features, dim=1)  # [batch_size, 64] 简单平均聚合
        
        # 编码全局状态
        global_state = global_state.unsqueeze(1)  # [batch_size, 1, height, width]
        global_features = self.global_encoder(global_state)  # [batch_size, 128]
        
        # 特征融合
        fused_features = torch.cat([agv_features, task_features, global_features], dim=1)
        fused_features = self.feature_fusion(fused_features)  # [batch_size, hidden_dims[0]]
        
        # 策略头部
        policy_features = self.policy_head(fused_features)  # [batch_size, hidden_dims[-1]]
        
        # 动作logits
        action_logits = self.action_logits(policy_features)  # [batch_size, action_dim]
        
        # 应用动作掩码
        if action_mask is not None:
            # 将无效动作的logits设为很小的值
            action_logits = action_logits + (action_mask - 1) * 1e8
        
        # 计算动作概率
        action_probs = F.softmax(action_logits, dim=-1)
        
        return action_logits, action_probs
    
    def get_action_distribution(self, 
                              agv_state: torch.Tensor,
                              task_states: torch.Tensor,
                              global_state: torch.Tensor,
                              action_mask: Optional[torch.Tensor] = None,
                              temperature: float = 1.0) -> torch.distributions.Categorical:
        """
        获取动作分布
        
        Args:
            agv_state: AGV状态
            task_states: 任务状态
            global_state: 全局状态
            action_mask: 动作掩码
            temperature: 温度参数
            
        Returns:
            动作分布
        """
        action_logits, _ = self.forward(agv_state, task_states, global_state, action_mask)
        
        # 应用温度
        if temperature != 1.0:
            action_logits = action_logits / temperature
        
        return torch.distributions.Categorical(logits=action_logits)
    
    def sample_action(self, 
                     agv_state: torch.Tensor,
                     task_states: torch.Tensor,
                     global_state: torch.Tensor,
                     action_mask: Optional[torch.Tensor] = None,
                     deterministic: bool = False,
                     temperature: float = 1.0) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        采样动作
        
        Args:
            agv_state: AGV状态
            task_states: 任务状态
            global_state: 全局状态
            action_mask: 动作掩码
            deterministic: 是否确定性采样
            temperature: 温度参数
            
        Returns:
            actions: 采样的动作 [batch_size]
            log_probs: 动作的对数概率 [batch_size]
            entropy: 策略熵 [batch_size]
        """
        dist = self.get_action_distribution(agv_state, task_states, global_state, action_mask, temperature)
        
        if deterministic:
            actions = dist.probs.argmax(dim=-1)
        else:
            actions = dist.sample()
        
        log_probs = dist.log_prob(actions)
        entropy = dist.entropy()
        
        return actions, log_probs, entropy
    
    def evaluate_actions(self, 
                        agv_state: torch.Tensor,
                        task_states: torch.Tensor,
                        global_state: torch.Tensor,
                        actions: torch.Tensor,
                        action_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        评估给定动作的概率和熵
        
        Args:
            agv_state: AGV状态
            task_states: 任务状态
            global_state: 全局状态
            actions: 动作 [batch_size]
            action_mask: 动作掩码
            
        Returns:
            log_probs: 动作的对数概率 [batch_size]
            entropy: 策略熵 [batch_size]
        """
        dist = self.get_action_distribution(agv_state, task_states, global_state, action_mask)
        
        log_probs = dist.log_prob(actions)
        entropy = dist.entropy()
        
        return log_probs, entropy
    
    def get_feature_dim(self) -> int:
        """获取特征维度"""
        return self.hidden_dims[-1]


class MultiAgentPolicyNetwork(nn.Module):
    """多智能体策略网络包装器"""
    
    def __init__(self, 
                 num_agents: int,
                 single_policy_config: Dict,
                 shared_policy: bool = True):
        """
        初始化多智能体策略网络
        
        Args:
            num_agents: 智能体数量
            single_policy_config: 单个策略网络配置
            shared_policy: 是否共享策略网络
        """
        super(MultiAgentPolicyNetwork, self).__init__()
        
        self.num_agents = num_agents
        self.shared_policy = shared_policy
        
        if shared_policy:
            # 所有智能体共享一个策略网络
            self.policy = PolicyNetwork(**single_policy_config)
        else:
            # 每个智能体有独立的策略网络
            self.policies = nn.ModuleList([
                PolicyNetwork(**single_policy_config) for _ in range(num_agents)
            ])
    
    def forward(self, 
                agv_states: torch.Tensor,
                task_states: torch.Tensor,
                global_state: torch.Tensor,
                action_masks: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        多智能体前向传播
        
        Args:
            agv_states: AGV状态 [batch_size, num_agents, agv_state_dim]
            task_states: 任务状态 [batch_size, num_tasks, task_state_dim]
            global_state: 全局状态 [batch_size, height, width]
            action_masks: 动作掩码 [batch_size, num_agents, action_dim]
            
        Returns:
            action_logits: 动作logits [batch_size, num_agents, action_dim]
            action_probs: 动作概率 [batch_size, num_agents, action_dim]
        """
        batch_size = agv_states.size(0)
        all_logits = []
        all_probs = []
        
        for i in range(self.num_agents):
            agv_state = agv_states[:, i, :]  # [batch_size, agv_state_dim]
            action_mask = action_masks[:, i, :] if action_masks is not None else None
            
            if self.shared_policy:
                logits, probs = self.policy(agv_state, task_states, global_state, action_mask)
            else:
                logits, probs = self.policies[i](agv_state, task_states, global_state, action_mask)
            
            all_logits.append(logits.unsqueeze(1))  # [batch_size, 1, action_dim]
            all_probs.append(probs.unsqueeze(1))    # [batch_size, 1, action_dim]
        
        action_logits = torch.cat(all_logits, dim=1)  # [batch_size, num_agents, action_dim]
        action_probs = torch.cat(all_probs, dim=1)    # [batch_size, num_agents, action_dim]
        
        return action_logits, action_probs
    
    def sample_actions(self, 
                      agv_states: torch.Tensor,
                      task_states: torch.Tensor,
                      global_state: torch.Tensor,
                      action_masks: Optional[torch.Tensor] = None,
                      deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        多智能体动作采样
        
        Returns:
            actions: [batch_size, num_agents]
            log_probs: [batch_size, num_agents]
            entropies: [batch_size, num_agents]
        """
        batch_size = agv_states.size(0)
        all_actions = []
        all_log_probs = []
        all_entropies = []
        
        for i in range(self.num_agents):
            agv_state = agv_states[:, i, :]
            action_mask = action_masks[:, i, :] if action_masks is not None else None
            
            if self.shared_policy:
                actions, log_probs, entropy = self.policy.sample_action(
                    agv_state, task_states, global_state, action_mask, deterministic
                )
            else:
                actions, log_probs, entropy = self.policies[i].sample_action(
                    agv_state, task_states, global_state, action_mask, deterministic
                )
            
            all_actions.append(actions.unsqueeze(1))
            all_log_probs.append(log_probs.unsqueeze(1))
            all_entropies.append(entropy.unsqueeze(1))
        
        actions = torch.cat(all_actions, dim=1)      # [batch_size, num_agents]
        log_probs = torch.cat(all_log_probs, dim=1)  # [batch_size, num_agents]
        entropies = torch.cat(all_entropies, dim=1)  # [batch_size, num_agents]
        
        return actions, log_probs, entropies
